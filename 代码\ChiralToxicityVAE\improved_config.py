"""
改进的配置文件，专门解决模式坍塌问题
"""

# 基础配置
IMPROVED_CONFIG = {
    # === 数据配置 ===
    'data_path': '../../数据/Chiral_AquaTox_scr.csv',
    'molecular_feature_dim': 10,
    'toxicity_dim': 5,
    
    # === 模型架构配置（增强容量） ===
    'latent_dim': 64,           # 从32增加到64，提高表示能力
    'hidden_dims': [128, 64],   # 从[64, 32]增加到[128, 64]
    
    # === 训练配置（解决模式坍塌） ===
    'epochs': 500,              # 增加训练轮数
    'batch_size': 32,           # 适中的批次大小
    'learning_rate': 1e-4,      # 降低学习率，更稳定训练
    'weight_decay': 1e-5,
    
    # === VAE损失权重（关键改进） ===
    'beta': 0.5,                # 降低KL权重，防止后验坍塌
    'diversity_weight': 0.2,    # 添加多样性损失
    'beta_schedule': 'cyclical', # 使用周期性beta调度
    'beta_min': 0.1,
    'beta_max': 1.0,
    'cycle_length': 100,
    
    # === 早停和调度器 ===
    'patience': 50,             # 增加耐心值
    'scheduler_factor': 0.8,
    'scheduler_patience': 20,
    
    # === 生成配置 ===
    'n_samples_per_condition': 50,  # 减少每条件样本数，专注质量
    'temperature_range': [0.8, 1.2], # 温度范围采样
    'generate_enantiomers': True,
    
    # === 简化的目标条件（避免复杂组合） ===
    'target_conditions': [
        # 单一条件生成
        [1, None, None, None, None],  # 仅FishLC50有毒
        [0, None, None, None, None],  # 仅FishLC50无毒
        [None, 1, None, None, None],  # 仅FishCT有毒
        [None, 0, None, None, None],  # 仅FishCT无毒
        [None, None, 1, None, None],  # 仅DMCT有毒
        [None, None, 0, None, None],  # 仅DMCT无毒
        [None, None, None, 1, None],  # 仅DMAT有毒
        [None, None, None, 0, None],  # 仅DMAT无毒
        [None, None, None, None, 1],  # 仅AlaAT有毒
        [None, None, None, None, 0],  # 仅AlaAT无毒
        
        # 无条件生成（重要！）
        [None, None, None, None, None], # 完全随机
    ],
    
    # === 输出配置 ===
    'output_dir': 'models',
    'save_interval': 50,
    'log_interval': 10,
    
    # === 验证和可视化 ===
    'validation_split': 0.2,
    'test_split': 0.1,
    'visualize_interval': 100,
    'n_visualization_samples': 200,
}

# 渐进式训练策略
PROGRESSIVE_TRAINING_STAGES = [
    {
        'name': 'Stage 1: 无条件生成',
        'description': '首先学习无条件生成，建立基础分布',
        'epochs': 200,
        'conditions': [[None, None, None, None, None]],  # 仅无条件
        'beta': 0.1,  # 很低的KL权重
        'diversity_weight': 0.3,  # 高多样性权重
    },
    {
        'name': 'Stage 2: 单条件生成',
        'description': '在无条件基础上添加单一条件',
        'epochs': 200,
        'conditions': [
            [None, None, None, None, None],  # 无条件
            [1, None, None, None, None],     # FishLC50有毒
            [0, None, None, None, None],     # FishLC50无毒
        ],
        'beta': 0.3,
        'diversity_weight': 0.2,
    },
    {
        'name': 'Stage 3: 多条件生成',
        'description': '逐步添加更多条件',
        'epochs': 100,
        'conditions': IMPROVED_CONFIG['target_conditions'],
        'beta': 0.5,
        'diversity_weight': 0.1,
    }
]

# 数据增强策略
DATA_AUGMENTATION_CONFIG = {
    'oversample_rare_conditions': True,  # 过采样稀有条件组合
    'undersample_common_conditions': True,  # 欠采样常见条件
    'balance_threshold': 0.1,  # 平衡阈值
    'molecular_feature_noise': 0.05,  # 添加轻微噪声
    'toxicity_label_smoothing': 0.1,  # 标签平滑
}

# 模型评估配置
EVALUATION_CONFIG = {
    'metrics': [
        'reconstruction_loss',
        'kl_divergence', 
        'diversity_score',
        'coverage_score',
        'fid_score',  # Fréchet Inception Distance
    ],
    'evaluation_interval': 50,
    'n_evaluation_samples': 1000,
    'reference_dataset_size': 500,
}

def get_cyclical_beta(epoch, cycle_length=100, beta_min=0.1, beta_max=1.0):
    """
    周期性beta调度，防止KL坍塌
    """
    import math
    cycle_position = (epoch % cycle_length) / cycle_length
    beta = beta_min + (beta_max - beta_min) * (1 + math.cos(math.pi * cycle_position)) / 2
    return beta

def get_temperature_schedule(epoch, max_epochs, temp_start=1.5, temp_end=0.8):
    """
    温度调度：从高温度（高多样性）到低温度（高质量）
    """
    progress = epoch / max_epochs
    temperature = temp_start * (1 - progress) + temp_end * progress
    return temperature

# 诊断和调试配置
DEBUG_CONFIG = {
    'save_latent_representations': True,
    'save_generation_samples': True,
    'plot_loss_curves': True,
    'plot_latent_space': True,
    'save_model_checkpoints': True,
    'verbose_logging': True,
}
