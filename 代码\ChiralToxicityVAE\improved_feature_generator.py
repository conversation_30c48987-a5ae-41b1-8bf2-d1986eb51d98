"""
基于真实数据分布的改进特征生成器
"""


def generate_realistic_molecular_features(n_samples):
    """基于真实数据分布生成分子特征"""
    import numpy as np
    
    features = []
    
    for _ in range(n_samples):
        feature_values = []
        
        # stereo_centers
        stereo_centers_val = np.random.poisson(7.049)
        stereo_centers_val = np.clip(stereo_centers_val, 1, 67)
        feature_values.append(stereo_centers_val)
        
        # MW
        MW_val = np.random.gamma(1.223, 396.939)
        MW_val = np.clip(MW_val, 90.078, 4549.452)
        feature_values.append(MW_val)
        
        # LogP
        LogP_val = np.random.normal(2.787, 4.541)
        LogP_val = np.clip(LogP_val, -22.906, 64.713)
        feature_values.append(LogP_val)
        
        # TPSA
        TPSA_val = np.random.gamma(0.497, 249.160)
        TPSA_val = np.clip(TPSA_val, 0.000, 1722.140)
        feature_values.append(TPSA_val)
        
        # HBD
        HBD_val = np.random.poisson(3.406)
        HBD_val = np.clip(HBD_val, 0, 64)
        feature_values.append(HBD_val)
        
        # HBA
        HBA_val = np.random.poisson(6.303)
        HBA_val = np.clip(HBA_val, 0, 60)
        feature_values.append(HBA_val)
        
        # RotBonds
        RotBonds_val = np.random.gamma(0.349, 21.765)
        RotBonds_val = np.clip(RotBonds_val, 0.000, 130.000)
        feature_values.append(RotBonds_val)
        
        # AromaticRings
        AromaticRings_val = np.random.poisson(1.131)
        AromaticRings_val = np.clip(AromaticRings_val, 0, 36)
        feature_values.append(AromaticRings_val)
        
        # length
        length_val = np.random.gamma(1.136, 70.863)
        length_val = np.clip(length_val, 12.000, 661.000)
        feature_values.append(length_val)
        
        # carbon_count
        carbon_count_val = np.random.gamma(1.143, 16.607)
        carbon_count_val = np.clip(carbon_count_val, 2.000, 146.000)
        feature_values.append(carbon_count_val)
        
        # nitrogen_count
        nitrogen_count_val = np.random.poisson(2.148)
        nitrogen_count_val = np.clip(nitrogen_count_val, 0, 54)
        feature_values.append(nitrogen_count_val)
        
        # oxygen_count
        oxygen_count_val = np.random.poisson(5.499)
        oxygen_count_val = np.clip(oxygen_count_val, 0, 58)
        feature_values.append(oxygen_count_val)
        
        # sulfur_count
        sulfur_count_val = np.random.poisson(0.174)
        sulfur_count_val = np.clip(sulfur_count_val, 0, 20)
        feature_values.append(sulfur_count_val)
        
        # halogen_count
        halogen_count_val = np.random.poisson(0.785)
        halogen_count_val = np.clip(halogen_count_val, 0, 50)
        feature_values.append(halogen_count_val)
        
        # ring_count
        ring_count_val = np.random.poisson(6.575)
        ring_count_val = np.clip(ring_count_val, 0, 78)
        feature_values.append(ring_count_val)
        
        # double_bonds
        double_bonds_val = np.random.poisson(3.166)
        double_bonds_val = np.clip(double_bonds_val, 0, 42)
        feature_values.append(double_bonds_val)
        
        # triple_bonds
        triple_bonds_val = np.random.poisson(0.100)
        triple_bonds_val = np.clip(triple_bonds_val, 0, 2)
        feature_values.append(triple_bonds_val)
        
        features.append(feature_values)
    
    return np.array(features)
