"""
手性分子毒性VAE主程序
"""

import torch
from torch.utils.data import DataLoader
import os
from datetime import datetime

from data import DataProcessor
from train import VAETrainer
from generate import ToxicityGenerator


# 配置参数
CONFIG = {
    # 数据处理
    'data_path': '../../数据/Chiral_AquaTox_scr.csv',
    'train_ratio': 0.8,
    'val_ratio': 0.1,
    'test_ratio': 0.1,
    
    # 模型参数
    'molecular_feature_dim': 13,
    'toxicity_dim': 5,
    'latent_dim': 32,
    'hidden_dims': [64, 32],
    
    # 训练参数
    'epochs': 500,
    'batch_size': 32,
    'learning_rate': 0.001,
    'weight_decay': 1e-5,
    'beta': 1.0,
    'scheduler_patience': 10,
    'scheduler_factor': 0.5,
    'early_stopping_patience': 20,
    'min_delta': 1e-4,
    
    # 生成参数
    'n_samples_per_condition': 300,  # 增加样本数以提高多样性
    'generate_enantiomers': True,     # 是否生成对映异构体对
    'temperature': 1.2,               # 增加采样温度以提高多样性
    'target_conditions': [
        [1, None, None, None, None],  # FishLC50 toxic
        [0, None, None, None, None],  # FishLC50 non-toxic
        [None, None, None, 1, None],  # DMAT toxic
        [None, None, None, 0, None],  # DMAT non-toxic
        [1, None, None, 1, None],     # FishLC50 and DMAT toxic
        [0, None, None, 0, None],     # FishLC50 and DMAT non-toxic
        [1, 1, 1, 1, 1],              # All toxic
        [0, 0, 0, 0, 0],              # All non-toxic
        [None, None, None, None, None], # Random
        # 添加更多条件组合以增加多样性
        [None, 1, None, None, None],  # FishCT toxic
        [None, 0, None, None, None],  # FishCT non-toxic
        [None, None, 1, None, None],  # DMCT toxic
        [None, None, 0, None, None],  # DMCT non-toxic
        [None, None, None, None, 1],  # AlaAT toxic
        [None, None, None, None, 0],  # AlaAT non-toxic
    ],
    'molecular_feature_ranges': {
        'stereo_centers': (1, 67),     # 基于真实数据范围
        'length': (12, 661),           # 基于真实数据范围
        'carbon_count': (2, 146),      # 基于真实数据范围
    },
    
    # 输出目录
    'data_output_dir': 'data',
    'model_output_dir': 'models',
    'generation_output_dir': 'results',
    
    # 运行控制
    'run_data_processing': True,
    'run_training': True,
    'run_generation': True,

}

def run_data_processing():
    processor = DataProcessor(CONFIG['data_path'])
    processor.load_data()
    output_dir = processor.save_processed_data(CONFIG['data_output_dir'])
    return True

def run_training():
    train_dataset = torch.load(os.path.join(CONFIG['data_output_dir'], 'train_dataset.pt'))
    val_dataset = torch.load(os.path.join(CONFIG['data_output_dir'], 'val_dataset.pt'))
    
    train_loader = DataLoader(train_dataset, batch_size=CONFIG['batch_size'], shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=CONFIG['batch_size'], shuffle=False)
    
    training_config = {
        'molecular_feature_dim': CONFIG['molecular_feature_dim'],
        'toxicity_dim': CONFIG['toxicity_dim'],
        'latent_dim': CONFIG['latent_dim'],
        'hidden_dims': CONFIG['hidden_dims'],
        'epochs': CONFIG['epochs'],
        'learning_rate': CONFIG['learning_rate'],
        'weight_decay': CONFIG['weight_decay'],
        'beta': CONFIG['beta'],
        'scheduler_patience': CONFIG['scheduler_patience'],
        'scheduler_factor': CONFIG['scheduler_factor'],
        'early_stopping_patience': CONFIG['early_stopping_patience'],
        'min_delta': CONFIG['min_delta'],
        'output_dir': CONFIG['model_output_dir']
    }
    
    trainer = VAETrainer(training_config)
    trainer.create_model()
    trainer.train(train_loader, val_loader)
    
    return True

def run_generation():
    generation_config = {
        'model_path': os.path.join(CONFIG['model_output_dir'], 'best_model.pth'),
        'metadata_path': os.path.join(CONFIG['data_output_dir'], 'metadata.pkl'),
        'n_samples_per_condition': CONFIG['n_samples_per_condition'],
        'generate_enantiomers': CONFIG['generate_enantiomers'],
        'target_conditions': CONFIG['target_conditions'],
        'molecular_feature_ranges': CONFIG['molecular_feature_ranges'],
        'reference_data_path': CONFIG['data_path'],  # 用于金标准验证
        'output_dir': CONFIG['generation_output_dir']
    }
    
    generator = ToxicityGenerator(generation_config)
    generator.load_model_and_metadata()
    results = generator.generate_all_conditions()
    analysis = generator.analyze_generated_data()
    output_dir = generator.save_results()
    
    return output_dir, analysis['total_samples']



def main():
    print("Chiral Toxicity VAE System")

    if CONFIG['run_data_processing']:
        print("Data Processing...")
        if not run_data_processing():
            return

    if CONFIG['run_training']:
        print("VAE Training...")
        if not run_training():
            return

    if CONFIG['run_generation']:
        print("Toxicity Generation...")
        output_dir, n_samples = run_generation()
        if output_dir:
            print(f"Completed: {n_samples} samples -> {output_dir}")



    print("Done")

if __name__ == "__main__":
    main()
