"""
模式坍塌诊断工具
专门用于检测和分析VAE的模式坍塌问题
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.metrics import pairwise_distances
import warnings
warnings.filterwarnings('ignore')

class ModeCollapseDiagnostic:
    def __init__(self, model, data_loader, device='cpu'):
        """
        模式坍塌诊断器
        
        Args:
            model: 训练好的VAE模型
            data_loader: 数据加载器
            device: 计算设备
        """
        self.model = model
        self.data_loader = data_loader
        self.device = device
        self.model.eval()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    def extract_latent_representations(self, n_samples=1000):
        """提取潜在表示"""
        print("提取潜在表示...")
        
        all_mu = []
        all_logvar = []
        all_z = []
        all_original_toxicity = []
        all_reconstructed = []
        
        count = 0
        with torch.no_grad():
            for molecular_features, toxicity_data, mask in self.data_loader:
                if count >= n_samples:
                    break
                    
                molecular_features = molecular_features.to(self.device)
                toxicity_data = toxicity_data.to(self.device)
                mask = mask.to(self.device)
                
                # 编码
                mu, logvar = self.model.encode(molecular_features, toxicity_data, mask)
                z = self.model.reparameterize(mu, logvar)
                
                # 解码
                reconstructed = self.model.decode(z, molecular_features)
                
                all_mu.append(mu.cpu())
                all_logvar.append(logvar.cpu())
                all_z.append(z.cpu())
                all_original_toxicity.append(toxicity_data.cpu())
                all_reconstructed.append(reconstructed.cpu())
                
                count += molecular_features.size(0)
        
        return {
            'mu': torch.cat(all_mu, dim=0)[:n_samples],
            'logvar': torch.cat(all_logvar, dim=0)[:n_samples],
            'z': torch.cat(all_z, dim=0)[:n_samples],
            'original': torch.cat(all_original_toxicity, dim=0)[:n_samples],
            'reconstructed': torch.cat(all_reconstructed, dim=0)[:n_samples]
        }
    
    def generate_samples(self, n_samples=1000):
        """生成新样本"""
        print("生成新样本...")
        
        # 随机采样分子特征（这里简化处理）
        # 在实际应用中，应该从真实的分子特征分布中采样
        molecular_features = torch.randn(n_samples, self.model.molecular_feature_dim).to(self.device)
        
        generated_samples = []
        generated_z = []
        
        with torch.no_grad():
            # 批次生成
            batch_size = 100
            for i in range(0, n_samples, batch_size):
                end_idx = min(i + batch_size, n_samples)
                batch_features = molecular_features[i:end_idx]
                
                # 从先验分布采样
                z = torch.randn(end_idx - i, self.model.latent_dim).to(self.device)
                
                # 解码
                generated = self.model.decode(z, batch_features)
                
                generated_samples.append(generated.cpu())
                generated_z.append(z.cpu())
        
        return {
            'generated': torch.cat(generated_samples, dim=0),
            'z': torch.cat(generated_z, dim=0)
        }
    
    def calculate_diversity_metrics(self, representations):
        """计算多样性指标"""
        print("计算多样性指标...")
        
        metrics = {}
        
        # 1. 潜在空间多样性
        mu = representations['mu'].numpy()
        z = representations['z'].numpy()
        
        # 标准差（衡量分布宽度）
        metrics['mu_std'] = np.std(mu, axis=0).mean()
        metrics['z_std'] = np.std(z, axis=0).mean()
        
        # 有效维度数（方差大于阈值的维度）
        mu_var = np.var(mu, axis=0)
        z_var = np.var(z, axis=0)
        threshold = 0.01
        metrics['mu_effective_dims'] = np.sum(mu_var > threshold)
        metrics['z_effective_dims'] = np.sum(z_var > threshold)
        
        # 2. 输出多样性
        original = representations['original'].numpy()
        reconstructed = representations['reconstructed'].numpy()
        
        # 重构多样性
        metrics['recon_std'] = np.std(reconstructed, axis=0).mean()
        metrics['original_std'] = np.std(original, axis=0).mean()
        
        # 3. 距离指标
        # 平均成对距离
        mu_distances = pairwise_distances(mu[:500])  # 限制样本数以节省计算
        metrics['mu_avg_distance'] = np.mean(mu_distances[np.triu_indices_from(mu_distances, k=1)])
        
        # 最近邻距离
        mu_min_distances = np.min(mu_distances + np.eye(mu_distances.shape[0]) * 1e6, axis=1)
        metrics['mu_min_distance'] = np.mean(mu_min_distances)
        
        return metrics
    
    def detect_mode_collapse(self, representations, generated):
        """检测模式坍塌"""
        print("检测模式坍塌...")
        
        collapse_indicators = {}
        
        # 1. 潜在空间坍塌检测
        mu = representations['mu'].numpy()
        
        # 检查是否所有样本聚集在少数几个点
        from sklearn.cluster import KMeans
        n_clusters = min(10, len(mu) // 10)
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        labels = kmeans.fit_predict(mu)
        
        # 计算最大簇的比例
        max_cluster_ratio = np.max(np.bincount(labels)) / len(labels)
        collapse_indicators['max_cluster_ratio'] = max_cluster_ratio
        collapse_indicators['is_collapsed'] = max_cluster_ratio > 0.5
        
        # 2. 输出多样性检测
        generated_toxicity = generated['generated'].numpy()
        
        # 检查生成样本的方差
        gen_var = np.var(generated_toxicity, axis=0)
        collapse_indicators['low_variance_dims'] = np.sum(gen_var < 0.01)
        collapse_indicators['avg_output_variance'] = np.mean(gen_var)
        
        # 3. 重复样本检测
        # 检查是否有大量重复或近似重复的样本
        unique_samples = np.unique(np.round(generated_toxicity, 2), axis=0)
        collapse_indicators['uniqueness_ratio'] = len(unique_samples) / len(generated_toxicity)
        
        return collapse_indicators
    
    def plot_latent_space_analysis(self, representations, save_path='latent_analysis.png'):
        """绘制潜在空间分析图"""
        print("绘制潜在空间分析...")
        
        mu = representations['mu'].numpy()
        z = representations['z'].numpy()
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 潜在空间分布 (前两个维度)
        axes[0, 0].scatter(mu[:, 0], mu[:, 1], alpha=0.6, s=20)
        axes[0, 0].set_title('潜在空间分布 (μ, 前两维)')
        axes[0, 0].set_xlabel('Latent Dim 1')
        axes[0, 0].set_ylabel('Latent Dim 2')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 采样空间分布
        axes[0, 1].scatter(z[:, 0], z[:, 1], alpha=0.6, s=20, color='orange')
        axes[0, 1].set_title('采样空间分布 (z, 前两维)')
        axes[0, 1].set_xlabel('Latent Dim 1')
        axes[0, 1].set_ylabel('Latent Dim 2')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 维度方差分析
        mu_var = np.var(mu, axis=0)
        axes[0, 2].bar(range(len(mu_var)), mu_var)
        axes[0, 2].set_title('各维度方差 (μ)')
        axes[0, 2].set_xlabel('Latent Dimension')
        axes[0, 2].set_ylabel('Variance')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. t-SNE可视化
        if len(mu) > 50:
            tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(mu)//4))
            mu_tsne = tsne.fit_transform(mu[:1000])  # 限制样本数
            axes[1, 0].scatter(mu_tsne[:, 0], mu_tsne[:, 1], alpha=0.6, s=20)
            axes[1, 0].set_title('t-SNE 潜在空间可视化')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 重构质量分析
        original = representations['original'].numpy()
        reconstructed = representations['reconstructed'].numpy()
        
        # 计算重构误差
        recon_error = np.mean((original - reconstructed) ** 2, axis=1)
        axes[1, 1].hist(recon_error, bins=50, alpha=0.7)
        axes[1, 1].set_title('重构误差分布')
        axes[1, 1].set_xlabel('MSE')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. 潜在空间密度
        axes[1, 2].hist2d(mu[:, 0], mu[:, 1], bins=30, alpha=0.7)
        axes[1, 2].set_title('潜在空间密度图')
        axes[1, 2].set_xlabel('Latent Dim 1')
        axes[1, 2].set_ylabel('Latent Dim 2')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_output_diversity_analysis(self, representations, generated, save_path='output_diversity.png'):
        """绘制输出多样性分析"""
        print("绘制输出多样性分析...")
        
        original = representations['original'].numpy()
        reconstructed = representations['reconstructed'].numpy()
        generated_samples = generated['generated'].numpy()
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        toxicity_names = ['FishLC50', 'FishCT', 'DMCT', 'DMAT', 'AlaAT']
        
        # 对比原始、重构和生成数据的分布
        for i, name in enumerate(toxicity_names):
            if i < 5:  # 只显示前5个
                row = i // 3
                col = i % 3
                
                axes[row, col].hist(original[:, i], bins=30, alpha=0.5, label='原始', density=True)
                axes[row, col].hist(reconstructed[:, i], bins=30, alpha=0.5, label='重构', density=True)
                axes[row, col].hist(generated_samples[:, i], bins=30, alpha=0.5, label='生成', density=True)
                
                axes[row, col].set_title(f'{name} 分布对比')
                axes[row, col].set_xlabel('Toxicity Value')
                axes[row, col].set_ylabel('Density')
                axes[row, col].legend()
                axes[row, col].grid(True, alpha=0.3)
        
        # 最后一个子图：整体统计
        axes[1, 2].bar(['原始', '重构', '生成'], 
                      [np.std(original), np.std(reconstructed), np.std(generated_samples)],
                      alpha=0.7)
        axes[1, 2].set_title('整体标准差对比')
        axes[1, 2].set_ylabel('Standard Deviation')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_diagnostic_report(self, save_path='mode_collapse_report.txt'):
        """生成诊断报告"""
        print("生成诊断报告...")
        
        # 提取数据
        representations = self.extract_latent_representations(1000)
        generated = self.generate_samples(1000)
        
        # 计算指标
        diversity_metrics = self.calculate_diversity_metrics(representations)
        collapse_indicators = self.detect_mode_collapse(representations, generated)
        
        # 生成可视化
        self.plot_latent_space_analysis(representations, 'latent_space_analysis.png')
        self.plot_output_diversity_analysis(representations, generated, 'output_diversity_analysis.png')
        
        # 写入报告
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write("=== VAE模式坍塌诊断报告 ===\n\n")
            
            f.write("1. 多样性指标:\n")
            for key, value in diversity_metrics.items():
                f.write(f"   {key}: {value:.4f}\n")
            
            f.write("\n2. 模式坍塌指标:\n")
            for key, value in collapse_indicators.items():
                f.write(f"   {key}: {value}\n")
            
            f.write("\n3. 诊断结论:\n")
            if collapse_indicators['is_collapsed']:
                f.write("   ⚠️  检测到严重的模式坍塌!\n")
                f.write("   建议:\n")
                f.write("   - 降低KL损失权重 (beta)\n")
                f.write("   - 增加多样性损失项\n")
                f.write("   - 增加模型容量\n")
                f.write("   - 使用周期性beta调度\n")
            else:
                f.write("   ✅ 未检测到严重的模式坍塌\n")
                
            if diversity_metrics['mu_effective_dims'] < self.model.latent_dim * 0.5:
                f.write("   ⚠️  潜在空间利用率低\n")
                f.write("   建议增加模型训练时间或调整架构\n")
                
            if collapse_indicators['uniqueness_ratio'] < 0.8:
                f.write("   ⚠️  生成样本重复率高\n")
                f.write("   建议增加生成时的温度参数\n")
        
        print(f"诊断报告已保存到: {save_path}")
        return diversity_metrics, collapse_indicators

def main():
    """主函数 - 运行诊断"""
    print("=== VAE模式坍塌诊断工具 ===")
    
    # 这里需要加载训练好的模型和数据
    # model = torch.load('models/best_model.pth')
    # data_loader = ...
    
    print("请确保已有训练好的模型，然后调用诊断器:")
    print("diagnostic = ModeCollapseDiagnostic(model, data_loader)")
    print("diagnostic.generate_diagnostic_report()")

if __name__ == "__main__":
    main()
