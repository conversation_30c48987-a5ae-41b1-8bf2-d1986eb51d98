"""
数据处理模块
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
import pickle
import os

class ToxicityDataset(Dataset):
    def __init__(self, molecular_features, toxicity_data, masks):
        self.molecular_features = torch.FloatTensor(molecular_features)
        self.toxicity_data = torch.FloatTensor(toxicity_data)
        self.masks = torch.FloatTensor(masks)
    
    def __len__(self):
        return len(self.molecular_features)
    
    def __getitem__(self, idx):
        return (
            self.molecular_features[idx],
            self.toxicity_data[idx],
            self.masks[idx]
        )

class DataProcessor:
    def __init__(self, data_path='../数据/Chiral_AquaTox_scr.csv'):
        self.data_path = data_path
        self.toxicity_endpoints = ['FishLC50', 'FishCT', 'DMCT', 'DMAT', 'AlaAT']
        self.scaler = StandardScaler()
        
    def load_data(self):
        self.df = pd.read_csv(self.data_path)
        return self.df
    
    def extract_molecular_features(self):
        features = []

        for smiles in self.df['smiles']:
            features.append(self._extract_features_from_smiles(smiles))

        features_array = np.array(features)
        features_scaled = self.scaler.fit_transform(features_array)

        self.feature_names = ['stereo_centers', 'stereo_r', 'stereo_s', 'length', 'carbon_count',
                             'nitrogen_count', 'oxygen_count', 'sulfur_count', 'halogen_count',
                             'ring_count', 'double_bond_count', 'triple_bond_count', 'branch_count']
        return features_scaled

    @staticmethod
    def _extract_features_from_smiles(smiles):
        return [
            smiles.count('@'),
            smiles.count('@@'),
            smiles.count('@') - smiles.count('@@'),
            len(smiles),
            smiles.count('C'),
            smiles.count('N'),
            smiles.count('O'),
            smiles.count('S'),
            smiles.count('F') + smiles.count('Cl') + smiles.count('Br') + smiles.count('I'),
            smiles.count('1') + smiles.count('2') + smiles.count('3'),
            smiles.count('='),
            smiles.count('#'),
            smiles.count('('),
        ]

    @staticmethod
    def generate_random_molecular_features(n_samples, feature_ranges, scaler):
        """基于真实数据分布生成分子特征"""
        features = []

        for _ in range(n_samples):
            # 基于真实数据统计的分布参数生成特征

            # 手性中心数：泊松分布 (λ=7.05, 范围1-67)
            stereo_centers = max(1, min(67, np.random.poisson(7.05)))

            # 分子复杂度：基于分子量的伽马分布估算
            # MW: 伽马分布 (shape=1.22, scale=396.94, 范围90-4549)
            molecular_weight = np.clip(np.random.gamma(1.22, 396.94), 90, 4549)
            complexity = int(np.log10(molecular_weight))  # 基于分子量的复杂度

            # 分子大小：基于LogP的正态分布估算
            # LogP: 正态分布 (mean=2.79, std=4.54, 范围-22.9-64.7)
            logp = np.clip(np.random.normal(2.79, 4.54), -22.9, 64.7)
            size = max(1, min(10, int((logp + 23) / 9)))  # 映射到1-10范围

            # SMILES长度：伽马分布 (shape=1.14, scale=70.86, 范围12-661)
            length = max(12, min(661, int(np.random.gamma(1.14, 70.86))))

            # 碳原子数：伽马分布 (shape=1.14, scale=16.61, 范围2-146)
            carbon_count = max(2, min(146, int(np.random.gamma(1.14, 16.61))))

            # 氮原子数：泊松分布 (λ=2.15, 范围0-54)
            nitrogen_count = min(54, np.random.poisson(2.15))

            # 氧原子数：泊松分布 (λ=5.50, 范围0-58)
            oxygen_count = min(58, np.random.poisson(5.50))

            # 硫原子数：泊松分布 (λ=0.17, 范围0-20)
            sulfur_count = min(20, np.random.poisson(0.17))

            # 卤素原子数：泊松分布 (λ=0.79, 范围0-50)
            halogen_count = min(50, np.random.poisson(0.79))

            # 磷原子数：极少，设为0或基于硫原子数的比例
            phosphorus_count = 0 if sulfur_count == 0 else np.random.choice([0, 1], p=[0.95, 0.05])

            # 环数：泊松分布 (λ=6.57, 范围0-78)
            ring_count = min(78, np.random.poisson(6.57))

            # 双键数：泊松分布 (λ=3.17, 范围0-42)
            double_bonds = min(42, np.random.poisson(3.17))

            # 三键数：泊松分布 (λ=0.06, 范围0-2)
            triple_bonds = min(2, np.random.poisson(0.06))

            feature_values = [
                stereo_centers,
                complexity,
                size,
                length,
                carbon_count,
                nitrogen_count,
                oxygen_count,
                sulfur_count,
                halogen_count,
                phosphorus_count,
                ring_count,
                double_bonds,
                triple_bonds,
            ]
            features.append(feature_values)

        features_array = np.array(features)
        return scaler.transform(features_array)
    
    def process_toxicity_data(self):
        toxicity_matrix = []
        mask_matrix = []
        
        for _, row in self.df.iterrows():
            toxicity_row = []
            mask_row = []
            
            for endpoint in self.toxicity_endpoints:
                if endpoint in self.df.columns:
                    value = row[endpoint]
                    if pd.notna(value):
                        toxicity_row.append(float(value))
                        mask_row.append(1.0)
                    else:
                        toxicity_row.append(0.0)
                        mask_row.append(0.0)
                else:
                    toxicity_row.append(0.0)
                    mask_row.append(0.0)
            
            toxicity_matrix.append(toxicity_row)
            mask_matrix.append(mask_row)
        
        return np.array(toxicity_matrix), np.array(mask_matrix)
    
    def create_datasets(self, train_ratio=0.8, val_ratio=0.1, random_seed=42):
        molecular_features = self.extract_molecular_features()
        toxicity_data, masks = self.process_toxicity_data()
        
        np.random.seed(random_seed)
        n_samples = len(molecular_features)
        indices = np.random.permutation(n_samples)
        
        n_train = int(n_samples * train_ratio)
        n_val = int(n_samples * val_ratio)
        
        train_indices = indices[:n_train]
        val_indices = indices[n_train:n_train + n_val]
        test_indices = indices[n_train + n_val:]
        
        train_dataset = ToxicityDataset(
            molecular_features[train_indices],
            toxicity_data[train_indices],
            masks[train_indices]
        )
        
        val_dataset = ToxicityDataset(
            molecular_features[val_indices],
            toxicity_data[val_indices],
            masks[val_indices]
        )
        
        test_dataset = ToxicityDataset(
            molecular_features[test_indices],
            toxicity_data[test_indices],
            masks[test_indices]
        )
        
        return train_dataset, val_dataset, test_dataset
    
    def save_processed_data(self, output_dir='data'):
        os.makedirs(output_dir, exist_ok=True)
        
        train_dataset, val_dataset, test_dataset = self.create_datasets()
        
        torch.save(train_dataset, os.path.join(output_dir, 'train_dataset.pt'))
        torch.save(val_dataset, os.path.join(output_dir, 'val_dataset.pt'))
        torch.save(test_dataset, os.path.join(output_dir, 'test_dataset.pt'))
        
        metadata = {
            'feature_names': self.feature_names,
            'toxicity_endpoints': self.toxicity_endpoints,
            'n_molecular_features': len(self.feature_names),
            'n_toxicity_endpoints': len(self.toxicity_endpoints),
            'scaler': self.scaler
        }
        
        with open(os.path.join(output_dir, 'metadata.pkl'), 'wb') as f:
            pickle.dump(metadata, f)
        
        return output_dir
