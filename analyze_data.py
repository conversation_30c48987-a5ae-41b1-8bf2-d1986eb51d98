#!/usr/bin/env python3
"""
分析原始数据分布，为改进生成策略提供依据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_original_data():
    """分析原始数据的分布特征"""
    # 读取数据
    df = pd.read_csv('数据/Chiral_AquaTox_scr.csv')
    print(f'数据集大小: {len(df)}')
    
    # 分析毒性终点分布
    print(f'\n毒性终点分布:')
    toxicity_stats = {}
    for col in ['FishLC50', 'FishCT', 'DMCT', 'DMAT', 'AlaAT']:
        valid_count = df[col].notna().sum()
        if valid_count > 0:
            toxic_count = (df[col] == 1).sum()
            toxicity_stats[col] = {
                'valid_count': valid_count,
                'toxic_count': toxic_count,
                'toxic_rate': toxic_count/valid_count
            }
            print(f'  {col}: {valid_count} 个有效值, {toxic_count} 个有毒 ({toxic_count/valid_count*100:.1f}%)')
    
    # 分析分子特征分布（基于SMILES字符串）
    features = []
    for smiles in df['smiles']:
        try:
            features.append({
                'stereo_centers': smiles.count('@'),
                'stereo_r': smiles.count('@@'),
                'stereo_s': smiles.count('@') - smiles.count('@@'),
                'length': len(smiles),
                'carbon_count': smiles.count('C') + smiles.count('c'),
                'nitrogen_count': smiles.count('N') + smiles.count('n'),
                'oxygen_count': smiles.count('O') + smiles.count('o'),
                'sulfur_count': smiles.count('S') + smiles.count('s'),
                'halogen_count': smiles.count('Cl') + smiles.count('Br') + smiles.count('F') + smiles.count('I'),
                'ring_count': smiles.count('1') + smiles.count('2') + smiles.count('3'),
                'double_bond_count': smiles.count('='),
                'triple_bond_count': smiles.count('#'),
                'branch_count': smiles.count('(') + smiles.count('['),
            })
        except:
            continue
    
    features_df = pd.DataFrame(features)
    print(f'\n分子特征统计:')
    print(features_df.describe())
    
    # 保存统计结果
    with open('data_analysis_results.txt', 'w', encoding='utf-8') as f:
        f.write(f'数据集大小: {len(df)}\n\n')
        f.write('毒性终点分布:\n')
        for col, stats in toxicity_stats.items():
            f.write(f'  {col}: {stats["valid_count"]} 个有效值, {stats["toxic_count"]} 个有毒 ({stats["toxic_rate"]*100:.1f}%)\n')
        f.write('\n分子特征统计:\n')
        f.write(features_df.describe().to_string())
    
    return features_df, toxicity_stats

def plot_feature_distributions(features_df):
    """绘制特征分布图"""
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    axes = axes.flatten()
    
    feature_names = ['stereo_centers', 'length', 'carbon_count', 'nitrogen_count', 
                    'oxygen_count', 'sulfur_count', 'halogen_count', 'ring_count',
                    'double_bond_count', 'triple_bond_count', 'branch_count']
    
    for i, feature in enumerate(feature_names):
        if i < len(axes):
            axes[i].hist(features_df[feature], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            axes[i].set_title(f'{feature} 分布')
            axes[i].set_xlabel(feature)
            axes[i].set_ylabel('频次')
            axes[i].grid(True, alpha=0.3)
    
    # 隐藏多余的子图
    for i in range(len(feature_names), len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('original_data_feature_distributions.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

if __name__ == "__main__":
    print("=== 原始数据分析 ===")
    features_df, toxicity_stats = analyze_original_data()
    plot_feature_distributions(features_df)
    print("\n分析完成！结果已保存到 data_analysis_results.txt 和 original_data_feature_distributions.png")
