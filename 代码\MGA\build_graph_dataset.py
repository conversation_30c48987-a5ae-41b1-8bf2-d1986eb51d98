# generate dataset
from utils import build_dataset
args={}
args['input_csv'] = 'data/Chiral_AquaTox_scr.csv'
args['output_bin'] = 'data/Chiral_AquaTox_scr.bin'
args['output_csv'] = 'data/Chiral_AquaTox_scr_group.csv'

build_dataset.built_data_and_save_for_splited(
        origin_path=args['input_csv'],
        save_path=args['output_bin'],
        group_path=args['output_csv'],
        task_list_selected=None
         )

# args['pre_input_csv'] = 'prediction/Pre12chemical.csv'
# args['pre_output_bin'] = 'prediction/Pre12chemical.bin'
# build_dataset.build_and_save_predicted_data(
#         origin_path=args['pre_input_csv'],
#         save_path=args['pre_output_bin'],
#         task_list=['pNOEC'])





