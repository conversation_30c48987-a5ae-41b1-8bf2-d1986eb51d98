"""
VAE训练模块
"""

import torch
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
import os
import pickle
import time

from model import ChiralToxicityVAE, vae_loss_function_legacy

class VAETrainer:
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        os.makedirs(config['output_dir'], exist_ok=True)
        
        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        
    def create_model(self):
        self.model = ChiralToxicityVAE(
            molecular_feature_dim=self.config['molecular_feature_dim'],
            toxicity_dim=self.config['toxicity_dim'],
            latent_dim=self.config['latent_dim'],
            hidden_dims=self.config['hidden_dims']
        ).to(self.device)
        
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay']
        )
        
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=self.config['scheduler_factor'],
            patience=self.config['scheduler_patience']
        )
        
    def train_epoch(self, train_loader):
        self.model.train()
        epoch_loss = 0
        
        for molecular_features, toxicity_data, masks in train_loader:
            molecular_features = molecular_features.to(self.device)
            toxicity_data = toxicity_data.to(self.device)
            masks = masks.to(self.device)
            
            recon_toxicity, mu, logvar = self.model(molecular_features, toxicity_data, masks)
            
            total_loss, recon_loss, kl_loss = vae_loss_function_legacy(
                recon_toxicity, toxicity_data, masks, mu, logvar, beta=self.config['beta']
            )
            
            self.optimizer.zero_grad()
            total_loss.backward()
            self.optimizer.step()
            
            epoch_loss += total_loss.item()
        
        return epoch_loss / len(train_loader)
    
    def validate_epoch(self, val_loader):
        self.model.eval()
        epoch_loss = 0
        
        with torch.no_grad():
            for molecular_features, toxicity_data, masks in val_loader:
                molecular_features = molecular_features.to(self.device)
                toxicity_data = toxicity_data.to(self.device)
                masks = masks.to(self.device)
                
                recon_toxicity, mu, logvar = self.model(molecular_features, toxicity_data, masks)
                
                total_loss, recon_loss, kl_loss = vae_loss_function_legacy(
                    recon_toxicity, toxicity_data, masks, mu, logvar, beta=self.config['beta']
                )
                
                epoch_loss += total_loss.item()
        
        return epoch_loss / len(val_loader)
    
    def train(self, train_loader, val_loader):
        for epoch in range(self.config['epochs']):
            train_loss = self.train_epoch(train_loader)
            val_loss = self.validate_epoch(val_loader)
            
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            
            self.scheduler.step(val_loss)
            
            if (epoch + 1) % 50 == 0:
                print(f'Epoch {epoch+1}: Train {train_loss:.4f}, Val {val_loss:.4f}')
            
            if val_loss < self.best_val_loss - self.config['min_delta']:
                self.best_val_loss = val_loss
                self.patience_counter = 0
                self.save_model('best_model.pth', epoch, val_loss)
            else:
                self.patience_counter += 1
            
            if self.patience_counter >= self.config['early_stopping_patience']:
                break
        
        self.save_model('final_model.pth', epoch, val_loss)
        self.save_training_history()
        self.plot_training_curves()
        
    def save_model(self, filename, epoch, val_loss):
        filepath = os.path.join(self.config['output_dir'], filename)
        torch.save({
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'val_loss': val_loss,
            'config': self.config
        }, filepath)
    
    def save_training_history(self):
        history = {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'config': self.config
        }
        
        filepath = os.path.join(self.config['output_dir'], 'training_history.pkl')
        with open(filepath, 'wb') as f:
            pickle.dump(history, f)
    
    def plot_training_curves(self):
        plt.figure(figsize=(10, 6))
        plt.plot(self.train_losses, label='Train Loss')
        plt.plot(self.val_losses, label='Validation Loss')
        plt.title('Training Progress')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(self.config['output_dir'], 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.close()
