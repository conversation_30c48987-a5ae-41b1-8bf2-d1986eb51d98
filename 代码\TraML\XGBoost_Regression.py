# ==============================================================================
# Part 0: All Imports and Setup (添加XGBoost导入)
# ==============================================================================
from rdkit import RDLogger
from rdkit import Chem
from rdkit.Chem import AllChem, MACCSkeys
from rdkit.Avalon import pyAvalonTools
from skfp.fingerprints import ECFPFingerprint, PubChemFingerprint
from tqdm import tqdm

import pandas as pd
import numpy as np
import ast
import os
import matplotlib.pyplot as plt
import xgboost as xgb  # 添加XGBoost库
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    mean_absolute_percentage_error
)
from sklearn.model_selection import GridSearchCV, cross_val_score, KFold
import joblib

# Suppress RDKit warnings
lg = RDLogger.logger()
lg.setLevel(RDLogger.CRITICAL)

# ==============================================================================
# Part 1: Data Splitting Function (保持不变)
# ==============================================================================
def split_dataset_4_to_1(df, train_ratio=0.8, external_ratio=0.2, random_state=42):
    """Splits data into training set (4) and external validation set (1) with 4:1 ratio."""
    if abs(train_ratio + external_ratio - 1.0) > 1e-6:
        raise ValueError("The sum of ratios must be 1.0")

    np.random.seed(random_state)
    choices = ['train', 'external']
    probabilities = [train_ratio, external_ratio]

    df_copy = df.copy()
    df_copy['set'] = np.random.choice(choices, size=len(df_copy), p=probabilities)

    print("Data successfully split into sets (4:1 ratio):")
    print(df_copy['set'].value_counts())
    print("Proportions:")
    print(df_copy['set'].value_counts(normalize=True))
    return df_copy

# ==============================================================================
# Part 2: Fingerprint Generation Functions (保持不变)
# ==============================================================================
def get_count_based_morgan_fp(mol, radius=2, nBits=2048):
    """Generates count-based Morgan fingerprints by manual hashing."""
    if mol is None:
        return [0] * nBits
    fp_sparse = AllChem.GetMorganFingerprint(mol, radius=radius, useCounts=True)
    nonzero_dict = fp_sparse.GetNonzeroElements()
    count_array = np.zeros(nBits, dtype=int)
    for feature_id, count_value in nonzero_dict.items():
        idx = feature_id % nBits
        count_array[idx] += count_value
    return count_array.tolist()

def generate_fingerprints_for_smiles(smiles):
    """Generates all required fingerprints for a single SMILES string."""
    molecule = Chem.MolFromSmiles(smiles)
    results = {}

    if not molecule:
        return {
            'ECFP4': [], 'MACCS': [], 'Morgan': [],
            'CMorgan': [], 'Avalon': [], 'PubChem': []
        }

    # ECFP (radius=2, 2048 bits)
    results['ECFP4'] = list(AllChem.GetMorganFingerprintAsBitVect(molecule, 2, nBits=2048))
    # MACCS keys (167 bits)
    results['MACCS'] = list(MACCSkeys.GenMACCSKeys(molecule))
    # Morgan (radius=1, 2048 bits)
    results['Morgan'] = list(AllChem.GetMorganFingerprintAsBitVect(molecule, 1, nBits=2048))
    # Count-based Morgan (radius=2, 2048 bits)
    results['CMorgan'] = get_count_based_morgan_fp(molecule, radius=2, nBits=2048)
    # Avalon (2048 bits)
    results['Avalon'] = list(pyAvalonTools.GetAvalonFP(molecule, nBits=2048))
    # PubChem (881 bits, skfp default)
    pubchem_fp_obj = PubChemFingerprint(n_jobs=-1)
    results['PubChem'] = pubchem_fp_obj.transform([smiles])[0].tolist()

    return results

def calculate_fingerprints_for_modeling(df, fingerprint_type='ECFP4'):
    """Calculates specified fingerprint for modeling without storing in DataFrame."""
    print(f"Calculating {fingerprint_type} fingerprints for modeling...")

    all_descriptors = []
    for smiles in tqdm(df['smiles'], desc=f'Generating {fingerprint_type} Fingerprints'):
        all_descriptors.append(generate_fingerprints_for_smiles(smiles))

    # For fingerprint types
    fp_list = [d[fingerprint_type] for d in all_descriptors]
    print(f"Calculated {fingerprint_type} fingerprints: {len(fp_list[0]) if fp_list and fp_list[0] else 0} bits")
    return fp_list

# ==============================================================================
# Part 3: ML Preprocessing and Evaluation Functions (修改为回归)
# ==============================================================================

def preprocess_fingerprint_data(data, fingerprint_column):
    """Preprocesses fingerprint data only."""
    data = data.copy()

    # Process fingerprint
    if fingerprint_column:
        # If the fingerprint is stored as string, convert to list
        if isinstance(data[fingerprint_column].iloc[0], str):
            data[fingerprint_column] = data[fingerprint_column].apply(ast.literal_eval)
        
        # Ensure all fingerprint vectors have same length
        max_length = max(data[fingerprint_column].apply(len))
        data[fingerprint_column] = data[fingerprint_column].apply(lambda x: x + [0] * (max_length - len(x)))
        
        # Create fingerprint matrix
        X_fp = np.array(data[fingerprint_column].tolist())
    else:
        max_length = 0
        X_fp = np.empty((len(data), 0))

    print(f"Final feature matrix shape: {X_fp.shape}")
    return X_fp, max_length

def preprocess_test_fingerprint_data(data, fingerprint_column, max_length):
    """Preprocesses test data using artifacts from training."""
    data = data.copy()

    # Process fingerprint
    if fingerprint_column:
        # Convert string to list if needed
        if isinstance(data[fingerprint_column].iloc[0], str):
            data[fingerprint_column] = data[fingerprint_column].apply(ast.literal_eval)
        
        # Pad fingerprint vectors to same length
        data[fingerprint_column] = data[fingerprint_column].apply(lambda x: x + [0] * (max_length - len(x)))
        X_fp = np.array(data[fingerprint_column].tolist())
    else:
        X_fp = np.empty((len(data), 0))

    return X_fp

def evaluate_regression(y_true, y_pred, set_name, output_dir):
    """Evaluates regression model and saves plots."""
    print(f"\n===== {set_name} Evaluation Report =====")

    # Calculate metrics
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    # Calculate MAPE, handling division by zero
    mape = mean_absolute_percentage_error(y_true, y_pred) * 100
    
    # Print metrics
    print(f"Mean Squared Error (MSE): {mse:.4f}")
    print(f"Root Mean Squared Error (RMSE): {rmse:.4f}")
    print(f"Mean Absolute Error (MAE): {mae:.4f}")
    print(f"R2 Score: {r2:.4f}")
    print(f"Mean Absolute Percentage Error (MAPE): {mape:.2f}%")

    # Create plots directory if needed
    plot_dir = os.path.join(output_dir, "plots")
    os.makedirs(plot_dir, exist_ok=True)

    # Scatter plot: Predicted vs Actual
    plt.figure(figsize=(8, 6))
    plt.scatter(y_true, y_pred, alpha=0.6)
    plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
    plt.xlabel('Actual Values')
    plt.ylabel('Predicted Values')
    plt.title(f'{set_name} - Predicted vs Actual')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(plot_dir, f'{set_name}_predicted_vs_actual_{fp_column}.png'))
    plt.close()

    # Residuals plot
    residuals = y_pred - y_true
    plt.figure(figsize=(8, 6))
    plt.scatter(y_pred, residuals, alpha=0.6)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.title(f'{set_name} - Residuals Plot')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(plot_dir, f'{set_name}_residuals_{fp_column}.png'))
    plt.close()

    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2_Score': r2,
        'MAPE': mape
    }

def extract_cv_results_for_xgb(grid_search, cv_folds=10, output_dir='.'):
    """Extracts cross-validation metrics for XGBoost model from GridSearchCV"""
    print(f"\n===== {cv_folds}-Fold Cross Validation Results for XGBoost =====")

    best_idx = grid_search.best_index_
    cv_results = grid_search.cv_results_
    
    # Create plots directory
    plot_dir = os.path.join(output_dir, "plots")
    os.makedirs(plot_dir, exist_ok=True)

    # Extract scores for the best model
    scores = []
    for i in range(cv_folds):
        score_key = f'split{i}_test_score'
        if score_key in cv_results:
            scores.append(cv_results[score_key][best_idx])

    scores = np.array(scores)
    mean_score = np.mean(scores)
    std_score = np.std(scores)
    
    # Print results
    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Best CV score: {grid_search.best_score_:.4f}")
    print(f"Mean CV score for best params: {mean_score:.4f} ± {std_score:.4f}")

    # CV scores visualization
    plt.figure(figsize=(10, 6))
    plt.plot(range(1, cv_folds+1), scores, 'o-', color='blue', label='CV scores')
    plt.axhline(mean_score, color='red', linestyle='--', label=f'Mean = {mean_score:.4f}')
    plt.fill_between(range(1, cv_folds+1), mean_score-std_score, mean_score+std_score, 
                     color='gray', alpha=0.2, label=f'±1 std = {std_score:.4f}')
    plt.title(f'{cv_folds}-Fold Cross Validation Scores\n(Best Model Parameters)')
    plt.xlabel('Fold #')
    plt.ylabel('Score (R2)')
    plt.xticks(range(1, cv_folds+1))
    plt.legend(loc='best')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(plot_dir, f'xgb_cv_scores_{fp_column}.png'))
    plt.close()

    return {
        'CV_Mean_Score': mean_score,
        'CV_Std_Score': std_score,
        'CV_Scores': scores.tolist(),
        'Best_Parameters': grid_search.best_params_
    }

# ==============================================================================
# Part 4: Main Execution Block - XGBoost Regression
# ==============================================================================

if __name__ == '__main__':

    # Configuration
    input_file = '../数据/fish_LC50_fp.xlsx'  # 使用预处理好的文件，包含分子指纹
    fp_column = 'PubChem'                       # Fingerprint type: ECFP, MACCS, Morgan, CMorgan, Avalon, PubChem
    label_column = 'fish_LC50'               # Target variable (regression target)
    output_dir = 'result/XGBoost_Regression' # Output directory

    # 并行配置 - 根据您的内存情况调整
    # 内存 < 16GB: gridsearch_n_jobs = 1
    # 内存 16-32GB: gridsearch_n_jobs = 2
    # 内存 > 32GB: gridsearch_n_jobs = 4
    gridsearch_n_jobs = 2  # 您可以根据内存情况调整这个值

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    print(f"Output directory: {os.path.abspath(output_dir)}")

    # Data Loading and Preprocessing
    print("\n--- Starting XGBoost Regression Pipeline ---")

    # Step 1: Load and preprocess data
    print("\nSTEP 1: Loading and preprocessing data...")
    try:
        data = pd.read_excel(input_file)
        print(f"Data loaded successfully. Shape: {data.shape}")
        print("Target variable statistics:")
        print(data[label_column].describe())

        # Step 2: Split data
        print("\nSTEP 2: Splitting data into training (80%) and external validation (20%) sets...")
        df_with_set = split_dataset_4_to_1(data, train_ratio=0.8, external_ratio=0.2)

        # Step 3: Read fingerprints
        print(f"\nSTEP 3: Reading {fp_column} fingerprints from preprocessed file...")
        # 直接从预处理文件中读取分子指纹，无需计算
        print(f"Available fingerprint columns: {[col for col in data.columns if col in ['ECFP', 'MACCS', 'Morgan', 'CMorgan', 'Avalon', 'PubChem']]}")
        if fp_column not in data.columns:
            raise ValueError(f"Fingerprint column '{fp_column}' not found in the data file. Available columns: {list(data.columns)}")

    except Exception as e:
        print(f"\nERROR: Data processing failed - {str(e)}")
        exit()

    # Split data into training and external validation sets
    train_data = df_with_set[df_with_set['set'] == 'train'].copy()
    external_data = df_with_set[df_with_set['set'] == 'external'].copy()

    # 分子指纹已经在原始数据中，无需额外处理
    print(f"Using fingerprint column: {fp_column}")
    print(f"Fingerprint data type: {type(train_data[fp_column].iloc[0])}")

    # 为完整数据集创建副本（用于最终预测）
    all_data_temp = df_with_set.copy()

    print(f"\nTraining set size: {len(train_data):,}")
    print(f"External validation set size: {len(external_data):,}")

    # Preprocess data
    print("\nSTEP 4: Preprocessing features...")
    try:
        X_train, max_length = preprocess_fingerprint_data(train_data, fp_column)

        # Print feature information
        print(f"\nFeature summary:")
        print(f"- Fingerprint features: {max_length} dimensions")
        print(f"- Total features: {X_train.shape[1]}")

        # Preprocess external validation data
        X_external = preprocess_test_fingerprint_data(external_data, fp_column, max_length)

    except Exception as e:
        print(f"\nERROR: Feature preprocessing failed - {str(e)}")
        exit()

    # Extract targets
    y_train = train_data[label_column].values
    y_external = external_data[label_column].values

    # ===== XGBOOST MODEL TRAINING =====
    print("\n--- XGBoost Model Training ---")
    print(f"Training XGBoost regressor on {X_train.shape[0]} samples with {X_train.shape[1]} features...")

    # Initialize model
    xgb_regressor = xgb.XGBRegressor(
        random_state=42,
        n_jobs=-1,                 # 模型内部并行是安全的，可以加速单个模型训练
        verbosity=0,               # 减少输出信息
        tree_method='hist'         # 使用histogram方法，适合高维数据
    )

    # Hyperparameter grid for GridSearch - 针对XGBoost优化
    param_grid = {
        'n_estimators': [100, 200],            # Number of boosting rounds
        'max_depth': [3, 6],                  # Tree depth
        'learning_rate': [0.001, 0.01, 0.1],         # Learning rate
        'min_child_weight': [1, 3],           # Minimum sum of instance weight
        'subsample': [0.8, 1.0],              # Row sampling
        'colsample_bytree': [0.8, 1.0],       # Column sampling
        'reg_alpha': [0, 0.4],                # L1 regularization
        'reg_lambda': [1, 1.5]                # L2 regularization
    }

    # 计算参数组合数量
    total_combinations = 2**8  # 256种组合
    print(f"参数组合数量: {total_combinations}")
    print(f"预计总训练次数: {total_combinations * 10} (10折交叉验证)")
    print(f"训练数据集大小: {X_train.shape}")

    # 优化的10折交叉验证配置
    print("\n开始10折交叉验证网格搜索...")
    # 注意：如果内存充足(32GB+)，可以将n_jobs改为2或4来加速
    # 如果出现内存错误，请改回n_jobs=1
    grid_search = GridSearchCV(
        estimator=xgb_regressor,
        param_grid=param_grid,
        cv=10,                        # 10-fold cross-validation
        scoring='r2',                 # Optimize for R²
        n_jobs=gridsearch_n_jobs,     # 使用配置的并行数
        verbose=2,                     # Print detailed progress
        refit=True,                   # Refit best model on full data
        return_train_score=False,     # 不返回训练分数以节省内存
        pre_dispatch=f'{gridsearch_n_jobs}*n_jobs'  # 控制预分发的作业数量
    )

    # Train model with GridSearch
    print("正在执行网格搜索，这可能需要一些时间...")
    grid_search.fit(X_train, y_train)

    # Get best model
    best_xgb = grid_search.best_estimator_
    print(f"\nBEST PARAMETERS FOUND:")
    for param, value in grid_search.best_params_.items():
        print(f"  {param}: {value}")

    # Save model
    model_path = os.path.join(output_dir, f'xgb_best_model_{fp_column}.joblib')
    joblib.dump(best_xgb, model_path)
    print(f"Saved best model to: {model_path}")

    # ===== MODEL EVALUATION =====
    print("\n--- Model Evaluation ---")

    # Extract cross-validation results
    cv_results = extract_cv_results_for_xgb(grid_search, output_dir=output_dir)

    # Training set performance
    print("\nEvaluating on training set...")
    y_train_pred = best_xgb.predict(X_train)
    train_metrics = evaluate_regression(y_train, y_train_pred, "Training", output_dir)

    # External validation set performance
    print("\nEvaluating on external validation set...")
    y_external_pred = best_xgb.predict(X_external)
    external_metrics = evaluate_regression(y_external, y_external_pred, "External_Validation", output_dir)

    # Feature importance
    print("\nComputing feature importance...")
    importances = best_xgb.feature_importances_
    sorted_idx = importances.argsort()[::-1]

    # Create feature names
    feature_names = [f"fp_{i}" for i in range(X_train.shape[1])]

    # Top 30 features
    plt.figure(figsize=(12, 12))
    plt.barh(range(min(30, len(feature_names))), importances[sorted_idx][:30][::-1], align='center')
    plt.yticks(range(min(30, len(feature_names))), np.array(feature_names)[sorted_idx][:30][::-1])
    plt.xlabel('Feature Importance')
    plt.title('Top 30 Feature Importances')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'plots', f'xgb_feature_importance_top30_{fp_column}.png'))
    plt.close()

    # All features (long form)
    pd.DataFrame({
        'Feature': feature_names,
        'Importance': importances
    }).to_csv(os.path.join(output_dir, 'xgb_feature_importances.csv'), index=False)

    # ===== FINAL PREDICTIONS & SAVING =====
    print("\nGenerating final predictions for all data...")

    # Preprocess entire dataset
    X_all = preprocess_test_fingerprint_data(all_data_temp, fp_column, max_length)

    # 使用原始数据（不包含分子指纹）作为输出基础
    clean_data = df_with_set.copy()  # 使用原始的df_with_set，不包含分子指纹

    # Make predictions
    clean_data['xgb_prediction'] = best_xgb.predict(X_all)

    # Save predictions
    output_filename = os.path.join(output_dir, f'xgb_final_predictions_{fp_column}.xlsx')
    clean_data.to_excel(output_filename, index=False)

    print(f"Note: Molecular fingerprints ({fp_column}) were read from preprocessed file and used for training but not saved to output file to save space.")

    # 清理临时数据以释放内存
    del all_data_temp
    print("Temporary data cleared from memory.")

    # Save model summary
    summary_path = os.path.join(output_dir, f'xgb_model_summary_{fp_column}.txt')
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("=== XGBOOST REGRESSION SUMMARY ===\n\n")
        f.write(f"Dataset: {input_file}\n")
        f.write(f"Generated: {pd.Timestamp.now()}\n")
        f.write(f"Fingerprint type: {fp_column}\n")
        f.write(f"Label column: {label_column}\n\n")

        f.write("DATA DISTRIBUTION:\n")
        f.write(f"  Training size: {len(train_data)}\n")
        f.write(f"  External validation size: {len(external_data)}\n")
        f.write(f"  Target statistics:\n{data[label_column].describe().to_string()}\n\n")

        f.write("BEST PARAMETERS:\n")
        for param, value in grid_search.best_params_.items():
            f.write(f"  {param}: {value}\n")
        f.write("\n")

        f.write("CROSS-VALIDATION PERFORMANCE (Training Set):\n")
        f.write(f"  Mean R2: {cv_results['CV_Mean_Score']:.4f}\n")
        f.write(f"  Standard deviation: {cv_results['CV_Std_Score']:.4f}\n")
        f.write("  Fold-by-fold scores:\n")
        for i, score in enumerate(cv_results['CV_Scores'], 1):
            f.write(f"    Fold {i}: {score:.4f}\n")
        f.write("\n")

        f.write("TRAINING SET PERFORMANCE:\n")
        for metric, value in train_metrics.items():
            f.write(f"  {metric}: {value:.4f}\n")
        f.write("\n")

        f.write("EXTERNAL VALIDATION PERFORMANCE:\n")
        for metric, value in external_metrics.items():
            f.write(f"  {metric}: {value:.4f}\n")
        f.write("\n")

        f.write("OUTPUT FILES:\n")
        f.write(f"  Predictions file: {output_filename}\n")
        f.write(f"  Model file: {model_path}\n")

    print(f"\n--- PIPELINE COMPLETED SUCCESSFULLY ---")
    print(f"Final predictions saved to: {output_filename}")
    print(f"Model summary saved to: {summary_path}")
    print(f"Model saved to: {model_path}")
