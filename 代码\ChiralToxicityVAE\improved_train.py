"""
改进的训练脚本，专门解决模式坍塌问题
"""

import torch
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
import os
import pickle
import time
from tqdm import tqdm

from model import ChiralToxicityVAE, vae_loss_function
from data import ChiralToxicityDataset
from improved_config import IMPROVED_CONFIG, get_cyclical_beta, get_temperature_schedule

class ImprovedVAETrainer:
    def __init__(self, config=IMPROVED_CONFIG):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        os.makedirs(config['output_dir'], exist_ok=True)
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.recon_losses = []
        self.kl_losses = []
        self.diversity_losses = []
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        
        # 多样性监控
        self.diversity_scores = []
        self.coverage_scores = []
        
    def create_model(self):
        """创建改进的模型"""
        self.model = ChiralToxicityVAE(
            molecular_feature_dim=self.config['molecular_feature_dim'],
            toxicity_dim=self.config['toxicity_dim'],
            latent_dim=self.config['latent_dim'],
            hidden_dims=self.config['hidden_dims']
        ).to(self.device)
        
        # 使用更保守的优化器设置
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay'],
            betas=(0.9, 0.999)  # 标准设置
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=self.config['scheduler_factor'],
            patience=self.config['scheduler_patience'],
            verbose=True
        )
        
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
    def train_epoch(self, train_loader, epoch):
        """改进的训练epoch"""
        self.model.train()
        total_loss = 0
        total_recon = 0
        total_kl = 0
        total_diversity = 0
        
        # 动态调整损失权重
        beta = get_cyclical_beta(
            epoch, 
            self.config['cycle_length'],
            self.config['beta_min'],
            self.config['beta_max']
        )
        
        temperature = get_temperature_schedule(epoch, self.config['epochs'])
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
        for batch_idx, (molecular_features, toxicity_data, mask) in enumerate(pbar):
            molecular_features = molecular_features.to(self.device)
            toxicity_data = toxicity_data.to(self.device)
            mask = mask.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            recon_toxicity, mu, logvar = self.model(
                molecular_features, toxicity_data, mask, temperature
            )
            
            # 计算损失
            loss, recon_loss, kl_loss, diversity_loss = vae_loss_function(
                recon_toxicity, toxicity_data, mask, mu, logvar,
                beta=beta,
                diversity_weight=self.config['diversity_weight']
            )
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪（防止梯度爆炸）
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 记录损失
            total_loss += loss.item()
            total_recon += recon_loss.item()
            total_kl += kl_loss.item()
            total_diversity += diversity_loss.item() if isinstance(diversity_loss, torch.Tensor) else diversity_loss
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Recon': f'{recon_loss.item():.4f}',
                'KL': f'{kl_loss.item():.4f}',
                'Div': f'{diversity_loss.item() if isinstance(diversity_loss, torch.Tensor) else diversity_loss:.4f}',
                'Beta': f'{beta:.3f}',
                'Temp': f'{temperature:.3f}'
            })
        
        avg_loss = total_loss / len(train_loader)
        avg_recon = total_recon / len(train_loader)
        avg_kl = total_kl / len(train_loader)
        avg_diversity = total_diversity / len(train_loader)
        
        return avg_loss, avg_recon, avg_kl, avg_diversity, beta, temperature
    
    def validate_epoch(self, val_loader, epoch):
        """验证epoch"""
        self.model.eval()
        total_loss = 0
        total_recon = 0
        total_kl = 0
        total_diversity = 0
        
        beta = get_cyclical_beta(epoch, self.config['cycle_length'])
        
        with torch.no_grad():
            for molecular_features, toxicity_data, mask in val_loader:
                molecular_features = molecular_features.to(self.device)
                toxicity_data = toxicity_data.to(self.device)
                mask = mask.to(self.device)
                
                recon_toxicity, mu, logvar = self.model(molecular_features, toxicity_data, mask)
                
                loss, recon_loss, kl_loss, diversity_loss = vae_loss_function(
                    recon_toxicity, toxicity_data, mask, mu, logvar,
                    beta=beta,
                    diversity_weight=self.config['diversity_weight']
                )
                
                total_loss += loss.item()
                total_recon += recon_loss.item()
                total_kl += kl_loss.item()
                total_diversity += diversity_loss.item() if isinstance(diversity_loss, torch.Tensor) else diversity_loss
        
        avg_loss = total_loss / len(val_loader)
        avg_recon = total_recon / len(val_loader)
        avg_kl = total_kl / len(val_loader)
        avg_diversity = total_diversity / len(val_loader)
        
        return avg_loss, avg_recon, avg_kl, avg_diversity
    
    def calculate_diversity_metrics(self, val_loader):
        """计算多样性指标"""
        self.model.eval()
        all_mu = []
        all_generated = []
        
        with torch.no_grad():
            for molecular_features, toxicity_data, mask in val_loader:
                molecular_features = molecular_features.to(self.device)
                toxicity_data = toxicity_data.to(self.device)
                mask = mask.to(self.device)
                
                # 获取潜在表示
                mu, _ = self.model.encode(molecular_features, toxicity_data, mask)
                all_mu.append(mu.cpu())
                
                # 生成样本
                generated_probs, _ = self.model.generate(molecular_features, n_samples=5)
                all_generated.append(generated_probs.cpu())
        
        all_mu = torch.cat(all_mu, dim=0)
        all_generated = torch.cat(all_generated, dim=0)
        
        # 计算潜在空间多样性
        mu_std = torch.std(all_mu, dim=0).mean().item()
        
        # 计算生成样本多样性
        gen_std = torch.std(all_generated, dim=0).mean().item()
        
        return mu_std, gen_std
    
    def save_checkpoint(self, epoch, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'recon_losses': self.recon_losses,
            'kl_losses': self.kl_losses,
            'diversity_losses': self.diversity_losses,
            'config': self.config
        }
        
        # 保存最新检查点
        torch.save(checkpoint, os.path.join(self.config['output_dir'], 'latest_checkpoint.pth'))
        
        # 保存最佳模型
        if is_best:
            torch.save(checkpoint, os.path.join(self.config['output_dir'], 'best_model.pth'))
            print(f"保存最佳模型 (epoch {epoch+1})")
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        plt.figure(figsize=(15, 10))
        
        # 总损失
        plt.subplot(2, 3, 1)
        plt.plot(self.train_losses, label='Train')
        plt.plot(self.val_losses, label='Validation')
        plt.title('Total Loss')
        plt.legend()
        plt.grid(True)
        
        # 重构损失
        plt.subplot(2, 3, 2)
        plt.plot(self.recon_losses, label='Reconstruction')
        plt.title('Reconstruction Loss')
        plt.legend()
        plt.grid(True)
        
        # KL损失
        plt.subplot(2, 3, 3)
        plt.plot(self.kl_losses, label='KL Divergence')
        plt.title('KL Divergence')
        plt.legend()
        plt.grid(True)
        
        # 多样性损失
        plt.subplot(2, 3, 4)
        plt.plot(self.diversity_losses, label='Diversity')
        plt.title('Diversity Loss')
        plt.legend()
        plt.grid(True)
        
        # 多样性指标
        if self.diversity_scores:
            plt.subplot(2, 3, 5)
            plt.plot(self.diversity_scores, label='Latent Diversity')
            plt.title('Latent Space Diversity')
            plt.legend()
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.config['output_dir'], 'training_curves.png'), dpi=300)
        plt.close()
    
    def train(self, train_loader, val_loader):
        """主训练循环"""
        print("开始改进的VAE训练...")
        print(f"训练配置: {self.config['epochs']} epochs, batch_size={self.config['batch_size']}")
        
        for epoch in range(self.config['epochs']):
            start_time = time.time()
            
            # 训练
            train_loss, train_recon, train_kl, train_div, beta, temp = self.train_epoch(train_loader, epoch)
            
            # 验证
            val_loss, val_recon, val_kl, val_div = self.validate_epoch(val_loader, epoch)
            
            # 记录损失
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.recon_losses.append(train_recon)
            self.kl_losses.append(train_kl)
            self.diversity_losses.append(train_div)
            
            # 计算多样性指标
            if epoch % 20 == 0:
                mu_div, gen_div = self.calculate_diversity_metrics(val_loader)
                self.diversity_scores.append(mu_div)
                print(f"多样性指标 - 潜在空间: {mu_div:.4f}, 生成样本: {gen_div:.4f}")
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 早停检查
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self.patience_counter = 0
                self.save_checkpoint(epoch, is_best=True)
            else:
                self.patience_counter += 1
            
            # 定期保存
            if epoch % self.config['save_interval'] == 0:
                self.save_checkpoint(epoch)
                self.plot_training_curves()
            
            # 打印进度
            epoch_time = time.time() - start_time
            print(f"Epoch {epoch+1}/{self.config['epochs']} - "
                  f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, "
                  f"Beta: {beta:.3f}, Temp: {temp:.3f}, Time: {epoch_time:.1f}s")
            
            # 早停
            if self.patience_counter >= self.config['patience']:
                print(f"早停触发 (patience={self.config['patience']})")
                break
        
        print("训练完成!")
        self.plot_training_curves()
        return self.model

def main():
    """主函数"""
    print("=== 改进的VAE训练 - 解决模式坍塌问题 ===")
    
    # 创建数据集
    dataset = ChiralToxicityDataset(IMPROVED_CONFIG['data_path'])
    
    # 数据分割
    train_size = int(0.7 * len(dataset))
    val_size = int(0.2 * len(dataset))
    test_size = len(dataset) - train_size - val_size
    
    train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size, test_size]
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=IMPROVED_CONFIG['batch_size'], shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=IMPROVED_CONFIG['batch_size'], shuffle=False)
    
    print(f"数据集大小: 训练={len(train_dataset)}, 验证={len(val_dataset)}, 测试={len(test_dataset)}")
    
    # 创建训练器
    trainer = ImprovedVAETrainer(IMPROVED_CONFIG)
    trainer.create_model()
    
    # 开始训练
    model = trainer.train(train_loader, val_loader)
    
    print("训练完成! 模型已保存到:", IMPROVED_CONFIG['output_dir'])

if __name__ == "__main__":
    main()
