"""
简单的启动脚本，使用改进的训练方法
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    print("=== 启动改进的VAE训练 ===")
    print("这将使用专门解决模式坍塌问题的改进训练脚本")
    
    try:
        # 导入改进的训练模块
        from improved_train import main as improved_main
        
        # 运行改进的训练
        improved_main()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖都已安装")
        
        # 回退到修复后的原始训练
        print("\n回退到原始训练方法...")
        try:
            from main import main as original_main
            original_main()
        except Exception as e2:
            print(f"原始训练也失败: {e2}")
            print("请检查数据文件和依赖")
            
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        print("建议:")
        print("1. 检查数据文件路径是否正确")
        print("2. 确保所有Python包都已安装")
        print("3. 查看详细错误信息进行调试")

if __name__ == "__main__":
    main()
