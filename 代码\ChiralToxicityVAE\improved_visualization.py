"""
改进的化学空间可视化分析
专门用于比较生成数据和原始数据的分布质量
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import seaborn as sns
from rdkit import Chem
from rdkit.Chem import Descriptors, AllChem
import warnings
warnings.filterwarnings('ignore')

try:
    import umap
    UMAP_AVAILABLE = True
except ImportError:
    UMAP_AVAILABLE = False
    print("UMAP not available, using only t-SNE and PCA")

class ImprovedChemicalSpaceAnalyzer:
    def __init__(self, original_data_path, generated_data_path=None):
        """
        初始化化学空间分析器
        
        Args:
            original_data_path: 原始数据路径
            generated_data_path: 生成数据路径（可选）
        """
        self.original_data_path = original_data_path
        self.generated_data_path = generated_data_path
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    def load_and_process_data(self):
        """加载并处理数据"""
        print("加载原始数据...")
        original_df = pd.read_csv(self.original_data_path)
        
        # 计算原始数据的分子特征
        self.original_features = self._calculate_molecular_features(original_df['smiles'])
        self.original_fingerprints = self._calculate_fingerprints(original_df['smiles'])
        
        if self.generated_data_path:
            print("加载生成数据...")
            generated_df = pd.read_csv(self.generated_data_path)
            self.generated_features = self._calculate_molecular_features(generated_df['smiles'])
            self.generated_fingerprints = self._calculate_fingerprints(generated_df['smiles'])
        else:
            print("未提供生成数据，将只分析原始数据")
            self.generated_features = None
            self.generated_fingerprints = None
    
    def _calculate_molecular_features(self, smiles_list):
        """计算分子特征"""
        features = []
        
        for smiles in smiles_list:
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol:
                    feature_dict = {
                        'MW': Descriptors.MolWt(mol),
                        'LogP': Descriptors.MolLogP(mol),
                        'TPSA': Descriptors.TPSA(mol),
                        'HBD': Descriptors.NumHDonors(mol),
                        'HBA': Descriptors.NumHAcceptors(mol),
                        'RotBonds': Descriptors.NumRotatableBonds(mol),
                        'AromaticRings': Descriptors.NumAromaticRings(mol),
                        'StereoCount': smiles.count('@'),
                        'RingCount': Descriptors.RingCount(mol),
                        'CarbonCount': sum(1 for atom in mol.GetAtoms() if atom.GetSymbol() == 'C'),
                        'NitrogenCount': sum(1 for atom in mol.GetAtoms() if atom.GetSymbol() == 'N'),
                        'OxygenCount': sum(1 for atom in mol.GetAtoms() if atom.GetSymbol() == 'O'),
                    }
                    features.append(list(feature_dict.values()))
                else:
                    # 如果分子无效，使用默认值
                    features.append([0] * 12)
            except:
                features.append([0] * 12)
        
        return np.array(features)
    
    def _calculate_fingerprints(self, smiles_list):
        """计算分子指纹"""
        fingerprints = []
        
        for smiles in smiles_list:
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol:
                    fp = AllChem.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
                    fingerprints.append(np.array(fp))
                else:
                    fingerprints.append(np.zeros(2048))
            except:
                fingerprints.append(np.zeros(2048))
        
        return np.array(fingerprints)
    
    def analyze_feature_distributions(self):
        """分析特征分布"""
        if self.generated_features is None:
            print("没有生成数据，跳过分布比较")
            return
        
        feature_names = ['MW', 'LogP', 'TPSA', 'HBD', 'HBA', 'RotBonds', 
                        'AromaticRings', 'StereoCount', 'RingCount', 
                        'CarbonCount', 'NitrogenCount', 'OxygenCount']
        
        fig, axes = plt.subplots(3, 4, figsize=(20, 15))
        axes = axes.flatten()
        
        for i, feature_name in enumerate(feature_names):
            if i < len(axes):
                # 绘制分布对比
                axes[i].hist(self.original_features[:, i], bins=30, alpha=0.6, 
                           label='原始数据', color='blue', density=True)
                axes[i].hist(self.generated_features[:, i], bins=30, alpha=0.6, 
                           label='生成数据', color='orange', density=True)
                axes[i].set_title(f'{feature_name} 分布对比')
                axes[i].set_xlabel(feature_name)
                axes[i].set_ylabel('密度')
                axes[i].legend()
                axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('feature_distribution_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_chemical_space(self):
        """分析化学空间分布"""
        if self.generated_fingerprints is None:
            print("没有生成数据，跳过化学空间分析")
            return
        
        # 合并指纹数据
        all_fingerprints = np.vstack([self.original_fingerprints, self.generated_fingerprints])
        
        # 标准化
        scaler = StandardScaler()
        all_fingerprints_scaled = scaler.fit_transform(all_fingerprints)
        
        # 创建子图
        fig, axes = plt.subplots(1, 3, figsize=(24, 7))
        
        # 1. PCA分析
        print("执行PCA降维...")
        pca = PCA(n_components=2, random_state=42)
        pca_coords = pca.fit_transform(all_fingerprints_scaled)
        
        original_pca = pca_coords[:len(self.original_fingerprints)]
        generated_pca = pca_coords[len(self.original_fingerprints):]
        
        axes[0].scatter(original_pca[:, 0], original_pca[:, 1], 
                       c='blue', alpha=0.6, s=20, label='原始数据')
        axes[0].scatter(generated_pca[:, 0], generated_pca[:, 1], 
                       c='orange', alpha=0.6, s=20, label='生成数据')
        axes[0].set_title(f'PCA 化学空间分布\n(解释方差: {pca.explained_variance_ratio_.sum():.3f})')
        axes[0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.3f})')
        axes[0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.3f})')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 2. t-SNE分析
        print("执行t-SNE降维...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(all_fingerprints)//4))
        tsne_coords = tsne.fit_transform(all_fingerprints_scaled)
        
        original_tsne = tsne_coords[:len(self.original_fingerprints)]
        generated_tsne = tsne_coords[len(self.original_fingerprints):]
        
        axes[1].scatter(original_tsne[:, 0], original_tsne[:, 1], 
                       c='blue', alpha=0.6, s=20, label='原始数据')
        axes[1].scatter(generated_tsne[:, 0], generated_tsne[:, 1], 
                       c='orange', alpha=0.6, s=20, label='生成数据')
        axes[1].set_title('t-SNE 化学空间分布')
        axes[1].set_xlabel('t-SNE 1')
        axes[1].set_ylabel('t-SNE 2')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # 3. UMAP分析（如果可用）
        if UMAP_AVAILABLE:
            print("执行UMAP降维...")
            umap_reducer = umap.UMAP(n_components=2, random_state=42, n_jobs=1)
            umap_coords = umap_reducer.fit_transform(all_fingerprints_scaled)
            
            original_umap = umap_coords[:len(self.original_fingerprints)]
            generated_umap = umap_coords[len(self.original_fingerprints):]
            
            axes[2].scatter(original_umap[:, 0], original_umap[:, 1], 
                           c='blue', alpha=0.6, s=20, label='原始数据')
            axes[2].scatter(generated_umap[:, 0], generated_umap[:, 1], 
                           c='orange', alpha=0.6, s=20, label='生成数据')
            axes[2].set_title('UMAP 化学空间分布')
            axes[2].set_xlabel('UMAP 1')
            axes[2].set_ylabel('UMAP 2')
            axes[2].legend()
            axes[2].grid(True, alpha=0.3)
        else:
            axes[2].text(0.5, 0.5, 'UMAP 不可用', ha='center', va='center', 
                        transform=axes[2].transAxes, fontsize=16)
            axes[2].set_title('UMAP 化学空间分布')
        
        plt.tight_layout()
        plt.savefig('improved_chemical_space_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return {
            'pca_explained_variance': pca.explained_variance_ratio_.sum(),
            'original_count': len(self.original_fingerprints),
            'generated_count': len(self.generated_fingerprints) if self.generated_fingerprints is not None else 0
        }
    
    def calculate_diversity_metrics(self):
        """计算多样性指标"""
        if self.generated_fingerprints is None:
            print("没有生成数据，跳过多样性分析")
            return
        
        from sklearn.metrics.pairwise import pairwise_distances
        
        # 计算内部多样性（平均距离）
        original_distances = pairwise_distances(self.original_fingerprints, metric='jaccard')
        generated_distances = pairwise_distances(self.generated_fingerprints, metric='jaccard')
        
        original_diversity = np.mean(original_distances[np.triu_indices_from(original_distances, k=1)])
        generated_diversity = np.mean(generated_distances[np.triu_indices_from(generated_distances, k=1)])
        
        # 计算覆盖度（生成数据与原始数据的最小距离）
        cross_distances = pairwise_distances(self.generated_fingerprints, self.original_fingerprints, metric='jaccard')
        coverage = np.mean(np.min(cross_distances, axis=1))
        
        print(f"\n=== 多样性分析结果 ===")
        print(f"原始数据内部多样性: {original_diversity:.4f}")
        print(f"生成数据内部多样性: {generated_diversity:.4f}")
        print(f"生成数据覆盖度: {coverage:.4f}")
        print(f"多样性比率: {generated_diversity/original_diversity:.4f}")
        
        return {
            'original_diversity': original_diversity,
            'generated_diversity': generated_diversity,
            'coverage': coverage,
            'diversity_ratio': generated_diversity/original_diversity
        }

def main():
    """主函数"""
    print("=== 改进的化学空间分析 ===")
    
    # 初始化分析器
    analyzer = ImprovedChemicalSpaceAnalyzer(
        original_data_path='../../数据/Chiral_AquaTox_scr.csv',
        generated_data_path='results/generated_molecules.csv'  # 如果有生成数据的话
    )
    
    # 加载数据
    analyzer.load_and_process_data()
    
    # 分析特征分布
    analyzer.analyze_feature_distributions()
    
    # 分析化学空间
    space_metrics = analyzer.analyze_chemical_space()
    
    # 计算多样性指标
    diversity_metrics = analyzer.calculate_diversity_metrics()
    
    print("\n=== 分析完成 ===")
    if space_metrics:
        print(f"PCA解释方差: {space_metrics['pca_explained_variance']:.3f}")
        print(f"原始数据数量: {space_metrics['original_count']}")
        print(f"生成数据数量: {space_metrics['generated_count']}")

if __name__ == "__main__":
    main()
