"""
独立的分子验证脚本
使用金标准验证流程验证生成的分子或任意SMILES列表
"""

import pandas as pd
import sys
import os
from molecular_validator import GoldStandardValidator

# 验证配置
VALIDATION_CONFIG = {
    # 参考数据路径（用于应用域评估）
    'reference_data_path': '../../数据/Chiral_AquaTox_scr.csv',
    
    # 输入文件路径（包含SMILES的CSV文件）
    'input_file': 'results/generated_toxicity_data.csv',
    
    # SMILES列名
    'smiles_column': 'smiles',
    
    # 输出文件路径
    'output_file': 'validation_results.csv',
    
    # 验证选项
    'save_detailed_results': True,
    'print_statistics': True,
    
    # 过滤选项
    'filter_valid_only': False,  # 是否只保留验证通过的分子
    'min_score_threshold': 0.0,  # 最小评分阈值
}

def validate_from_csv(input_file, config=VALIDATION_CONFIG):
    """
    从CSV文件读取SMILES并进行验证
    
    Args:
        input_file: 输入CSV文件路径
        config: 验证配置
        
    Returns:
        验证结果DataFrame
    """
    print(f"Reading SMILES from: {input_file}")
    
    # 读取输入文件
    try:
        df = pd.read_csv(input_file)
        print(f"Loaded {len(df)} rows from input file")
    except Exception as e:
        print(f"Error reading input file: {e}")
        return None
    
    # 检查SMILES列
    smiles_col = config['smiles_column']
    if smiles_col not in df.columns:
        print(f"Error: Column '{smiles_col}' not found in input file")
        print(f"Available columns: {list(df.columns)}")
        return None
    
    # 提取SMILES
    smiles_list = df[smiles_col].dropna().tolist()
    print(f"Found {len(smiles_list)} valid SMILES strings")
    
    if len(smiles_list) == 0:
        print("No valid SMILES found in input file")
        return None
    
    # 创建验证器
    print("Initializing Gold Standard Validator...")
    validator = GoldStandardValidator(config['reference_data_path'])
    
    # 执行验证
    print("Starting validation process...")
    validation_results = validator.validate_batch(
        smiles_list, 
        save_results=config['save_detailed_results'],
        output_file=config['output_file'].replace('.csv', '.pkl')
    )
    
    # 创建结果DataFrame
    results_data = []
    for i, result in enumerate(validation_results['detailed_results']):
        row_data = df.iloc[i].to_dict()  # 保留原始数据
        
        # 添加验证结果
        row_data.update({
            'validation_is_valid': result['is_valid'],
            'validation_final_score': result['final_score'],
            'validation_clean_smiles': result.get('clean_smiles', result['smiles']),
            'validation_stage1_passed': result['validation_stages']['stage1']['passed'],
            'validation_stage2_passed': result['validation_stages']['stage2']['passed'],
            'validation_stage3_passed': result['validation_stages']['stage3']['passed'],
            'validation_stage4_passed': result['validation_stages']['stage4']['passed'],
            'validation_stage5_passed': result['validation_stages']['stage5']['passed'],
            'validation_stage1_score': result['validation_stages']['stage1']['score'],
            'validation_stage2_score': result['validation_stages']['stage2']['score'],
            'validation_stage3_score': result['validation_stages']['stage3']['score'],
            'validation_stage4_score': result['validation_stages']['stage4']['score'],
            'validation_stage5_score': result['validation_stages']['stage5']['score'],
            'validation_warnings': '; '.join(result['warnings']),
            'validation_errors': '; '.join(result['errors'])
        })
        
        results_data.append(row_data)
    
    results_df = pd.DataFrame(results_data)
    
    # 应用过滤器
    if config['filter_valid_only']:
        before_count = len(results_df)
        results_df = results_df[results_df['validation_is_valid'] == True]
        after_count = len(results_df)
        print(f"Filtered to valid molecules only: {after_count}/{before_count} retained")
    
    if config['min_score_threshold'] > 0:
        before_count = len(results_df)
        results_df = results_df[results_df['validation_final_score'] >= config['min_score_threshold']]
        after_count = len(results_df)
        print(f"Filtered by score threshold ({config['min_score_threshold']}): {after_count}/{before_count} retained")
    
    # 保存结果
    output_file = config['output_file']
    results_df.to_csv(output_file, index=False)
    print(f"Validation results saved to: {output_file}")
    
    # 打印统计信息
    if config['print_statistics']:
        print_validation_statistics(results_df, validation_results)
    
    return results_df

def validate_smiles_list(smiles_list, config=VALIDATION_CONFIG):
    """
    直接验证SMILES列表
    
    Args:
        smiles_list: SMILES字符串列表
        config: 验证配置
        
    Returns:
        验证结果
    """
    print(f"Validating {len(smiles_list)} SMILES strings...")
    
    # 创建验证器
    validator = GoldStandardValidator(config['reference_data_path'])
    
    # 执行验证
    validation_results = validator.validate_batch(
        smiles_list,
        save_results=config['save_detailed_results'],
        output_file=config['output_file'].replace('.csv', '.pkl')
    )
    
    return validation_results

def print_validation_statistics(results_df, validation_results):
    """打印详细的验证统计信息"""
    print(f"\n" + "="*60)
    print("DETAILED VALIDATION STATISTICS")
    print("="*60)
    
    total = len(results_df)
    valid = (results_df['validation_is_valid'] == True).sum()
    
    print(f"Total molecules processed: {total}")
    print(f"Valid molecules: {valid} ({valid/total:.1%})")
    print(f"Average final score: {results_df['validation_final_score'].mean():.3f}")
    
    # 各阶段统计
    print(f"\nStage-wise pass rates:")
    stages = ['stage1', 'stage2', 'stage3', 'stage4', 'stage5']
    stage_names = ['Basic Validity', 'Chemical Feasibility', 'Synthetic Accessibility', '3D Stability', 'Applicability Domain']
    
    for stage, name in zip(stages, stage_names):
        col = f'validation_{stage}_passed'
        if col in results_df.columns:
            passed = (results_df[col] == True).sum()
            score_col = f'validation_{stage}_score'
            avg_score = results_df[score_col].mean()
            print(f"  {name}: {passed}/{total} ({passed/total:.1%}) - Avg Score: {avg_score:.3f}")
    
    # 评分分布
    print(f"\nScore distribution:")
    score_ranges = [(0.0, 0.2), (0.2, 0.4), (0.4, 0.6), (0.6, 0.8), (0.8, 1.0)]
    for low, high in score_ranges:
        count = ((results_df['validation_final_score'] >= low) & 
                (results_df['validation_final_score'] < high)).sum()
        print(f"  {low:.1f}-{high:.1f}: {count} molecules ({count/total:.1%})")
    
    # 最常见的警告和错误
    all_warnings = []
    all_errors = []
    
    for warnings in results_df['validation_warnings'].dropna():
        if warnings:
            all_warnings.extend([w.strip() for w in warnings.split(';')])
    
    for errors in results_df['validation_errors'].dropna():
        if errors:
            all_errors.extend([e.strip() for e in errors.split(';')])
    
    if all_warnings:
        from collections import Counter
        warning_counts = Counter(all_warnings)
        print(f"\nMost common warnings:")
        for warning, count in warning_counts.most_common(5):
            print(f"  {warning}: {count} times")
    
    if all_errors:
        from collections import Counter
        error_counts = Counter(all_errors)
        print(f"\nMost common errors:")
        for error, count in error_counts.most_common(5):
            print(f"  {error}: {count} times")

def main():
    """主函数"""
    print("Molecular Validation Script")
    print("Using Gold Standard Validation Protocol")
    print("="*50)
    
    # 检查输入文件是否存在
    input_file = VALIDATION_CONFIG['input_file']
    
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        print(f"Using input file from command line: {input_file}")
    
    if not os.path.exists(input_file):
        print(f"Input file not found: {input_file}")
        print("Please ensure the file exists or provide a valid path")
        
        # 提供示例SMILES进行演示
        print("\nRunning demonstration with example SMILES...")
        example_smiles = [
            "C[C@H](O)C(=O)O",  # 乳酸
            "C[C@H](N)C(=O)O",  # 丙氨酸
            "C[C@@H](O)[C@H](O)C",  # 多手性中心
            "C[C@H]1CC[C@@H](O)C1",  # 环状手性分子
            "CC(C)CC[C@H](N)C(=O)O",  # 亮氨酸
        ]
        
        results = validate_smiles_list(example_smiles)
        print("Demonstration completed!")
        return
    
    # 验证CSV文件中的分子
    results_df = validate_from_csv(input_file, VALIDATION_CONFIG)
    
    if results_df is not None:
        print(f"\nValidation completed successfully!")
        print(f"Results saved to: {VALIDATION_CONFIG['output_file']}")
    else:
        print("Validation failed!")

if __name__ == "__main__":
    main()
