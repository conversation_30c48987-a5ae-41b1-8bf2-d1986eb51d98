"""
手性分子毒性VAE模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ChiralToxicityVAE(nn.Module):
    def __init__(self, molecular_feature_dim=10, toxicity_dim=5, latent_dim=32, hidden_dims=[64, 32]):
        super(ChiralToxicityVAE, self).__init__()
        
        self.molecular_feature_dim = molecular_feature_dim
        self.toxicity_dim = toxicity_dim
        self.latent_dim = latent_dim
        
        # 编码器
        encoder_input_dim = molecular_feature_dim + toxicity_dim
        encoder_layers = []
        prev_dim = encoder_input_dim
        
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        self.encoder = nn.Sequential(*encoder_layers)
        self.fc_mu = nn.Linear(hidden_dims[-1], latent_dim)
        self.fc_logvar = nn.Linear(hidden_dims[-1], latent_dim)
        
        # 解码器
        decoder_input_dim = latent_dim + molecular_feature_dim
        decoder_layers = []
        prev_dim = decoder_input_dim
        
        for hidden_dim in reversed(hidden_dims):
            decoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        decoder_layers.extend([
            nn.Linear(prev_dim, toxicity_dim),
            nn.Sigmoid()
        ])
        
        self.decoder = nn.Sequential(*decoder_layers)
    
    def encode(self, molecular_features, toxicity_data, mask):
        masked_toxicity = toxicity_data * mask
        x = torch.cat([molecular_features, masked_toxicity], dim=1)
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar
    
    def reparameterize(self, mu, logvar, temperature=1.0):
        std = torch.exp(0.5 * logvar) * temperature
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z, molecular_features):
        x = torch.cat([z, molecular_features], dim=1)
        return self.decoder(x)
    
    def forward(self, molecular_features, toxicity_data, mask, temperature=1.0):
        mu, logvar = self.encode(molecular_features, toxicity_data, mask)
        z = self.reparameterize(mu, logvar, temperature)
        recon_toxicity = self.decode(z, molecular_features)
        return recon_toxicity, mu, logvar
    
    def generate(self, molecular_features, n_samples=1, temperature=1.0):
        self.eval()
        with torch.no_grad():
            batch_size = molecular_features.size(0)
            if n_samples > 1:
                molecular_features = molecular_features.repeat_interleave(n_samples, dim=0)

            z = torch.randn(batch_size * n_samples, self.latent_dim) * temperature
            if molecular_features.is_cuda:
                z = z.cuda()

            toxicity_probs = self.decode(z, molecular_features)
            toxicity_binary = (toxicity_probs > 0.5).float()

            return toxicity_probs, toxicity_binary
    
    def conditional_generate(self, molecular_features, known_toxicity, known_mask, n_samples=1, temperature=1.0):
        self.eval()
        with torch.no_grad():
            mu, logvar = self.encode(molecular_features, known_toxicity, known_mask)

            generated_samples = []
            for _ in range(n_samples):
                z = self.reparameterize(mu, logvar, temperature)
                full_toxicity = self.decode(z, molecular_features)
                final_toxicity = known_toxicity * known_mask + full_toxicity * (1 - known_mask)
                generated_samples.append(final_toxicity)

            return torch.stack(generated_samples, dim=1)

def vae_loss_function(recon_toxicity, target_toxicity, mask, mu, logvar, beta=1.0):
    recon_loss = F.binary_cross_entropy(
        recon_toxicity * mask, 
        target_toxicity * mask, 
        reduction='sum'
    ) / mask.sum()
    
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp()) / mu.size(0)
    total_loss = recon_loss + beta * kl_loss
    
    return total_loss, recon_loss, kl_loss
