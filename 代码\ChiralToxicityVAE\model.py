"""
手性分子毒性VAE模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ChiralToxicityVAE(nn.Module):
    def __init__(self, molecular_feature_dim=10, toxicity_dim=5, latent_dim=64, hidden_dims=[128, 64]):
        """
        改进的VAE模型，解决模式坍塌问题

        改进点：
        1. 增加latent_dim从32到64，提高表示能力
        2. 增加hidden_dims，提高模型容量
        3. 添加多样性损失项
        """
        super(ChiralToxicityVAE, self).__init__()

        self.molecular_feature_dim = molecular_feature_dim
        self.toxicity_dim = toxicity_dim
        self.latent_dim = latent_dim
        
        # 编码器
        encoder_input_dim = molecular_feature_dim + toxicity_dim
        encoder_layers = []
        prev_dim = encoder_input_dim
        
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        self.encoder = nn.Sequential(*encoder_layers)
        self.fc_mu = nn.Linear(hidden_dims[-1], latent_dim)
        self.fc_logvar = nn.Linear(hidden_dims[-1], latent_dim)
        
        # 解码器
        decoder_input_dim = latent_dim + molecular_feature_dim
        decoder_layers = []
        prev_dim = decoder_input_dim
        
        for hidden_dim in reversed(hidden_dims):
            decoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        decoder_layers.extend([
            nn.Linear(prev_dim, toxicity_dim),
            nn.Sigmoid()
        ])
        
        self.decoder = nn.Sequential(*decoder_layers)
    
    def encode(self, molecular_features, toxicity_data, mask):
        masked_toxicity = toxicity_data * mask
        x = torch.cat([molecular_features, masked_toxicity], dim=1)
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar
    
    def reparameterize(self, mu, logvar, temperature=1.0):
        std = torch.exp(0.5 * logvar) * temperature
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z, molecular_features):
        x = torch.cat([z, molecular_features], dim=1)
        return self.decoder(x)
    
    def forward(self, molecular_features, toxicity_data, mask, temperature=1.0):
        mu, logvar = self.encode(molecular_features, toxicity_data, mask)
        z = self.reparameterize(mu, logvar, temperature)
        recon_toxicity = self.decode(z, molecular_features)
        return recon_toxicity, mu, logvar
    
    def generate(self, molecular_features, n_samples=1, temperature=1.0):
        self.eval()
        with torch.no_grad():
            batch_size = molecular_features.size(0)
            if n_samples > 1:
                molecular_features = molecular_features.repeat_interleave(n_samples, dim=0)

            z = torch.randn(batch_size * n_samples, self.latent_dim) * temperature
            if molecular_features.is_cuda:
                z = z.cuda()

            toxicity_probs = self.decode(z, molecular_features)
            toxicity_binary = (toxicity_probs > 0.5).float()

            return toxicity_probs, toxicity_binary
    
    def conditional_generate(self, molecular_features, known_toxicity, known_mask, n_samples=1, temperature=1.0):
        self.eval()
        with torch.no_grad():
            mu, logvar = self.encode(molecular_features, known_toxicity, known_mask)

            generated_samples = []
            for _ in range(n_samples):
                z = self.reparameterize(mu, logvar, temperature)
                full_toxicity = self.decode(z, molecular_features)
                final_toxicity = known_toxicity * known_mask + full_toxicity * (1 - known_mask)
                generated_samples.append(final_toxicity)

            return torch.stack(generated_samples, dim=1)

def vae_loss_function(recon_toxicity, target_toxicity, mask, mu, logvar, beta=1.0, diversity_weight=0.1):
    """
    改进的VAE损失函数，添加多样性正则化来防止模式坍塌
    """
    # 重构损失
    recon_loss = F.binary_cross_entropy(
        recon_toxicity * mask,
        target_toxicity * mask,
        reduction='sum'
    ) / mask.sum()

    # KL散度损失
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp()) / mu.size(0)

    # 多样性损失：鼓励潜在空间的多样性
    diversity_loss = 0.0
    if mu.size(0) > 1:
        # 计算批次内潜在向量的平均距离
        mu_expanded = mu.unsqueeze(1)  # [batch, 1, latent_dim]
        mu_tiled = mu.unsqueeze(0)     # [1, batch, latent_dim]
        distances = torch.norm(mu_expanded - mu_tiled, dim=2)  # [batch, batch]

        # 排除对角线（自己与自己的距离）
        mask_diag = torch.eye(mu.size(0), device=mu.device).bool()
        distances = distances.masked_fill(mask_diag, 0)

        # 平均距离，鼓励更大的距离（更多样性）
        avg_distance = distances.sum() / (mu.size(0) * (mu.size(0) - 1))
        diversity_loss = -avg_distance  # 负号：最小化负距离 = 最大化距离

    total_loss = recon_loss + beta * kl_loss + diversity_weight * diversity_loss

    return total_loss, recon_loss, kl_loss, diversity_loss

def vae_loss_function_legacy(recon_toxicity, target_toxicity, mask, mu, logvar, beta=1.0):
    """
    向后兼容的损失函数，返回3个值
    """
    total_loss, recon_loss, kl_loss, diversity_loss = vae_loss_function(
        recon_toxicity, target_toxicity, mask, mu, logvar, beta, diversity_weight=0.0
    )
    return total_loss, recon_loss, kl_loss
