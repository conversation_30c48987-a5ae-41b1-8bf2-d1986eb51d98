"""
金标准分子验证器
实现从SMILES字符串到高可靠性分子的完整验证流程
"""

import numpy as np
import pandas as pd
from rdkit import Chem
from rdkit.Chem import AllChem, Descriptors, Crippen, Lipinski, FilterCatalog
from rdkit.Chem.FilterCatalog import FilterCatalogParams
from rdkit.Chem.Scaffolds import MurckoScaffold
from rdkit.Chem import rdMolDescriptors
from rdkit.ML.Descriptors import MoleculeDescriptors
import pickle
import os

class GoldStandardValidator:
    """金标准分子验证器"""
    
    def __init__(self, reference_data_path=None):
        """
        初始化验证器
        
        Args:
            reference_data_path: 参考数据集路径，用于应用域评估
        """
        # 允许的元素列表
        self.allowed_elements = {'C', 'H', 'O', 'N', 'S', 'P', 'F', 'Cl', 'Br', 'I'}
        
        # 物理化学性质范围
        self.property_ranges = {
            'MW': (100, 600),           # 分子量
            'LogP': (-2, 6),            # 脂水分配系数
            'HBD': (0, 5),              # 氢键供体
            'HBA': (0, 10),             # 氢键受体
            'TPSA': (0, 140),           # 拓扑极性表面积
            'RotBonds': (0, 10),        # 可旋转键
            'AromaticRings': (0, 4),    # 芳香环数
        }
        
        # 合成可及性阈值
        self.sa_score_threshold = 7.0
        
        # 应用域相似度阈值
        self.similarity_threshold = 0.35
        
        # 初始化PAINS过滤器
        self._init_filters()
        
        # 加载参考数据集
        self.reference_fingerprints = None
        if reference_data_path:
            self._load_reference_data(reference_data_path)
    
    def _init_filters(self):
        """初始化结构过滤器"""
        try:
            # PAINS过滤器
            params = FilterCatalogParams()
            params.AddCatalog(FilterCatalogParams.FilterCatalogs.PAINS)
            self.pains_filter = FilterCatalog.FilterCatalog(params)
            
            # BRENK过滤器
            params_brenk = FilterCatalogParams()
            params_brenk.AddCatalog(FilterCatalogParams.FilterCatalogs.BRENK)
            self.brenk_filter = FilterCatalog.FilterCatalog(params_brenk)
            
        except Exception as e:
            print(f"Warning: Could not initialize filters: {e}")
            self.pains_filter = None
            self.brenk_filter = None
    
    def _load_reference_data(self, data_path):
        """加载参考数据集用于应用域评估"""
        try:
            df = pd.read_csv(data_path)
            if 'smiles' in df.columns:
                reference_smiles = df['smiles'].dropna().unique()
                self.reference_fingerprints = []
                
                for smiles in reference_smiles:
                    mol = Chem.MolFromSmiles(smiles)
                    if mol:
                        # 使用带手性的Morgan指纹
                        fp = AllChem.GetMorganFingerprintAsBitVect(
                            mol, 2, nBits=2048, useChirality=True
                        )
                        self.reference_fingerprints.append(fp)
                
                print(f"Loaded {len(self.reference_fingerprints)} reference molecules for AD evaluation")
        except Exception as e:
            print(f"Warning: Could not load reference data: {e}")
            self.reference_fingerprints = None
    
    def validate_molecule(self, smiles):
        """
        完整的金标准验证流程
        
        Args:
            smiles: SMILES字符串
            
        Returns:
            dict: 验证结果
        """
        result = {
            'smiles': smiles,
            'is_valid': False,
            'validation_stages': {},
            'final_score': 0.0,
            'warnings': [],
            'errors': []
        }
        
        # 第1阶段：基础化学有效性检查
        stage1_result = self._stage1_basic_validity(smiles)
        result['validation_stages']['stage1'] = stage1_result
        
        if not stage1_result['passed']:
            result['errors'].extend(stage1_result['errors'])
            return result
        
        # 使用清理后的分子进行后续验证
        clean_mol = stage1_result['clean_mol']
        # clean_smiles就是原始输入的SMILES（如果通过验证）
        result['clean_smiles'] = smiles
        
        # 第2阶段：规则化化学可行性过滤
        stage2_result = self._stage2_chemical_feasibility(clean_mol)
        result['validation_stages']['stage2'] = stage2_result
        
        if not stage2_result['passed']:
            result['errors'].extend(stage2_result['errors'])
            result['warnings'].extend(stage2_result['warnings'])
            return result
        
        # 第3阶段：合成可行性评估
        stage3_result = self._stage3_synthetic_accessibility(clean_mol)
        result['validation_stages']['stage3'] = stage3_result
        
        if not stage3_result['passed']:
            result['warnings'].extend(stage3_result['warnings'])
        
        # 第4阶段：三维结构与物理稳定性验证
        stage4_result = self._stage4_3d_stability(clean_mol)
        result['validation_stages']['stage4'] = stage4_result
        
        if not stage4_result['passed']:
            result['warnings'].extend(stage4_result['warnings'])
        
        # 第5阶段：应用域评估
        stage5_result = self._stage5_applicability_domain(clean_mol)
        result['validation_stages']['stage5'] = stage5_result
        
        if not stage5_result['passed']:
            result['warnings'].extend(stage5_result['warnings'])
        
        # 计算最终评分
        stage_scores = [
            stage1_result['score'],
            stage2_result['score'],
            stage3_result['score'],
            stage4_result['score'],
            stage5_result['score']
        ]
        
        result['final_score'] = np.mean(stage_scores)
        result['is_valid'] = all([
            stage1_result['passed'],
            stage2_result['passed']
        ]) and result['final_score'] >= 0.6
        
        return result
    
    def _stage1_basic_validity(self, smiles):
        """第1阶段：基础化学有效性检查"""
        result = {
            'passed': False,
            'score': 0.0,
            'errors': [],
            'clean_mol': None
        }

        try:
            # 抑制RDKit警告
            import rdkit
            from rdkit import rdBase
            rdBase.DisableLog('rdApp.error')
            rdBase.DisableLog('rdApp.warning')

            # SMILES语法解析
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                result['errors'].append("Invalid SMILES syntax")
                return result

            # 化学合理化
            try:
                Chem.SanitizeMol(mol)
            except Exception as e:
                result['errors'].append(f"Sanitization failed: valence error")
                return result
            
            # 去除盐、溶剂和片段
            if '.' in smiles:
                # 保留最大片段
                fragments = smiles.split('.')
                largest_fragment = max(fragments, key=len)
                mol = Chem.MolFromSmiles(largest_fragment)
                if mol is None:
                    result['errors'].append("Largest fragment is invalid")
                    return result
            
            # 电荷中和
            try:
                from rdkit.Chem.MolStandardize import rdMolStandardize
                uncharger = rdMolStandardize.Uncharger()
                mol = uncharger.uncharge(mol)
            except:
                # 如果没有MolStandardize，跳过中和步骤
                pass
            
            result['clean_mol'] = mol
            result['passed'] = True
            result['score'] = 1.0
            
        except Exception as e:
            result['errors'].append(f"Stage 1 error: {str(e)}")
        
        return result
    
    def _stage2_chemical_feasibility(self, mol):
        """第2阶段：规则化化学可行性过滤"""
        result = {
            'passed': False,
            'score': 0.0,
            'errors': [],
            'warnings': []
        }
        
        score_components = []
        
        try:
            # 元素过滤
            atoms = [atom.GetSymbol() for atom in mol.GetAtoms()]
            forbidden_elements = set(atoms) - self.allowed_elements
            if forbidden_elements:
                result['errors'].append(f"Contains forbidden elements: {forbidden_elements}")
                return result
            score_components.append(1.0)
            
            # PAINS过滤
            if self.pains_filter:
                pains_matches = self.pains_filter.HasMatch(mol)
                if pains_matches:
                    result['warnings'].append("Contains PAINS substructure")
                    score_components.append(0.5)
                else:
                    score_components.append(1.0)
            else:
                score_components.append(1.0)
            
            # BRENK过滤
            if self.brenk_filter:
                brenk_matches = self.brenk_filter.HasMatch(mol)
                if brenk_matches:
                    result['warnings'].append("Contains BRENK substructure")
                    score_components.append(0.5)
                else:
                    score_components.append(1.0)
            else:
                score_components.append(1.0)
            
            # 物理化学性质过滤
            properties = {
                'MW': Descriptors.MolWt(mol),
                'LogP': Crippen.MolLogP(mol),
                'HBD': Lipinski.NumHDonors(mol),
                'HBA': Lipinski.NumHAcceptors(mol),
                'TPSA': Descriptors.TPSA(mol),
                'RotBonds': Descriptors.NumRotatableBonds(mol),
                'AromaticRings': Descriptors.NumAromaticRings(mol)
            }
            
            property_violations = 0
            for prop_name, value in properties.items():
                min_val, max_val = self.property_ranges[prop_name]
                if not (min_val <= value <= max_val):
                    result['warnings'].append(f"{prop_name} out of range: {value}")
                    property_violations += 1
            
            property_score = max(0, 1 - property_violations / len(properties))
            score_components.append(property_score)
            
            result['score'] = np.mean(score_components)
            result['passed'] = result['score'] >= 0.7
            
        except Exception as e:
            result['errors'].append(f"Stage 2 error: {str(e)}")
        
        return result
    
    def _stage3_synthetic_accessibility(self, mol):
        """第3阶段：合成可行性评估"""
        result = {
            'passed': False,
            'score': 0.0,
            'warnings': []
        }
        
        try:
            # 计算SA Score
            try:
                from rdkit.Chem import rdMolDescriptors
                sa_score = rdMolDescriptors.BertzCT(mol) / 100.0  # 简化的SA评分
            except:
                # 如果没有SA Score，使用分子复杂度作为替代
                sa_score = len(Chem.MolToSmiles(mol)) / 50.0
            
            if sa_score > self.sa_score_threshold:
                result['warnings'].append(f"High synthetic complexity: {sa_score:.2f}")
                result['score'] = max(0, 1 - (sa_score - self.sa_score_threshold) / 5.0)
            else:
                result['score'] = 1.0
            
            result['passed'] = result['score'] >= 0.5
            
        except Exception as e:
            result['warnings'].append(f"Stage 3 error: {str(e)}")
            result['score'] = 0.5
            result['passed'] = True  # 不因为SA计算失败而拒绝分子
        
        return result

    def _stage4_3d_stability(self, mol):
        """第4阶段：三维结构与物理稳定性验证"""
        result = {
            'passed': False,
            'score': 0.0,
            'warnings': []
        }

        try:
            # 3D构象生成能力
            mol_copy = Chem.Mol(mol)
            mol_copy = Chem.AddHs(mol_copy)

            # 尝试生成3D构象
            embed_result = AllChem.EmbedMolecule(mol_copy, randomSeed=42)
            if embed_result == -1:
                result['warnings'].append("Failed to generate 3D conformation")
                result['score'] = 0.3
                result['passed'] = False
                return result

            # 能量优化
            try:
                # 使用MMFF94力场优化
                AllChem.MMFFOptimizeMolecule(mol_copy)

                # 计算优化后的能量
                mmff_props = AllChem.MMFFGetMoleculeProperties(mol_copy)
                if mmff_props:
                    ff = AllChem.MMFFGetMoleculeForceField(mol_copy, mmff_props)
                    if ff:
                        energy = ff.CalcEnergy()

                        # 简化的能量评估（基于原子数归一化）
                        num_atoms = mol.GetNumAtoms()
                        energy_per_atom = energy / num_atoms if num_atoms > 0 else float('inf')

                        # 经验阈值：每原子能量不应超过50 kcal/mol
                        if energy_per_atom > 50:
                            result['warnings'].append(f"High strain energy: {energy_per_atom:.2f} kcal/mol per atom")
                            result['score'] = max(0, 1 - (energy_per_atom - 50) / 100)
                        else:
                            result['score'] = 1.0
                    else:
                        result['score'] = 0.7
                        result['warnings'].append("Could not calculate force field energy")
                else:
                    result['score'] = 0.7
                    result['warnings'].append("Could not get MMFF properties")

            except Exception as e:
                result['warnings'].append(f"Energy optimization failed: {str(e)}")
                result['score'] = 0.5

            result['passed'] = result['score'] >= 0.4

        except Exception as e:
            result['warnings'].append(f"Stage 4 error: {str(e)}")
            result['score'] = 0.5
            result['passed'] = True  # 不因为3D计算失败而拒绝分子

        return result

    def _stage5_applicability_domain(self, mol):
        """第5阶段：应用域评估"""
        result = {
            'passed': False,
            'score': 0.0,
            'warnings': []
        }

        try:
            if self.reference_fingerprints is None:
                result['warnings'].append("No reference data for AD evaluation")
                result['score'] = 0.5
                result['passed'] = True
                return result

            # 计算分子指纹
            mol_fp = AllChem.GetMorganFingerprintAsBitVect(
                mol, 2, nBits=2048, useChirality=True
            )

            # 计算与参考数据集的最大相似度
            max_similarity = 0.0
            for ref_fp in self.reference_fingerprints:
                from rdkit import DataStructs
                similarity = DataStructs.TanimotoSimilarity(mol_fp, ref_fp)
                max_similarity = max(max_similarity, similarity)

            if max_similarity < self.similarity_threshold:
                result['warnings'].append(f"Low similarity to reference data: {max_similarity:.3f}")
                result['score'] = max(0, max_similarity / self.similarity_threshold)
            else:
                result['score'] = 1.0

            result['passed'] = result['score'] >= 0.5

        except Exception as e:
            result['warnings'].append(f"Stage 5 error: {str(e)}")
            result['score'] = 0.5
            result['passed'] = True

        return result

    def validate_batch(self, smiles_list, save_results=True, output_file=None):
        """
        批量验证分子

        Args:
            smiles_list: SMILES字符串列表
            save_results: 是否保存结果
            output_file: 输出文件路径

        Returns:
            dict: 批量验证结果
        """
        results = []

        print(f"Validating {len(smiles_list)} molecules using gold standard protocol...")

        for i, smiles in enumerate(smiles_list):
            if (i + 1) % 100 == 0:
                print(f"Processed {i + 1}/{len(smiles_list)} molecules")

            result = self.validate_molecule(smiles)
            results.append(result)

        # 统计分析
        valid_count = sum(1 for r in results if r['is_valid'])

        stage_stats = {}
        for stage in ['stage1', 'stage2', 'stage3', 'stage4', 'stage5']:
            passed_count = sum(1 for r in results if r['validation_stages'][stage]['passed'])
            avg_score = np.mean([r['validation_stages'][stage]['score'] for r in results])
            stage_stats[stage] = {
                'passed_count': passed_count,
                'pass_rate': passed_count / len(results),
                'average_score': avg_score
            }

        batch_summary = {
            'total_molecules': len(smiles_list),
            'valid_molecules': valid_count,
            'validity_rate': valid_count / len(smiles_list),
            'average_final_score': np.mean([r['final_score'] for r in results]),
            'stage_statistics': stage_stats,
            'detailed_results': results
        }

        # 打印统计信息
        print(f"\n=== Gold Standard Validation Results ===")
        print(f"Total molecules: {batch_summary['total_molecules']}")
        print(f"Valid molecules: {batch_summary['valid_molecules']} ({batch_summary['validity_rate']:.1%})")
        print(f"Average final score: {batch_summary['average_final_score']:.3f}")

        print(f"\nStage-wise statistics:")
        stage_names = {
            'stage1': 'Basic Validity',
            'stage2': 'Chemical Feasibility',
            'stage3': 'Synthetic Accessibility',
            'stage4': '3D Stability',
            'stage5': 'Applicability Domain'
        }

        for stage, stats in stage_stats.items():
            print(f"  {stage_names[stage]}: {stats['passed_count']}/{len(results)} ({stats['pass_rate']:.1%}) - Avg Score: {stats['average_score']:.3f}")

        # 保存结果
        if save_results:
            if output_file is None:
                output_file = 'gold_standard_validation_results.pkl'

            with open(output_file, 'wb') as f:
                pickle.dump(batch_summary, f)

            # 保存CSV格式的简化结果
            csv_data = []
            for result in results:
                csv_row = {
                    'smiles': result['smiles'],
                    'clean_smiles': result.get('clean_smiles', result['smiles']),
                    'is_valid': result['is_valid'],
                    'final_score': result['final_score'],
                    'stage1_passed': result['validation_stages']['stage1']['passed'],
                    'stage2_passed': result['validation_stages']['stage2']['passed'],
                    'stage3_passed': result['validation_stages']['stage3']['passed'],
                    'stage4_passed': result['validation_stages']['stage4']['passed'],
                    'stage5_passed': result['validation_stages']['stage5']['passed'],
                    'warnings': '; '.join(result['warnings']),
                    'errors': '; '.join(result['errors'])
                }
                csv_data.append(csv_row)

            csv_file = output_file.replace('.pkl', '.csv')
            pd.DataFrame(csv_data).to_csv(csv_file, index=False)

            print(f"\nResults saved to:")
            print(f"  Detailed: {output_file}")
            print(f"  Summary: {csv_file}")

        return batch_summary

def main():
    """主函数 - 演示验证器使用"""
    # 示例SMILES用于测试
    test_smiles = [
        "C[C@H](O)C(=O)O",  # 乳酸
        "C[C@H](N)C(=O)O",  # 丙氨酸
        "C[C@@H](O)[C@H](O)C",  # 多手性中心
        "C[C@H]1CC[C@@H](O)C1",  # 环状手性分子
        "CCCCCCCCCCCCCCCCCCCCC",  # 长链烷烃（可能不合理）
        "C@@@H",  # 无效SMILES
    ]

    # 创建验证器
    validator = GoldStandardValidator()

    # 批量验证
    results = validator.validate_batch(test_smiles)

    print(f"\nValidation completed!")

if __name__ == "__main__":
    main()
