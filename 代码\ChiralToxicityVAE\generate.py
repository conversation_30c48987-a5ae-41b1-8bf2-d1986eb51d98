"""
毒性数据生成模块
"""

import torch
import pandas as pd
import numpy as np
import pickle
import os
from collections import Counter

from model import ChiralToxicityVAE
from data import DataProcessor
from molecular_validator import GoldStandardValidator

class ToxicityGenerator:
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        self.toxicity_endpoints = ['FishLC50', 'FishCT', 'DMCT', 'DMAT', 'AlaAT']

        # 抑制RDKit警告
        try:
            from rdkit import rdBase
            rdBase.DisableLog('rdApp.error')
            rdBase.DisableLog('rdApp.warning')
        except:
            pass

        # 初始化金标准验证器
        reference_data_path = config.get('reference_data_path', '../../数据/Chiral_AquaTox_scr.csv')
        self.validator = GoldStandardValidator(reference_data_path)
        
    def load_model_and_metadata(self):
        checkpoint = torch.load(self.config['model_path'], map_location=self.device)
        model_config = checkpoint['config']
        
        self.model = ChiralToxicityVAE(
            molecular_feature_dim=model_config['molecular_feature_dim'],
            toxicity_dim=model_config['toxicity_dim'],
            latent_dim=model_config['latent_dim'],
            hidden_dims=model_config['hidden_dims']
        ).to(self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        with open(self.config['metadata_path'], 'rb') as f:
            self.metadata = pickle.load(f)
        
        self.feature_names = self.metadata['feature_names']
        self.scaler = self.metadata['scaler']
        
    def generate_molecular_features(self, n_samples):
        return DataProcessor.generate_random_molecular_features(
            n_samples, self.config['molecular_feature_ranges'], self.scaler
        )

    def generate_smiles_from_features(self, molecular_features):
        """根据分子特征生成对应的SMILES字符串，只保留通过验证的分子"""
        valid_smiles_list = []
        valid_validation_results = []
        valid_features_list = []

        for features in molecular_features:
            # 从标准化特征中恢复原始值
            original_features = self.scaler.inverse_transform([features])[0]

            stereo_centers = int(max(1, original_features[0]))
            length = int(max(20, original_features[3]))
            carbon_count = int(max(5, original_features[4]))
            nitrogen_count = int(max(0, original_features[5]))
            oxygen_count = int(max(0, original_features[6]))

            # 尝试生成通过验证的SMILES（最多尝试10次）
            valid_smiles_found = False

            for attempt in range(10):
                # 生成基础SMILES结构
                candidate_smiles = self._construct_smiles(
                    stereo_centers, length, carbon_count,
                    nitrogen_count, oxygen_count
                )

                # 使用金标准验证器验证分子
                validation = self.validator.validate_molecule(candidate_smiles)

                # 只保留通过验证的分子
                if validation['is_valid']:
                    valid_smiles_list.append(candidate_smiles)
                    valid_validation_results.append(validation)
                    valid_features_list.append(features)
                    valid_smiles_found = True
                    break

            # 如果10次尝试都失败，记录警告但不添加任何分子
            if not valid_smiles_found:
                print(f"Warning: Failed to generate valid SMILES after 10 attempts for features: {original_features[:3]}")

        print(f"Generated {len(valid_smiles_list)} valid molecules out of {len(molecular_features)} attempts")
        return valid_smiles_list, valid_validation_results, valid_features_list

    def _construct_smiles(self, stereo_centers, length, carbon_count, nitrogen_count, oxygen_count):
        """构造手性SMILES字符串"""
        import random

        # 扩展的化学上安全的手性分子模板库
        templates = [
            # 简单手性分子（氨基酸类）
            "C[C@H](N)C(=O)O",      # L-丙氨酸
            "C[C@@H](N)C(=O)O",     # D-丙氨酸
            "CC[C@H](N)C(=O)O",     # L-氨基丁酸
            "CC[C@@H](N)C(=O)O",    # D-氨基丁酸
            "CCC[C@H](N)C(=O)O",    # 更长链氨基酸
            "CCC[C@@H](N)C(=O)O",   # 更长链氨基酸对映体

            # 简单手性醇
            "C[C@H](O)C(=O)O",      # L-乳酸
            "C[C@@H](O)C(=O)O",     # D-乳酸
            "CC[C@H](O)C(=O)O",     # 2-羟基丁酸
            "CC[C@@H](O)C(=O)O",    # 2-羟基丁酸对映体
            "CCC[C@H](O)C(=O)O",    # 更长链羟基酸
            "CCC[C@@H](O)C(=O)O",   # 更长链羟基酸对映体

            # 多手性中心分子（安全结构）
            "C[C@H](O)[C@H](O)C",   # 1,2-丙二醇
            "C[C@@H](O)[C@@H](O)C", # 1,2-丙二醇对映体
            "C[C@H](N)[C@H](O)C",   # 氨基醇
            "C[C@@H](N)[C@@H](O)C", # 氨基醇对映体
            "CC[C@H](O)[C@H](O)CC", # 更长链二醇
            "CC[C@@H](O)[C@@H](O)CC", # 更长链二醇对映体

            # 分支手性分子
            "CC(C)[C@H](N)C(=O)O",  # 缬氨酸
            "CC(C)[C@@H](N)C(=O)O", # 缬氨酸对映体
            "CC(C)[C@H](O)C(=O)O",  # 分支羟基酸
            "CC(C)[C@@H](O)C(=O)O", # 分支羟基酸对映体
            "CCC(C)[C@H](N)C(=O)O", # 更复杂分支
            "CCC(C)[C@@H](N)C(=O)O", # 更复杂分支对映体

            # 环状手性分子
            "C1CC[C@H](O)C1",       # 环戊醇
            "C1CC[C@@H](O)C1",      # 环戊醇对映体
            "C1CCC[C@H](N)C1",      # 环己胺
            "C1CCC[C@@H](N)C1",     # 环己胺对映体

            # 含芳环的手性分子
            "c1ccc(C[C@H](N)C(=O)O)cc1",  # 苯丙氨酸
            "c1ccc(C[C@@H](N)C(=O)O)cc1", # 苯丙氨酸对映体
            "c1ccc(C[C@H](O)C)cc1",       # 苯乙醇
            "c1ccc(C[C@@H](O)C)cc1",      # 苯乙醇对映体

            # 含杂原子的手性分子
            "C[C@H](S)C(=O)O",      # 含硫氨基酸
            "C[C@@H](S)C(=O)O",     # 含硫氨基酸对映体
            "CC[C@H](N)CCO",        # 氨基醇
            "CC[C@@H](N)CCO",       # 氨基醇对映体
            "C[C@H](O)C(=O)N",      # 酰胺
            "C[C@@H](O)C(=O)N",     # 酰胺对映体

            # 更复杂的多官能团分子
            "CC[C@H](O)[C@H](N)C(=O)O",   # 多官能团氨基酸
            "CC[C@@H](O)[C@@H](N)C(=O)O", # 多官能团氨基酸对映体
            "C[C@H](O)C[C@H](N)C",        # 分离的手性中心
            "C[C@@H](O)C[C@@H](N)C",      # 分离的手性中心对映体
        ]

        # 智能模板选择：根据分子特征选择最合适的模板
        suitable_templates = []

        # 首先根据手性中心数量筛选
        for template in templates:
            template_stereo_count = template.count('@')
            if template_stereo_count <= stereo_centers:
                suitable_templates.append(template)

        if not suitable_templates:
            suitable_templates = templates[:8]  # 使用更多简单模板

        # 根据原子组成进一步筛选
        best_templates = []
        for template in suitable_templates:
            template_n = template.count('N')
            template_o = template.count('O')
            template_s = template.count('S')

            # 计算模板与需求的匹配度
            match_score = 0
            if nitrogen_count > 0 and template_n > 0:
                match_score += 2
            if oxygen_count > 2 and template_o >= 2:
                match_score += 2
            if template.count('c') > 0:  # 芳香环
                match_score += 1
            if len(template) >= length * 0.5:  # 长度匹配
                match_score += 1

            best_templates.append((template, match_score))

        # 按匹配度排序，选择最佳模板
        best_templates.sort(key=lambda x: x[1], reverse=True)

        # 从前50%的最佳模板中随机选择
        top_half = max(1, len(best_templates) // 2)
        base_smiles = random.choice(best_templates[:top_half])[0]
        current_stereo = base_smiles.count('@')

        # 如果需要更多手性中心，使用更安全的方法
        if stereo_centers > current_stereo:
            additional_needed = min(stereo_centers - current_stereo, 2)  # 限制添加数量

            for _ in range(additional_needed):
                # 使用更安全的手性扩展模板
                if random.random() > 0.5:
                    # 在分子末端添加手性中心
                    if base_smiles.endswith("C"):
                        base_smiles = base_smiles[:-1] + "[C@H](O)C"
                    elif base_smiles.endswith("O"):
                        base_smiles = base_smiles + "[C@H](C)C"
                else:
                    # 在分子开头添加手性中心
                    if base_smiles.startswith("C"):
                        base_smiles = "[C@H](O)" + base_smiles[1:]

        # 保守的原子组成调整（避免破坏分子结构）
        # 只进行安全的替换，不添加可能导致价态错误的基团

        # 如果需要更多氮原子，优先选择含氮的模板
        current_nitrogen = base_smiles.count("N")
        if nitrogen_count > current_nitrogen and current_nitrogen == 0:
            # 如果当前没有氮原子，选择含氮模板
            nitrogen_templates = [t for t in templates if "N" in t]
            if nitrogen_templates:
                base_smiles = random.choice(nitrogen_templates)

        # 如果需要更多氧原子，优先选择含氧的模板
        current_oxygen = base_smiles.count("O")
        if oxygen_count > current_oxygen and current_oxygen < 2:
            # 如果氧原子不足，选择含更多氧的模板
            oxygen_rich_templates = [t for t in templates if t.count("O") >= 2]
            if oxygen_rich_templates:
                base_smiles = random.choice(oxygen_rich_templates)

        # 验证生成的SMILES是否有效
        if self._is_valid_smiles(base_smiles):
            return base_smiles
        else:
            # 如果无效，返回一个简单的有效手性分子
            fallback_molecules = [
                "C[C@H](O)C(=O)O",
                "C[C@H](N)C(=O)O",
                "CC[C@H](O)C(=O)O",
                "C[C@H](O)[C@H](O)C"
            ]
            return random.choice(fallback_molecules)

    def _is_valid_smiles(self, smiles):
        """快速检查SMILES是否有效"""
        try:
            from rdkit import Chem
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return False
            # 尝试进行化学合理化
            Chem.SanitizeMol(mol)
            return True
        except:
            return False

    def generate_enantiomer_pair(self, smiles):
        """生成对映异构体对"""
        # 检查输入是否有效
        if smiles is None or not isinstance(smiles, str) or len(smiles.strip()) == 0:
            print(f"Warning: Invalid SMILES input: {smiles}")
            # 返回一个默认的手性分子对
            return "C[C@H](O)C(=O)O", "C[C@@H](O)C(=O)O"

        # 更精确的对映异构体生成
        enantiomer = smiles.strip()

        # 方法1：翻转所有手性中心
        # 先处理@@H -> @H，再处理@H -> @@H
        temp_marker = "TEMP_MARKER"
        enantiomer = enantiomer.replace("@@H", temp_marker)
        enantiomer = enantiomer.replace("@H", "@@H")
        enantiomer = enantiomer.replace(temp_marker, "@H")

        # 方法2：如果没有H，处理单独的@符号
        if "@H" not in enantiomer:
            # 处理@]和@@]的情况
            enantiomer = enantiomer.replace("@@]", "TEMP_BRACKET")
            enantiomer = enantiomer.replace("@]", "@@]")
            enantiomer = enantiomer.replace("TEMP_BRACKET", "@]")

            # 处理@)和@@)的情况
            enantiomer = enantiomer.replace("@@)", "TEMP_PAREN")
            enantiomer = enantiomer.replace("@)", "@@)")
            enantiomer = enantiomer.replace("TEMP_PAREN", "@)")

        # 验证生成的对映异构体确实不同
        if enantiomer == smiles:
            # 如果相同，尝试另一种方法
            if "@" in smiles:
                # 简单地在第一个@后添加@
                first_at = smiles.find("@")
                if first_at != -1 and first_at + 1 < len(smiles) and smiles[first_at + 1] != "@":
                    enantiomer = smiles[:first_at + 1] + "@" + smiles[first_at + 1:]

        return smiles, enantiomer
    
    def create_condition_mask(self, condition):
        toxicity_values = []
        mask_values = []
        
        for val in condition:
            if val is not None:
                toxicity_values.append(float(val))
                mask_values.append(1.0)
            else:
                toxicity_values.append(0.0)
                mask_values.append(0.0)
        
        return np.array(toxicity_values), np.array(mask_values)
    
    def generate_for_condition(self, condition, n_samples):
        # 生成更多的候选分子，因为会有一些被过滤掉
        candidate_samples = n_samples * 5  # 增加候选分子数量以提高多样性

        # 使用多批次生成以增加多样性
        all_molecular_features = []
        all_generated_probs = []

        batch_size = max(50, candidate_samples // 4)  # 分批生成

        for batch_idx in range(0, candidate_samples, batch_size):
            current_batch_size = min(batch_size, candidate_samples - batch_idx)

            # 为每个批次生成不同的分子特征
            molecular_features = self.generate_molecular_features(current_batch_size)
            molecular_features_tensor = torch.FloatTensor(molecular_features).to(self.device)

            toxicity_condition, mask_condition = self.create_condition_mask(condition)

            toxicity_tensor = torch.FloatTensor(toxicity_condition).unsqueeze(0).repeat(current_batch_size, 1).to(self.device)
            mask_tensor = torch.FloatTensor(mask_condition).unsqueeze(0).repeat(current_batch_size, 1).to(self.device)

            with torch.no_grad():
                if any(val is not None for val in condition):
                    # 使用温度采样增加多样性
                    temperature = self.config.get('temperature', 1.2)
                    generated_samples = self.model.conditional_generate(
                        molecular_features_tensor,
                        toxicity_tensor,
                        mask_tensor,
                        n_samples=1,
                        temperature=temperature
                    )
                    generated_probs = generated_samples.squeeze(1)
                else:
                    # 无条件生成也使用温度采样
                    temperature = self.config.get('temperature', 1.2)
                    generated_probs, _ = self.model.generate(
                        molecular_features_tensor,
                        n_samples=1,
                        temperature=temperature
                    )

            all_molecular_features.append(molecular_features)
            all_generated_probs.append(generated_probs.cpu())

        # 合并所有批次的结果
        molecular_features = np.vstack(all_molecular_features)
        generated_probs = torch.cat(all_generated_probs, dim=0)

        # 添加噪声以增加多样性
        noise_scale = 0.1
        generated_probs = generated_probs + torch.randn_like(generated_probs) * noise_scale
        generated_probs = torch.clamp(generated_probs, 0, 1)  # 确保在[0,1]范围内

        generated_binary = (generated_probs > 0.5).float()

        # 生成对应的SMILES并验证，只保留通过验证的分子
        valid_smiles, valid_validation_results, valid_features = self.generate_smiles_from_features(molecular_features)

        # 如果通过验证的分子数量不足，补充到目标数量
        if len(valid_smiles) < n_samples:
            print(f"Warning: Only {len(valid_smiles)} valid molecules generated, target was {n_samples}")
            # 可以选择重新生成或者就用现有的数量

        # 截取到目标数量（如果超过的话）
        if len(valid_smiles) > n_samples:
            valid_smiles = valid_smiles[:n_samples]
            valid_validation_results = valid_validation_results[:n_samples]
            valid_features = valid_features[:n_samples]

        # 获取对应的毒性数据
        valid_indices = []
        for i, features in enumerate(molecular_features):
            if i < len(valid_features) and np.array_equal(features, valid_features[min(i, len(valid_features)-1)]):
                valid_indices.append(i)

        # 如果索引不够，使用前面的索引
        while len(valid_indices) < len(valid_smiles):
            valid_indices.append(valid_indices[-1] if valid_indices else 0)

        valid_indices = valid_indices[:len(valid_smiles)]

        return {
            'molecular_features': np.array(valid_features),
            'toxicity_probs': generated_probs[valid_indices].cpu().numpy(),
            'toxicity_binary': generated_binary[valid_indices].cpu().numpy(),
            'smiles': valid_smiles,
            'validation_results': valid_validation_results,
            'condition': condition
        }

    def generate_enantiomer_pairs_for_condition(self, condition, n_pairs):
        """为指定条件生成对映异构体对"""
        # 生成基础分子
        base_result = self.generate_for_condition(condition, n_pairs)

        # 为每个分子生成对映异构体
        enantiomer_pairs = []
        enantiomer_toxicity_probs = []
        enantiomer_toxicity_binary = []

        for i in range(n_pairs):
            # 获取原始分子
            original_smiles = base_result['smiles'][i]
            original_features = base_result['molecular_features'][i]
            original_tox_probs = base_result['toxicity_probs'][i]
            original_tox_binary = base_result['toxicity_binary'][i]

            # 生成对映异构体
            smiles_r, smiles_s = self.generate_enantiomer_pair(original_smiles)

            # 验证对映异构体（可选，为了性能考虑可以跳过）
            # validation_r = self.validator.validate_molecule(smiles_r)
            # validation_s = self.validator.validate_molecule(smiles_s)

            # 为对映异构体生成可能不同的毒性
            enantiomer_tox_probs, enantiomer_tox_binary = self._generate_enantiomer_toxicity(
                original_tox_probs, original_features
            )

            # 存储对映异构体对
            enantiomer_pairs.extend([smiles_r, smiles_s])
            enantiomer_toxicity_probs.extend([original_tox_probs, enantiomer_tox_probs])
            enantiomer_toxicity_binary.extend([original_tox_binary, enantiomer_tox_binary])

        # 扩展分子特征和验证结果（每个分子对应两个对映异构体）
        expanded_features = np.repeat(base_result['molecular_features'], 2, axis=0)
        expanded_validations = []

        for i in range(n_pairs):
            original_validation = base_result['validation_results'][i]
            # 为对映异构体创建相同的验证结果（假设它们具有相似的化学性质）
            enantiomer_validation = original_validation.copy()
            enantiomer_validation['smiles'] = enantiomer_pairs[i*2+1]
            expanded_validations.extend([original_validation, enantiomer_validation])

        return {
            'molecular_features': expanded_features,
            'toxicity_probs': np.array(enantiomer_toxicity_probs),
            'toxicity_binary': np.array(enantiomer_toxicity_binary),
            'smiles': enantiomer_pairs,
            'validation_results': expanded_validations,
            'condition': condition,
            'is_enantiomer_pair': True,
            'pair_indices': [(i*2, i*2+1) for i in range(n_pairs)]
        }

    def _generate_enantiomer_toxicity(self, original_toxicity, molecular_features):
        """为对映异构体生成可能不同的毒性"""
        import random

        # 对映异构体的毒性可能相同或不同
        enantiomer_toxicity = original_toxicity.copy()

        # 根据经验，对映异构体在某些终点可能有不同的毒性
        # 这里模拟这种差异
        for i in range(len(enantiomer_toxicity)):
            # 30%的概率对映异构体有不同的毒性
            if random.random() < 0.3:
                # 轻微调整毒性概率
                noise = random.uniform(-0.2, 0.2)
                enantiomer_toxicity[i] = max(0.0, min(1.0, enantiomer_toxicity[i] + noise))

        enantiomer_binary = (enantiomer_toxicity > 0.5).astype(int)

        return enantiomer_toxicity, enantiomer_binary
    
    def generate_all_conditions(self):
        all_results = []

        for i, condition in enumerate(self.config['target_conditions']):
            # 检查是否生成对映异构体
            if self.config.get('generate_enantiomers', False):
                # 生成对映异构体对（数量减半，因为每对包含两个分子）
                n_pairs = self.config['n_samples_per_condition'] // 2
                result = self.generate_enantiomer_pairs_for_condition(condition, n_pairs)
            else:
                # 常规生成
                result = self.generate_for_condition(
                    condition,
                    self.config['n_samples_per_condition']
                )

            result['condition_idx'] = i
            result['condition_name'] = self.format_condition_name(condition)

            all_results.append(result)

        self.generation_results = all_results
        return all_results
    
    def format_condition_name(self, condition):
        parts = []
        for i, val in enumerate(condition):
            endpoint = self.toxicity_endpoints[i]
            if val is not None:
                toxicity = "toxic" if val == 1 else "non_toxic"
                parts.append(f"{endpoint}_{toxicity}")
        
        if not parts:
            return "random"
        
        return "_".join(parts)
    
    def analyze_generated_data(self):
        analysis = {
            'total_samples': 0,
            'condition_stats': [],
            'endpoint_distributions': {},
            'toxicity_patterns': Counter(),
            'validation_stats': {
                'total_valid': 0,
                'total_stable': 0,
                'average_scores': {},
                'validation_distribution': Counter()
            }
        }
        
        all_toxicity_binary = []
        all_validation_results = []

        for result in self.generation_results:
            n_samples = len(result['toxicity_binary'])
            analysis['total_samples'] += n_samples
            
            condition_stats = {
                'condition': result['condition'],
                'condition_name': result['condition_name'],
                'n_samples': n_samples,
                'endpoint_stats': {}
            }
            
            for i, endpoint in enumerate(self.toxicity_endpoints):
                endpoint_data = result['toxicity_binary'][:, i]
                toxic_count = (endpoint_data == 1).sum()
                condition_stats['endpoint_stats'][endpoint] = {
                    'toxic_count': int(toxic_count),
                    'toxicity_rate': float(toxic_count / n_samples)
                }
            
            analysis['condition_stats'].append(condition_stats)
            all_toxicity_binary.extend(result['toxicity_binary'])
            all_validation_results.extend(result['validation_results'])
        
        all_toxicity_array = np.array(all_toxicity_binary)
        for i, endpoint in enumerate(self.toxicity_endpoints):
            endpoint_data = all_toxicity_array[:, i]
            toxic_count = (endpoint_data == 1).sum()
            analysis['endpoint_distributions'][endpoint] = {
                'toxic_count': int(toxic_count),
                'total_count': len(endpoint_data),
                'toxicity_rate': float(toxic_count / len(endpoint_data))
            }
        
        for toxicity_vector in all_toxicity_binary:
            pattern = tuple(toxicity_vector)
            analysis['toxicity_patterns'][pattern] += 1

        # 分析验证结果
        if all_validation_results:
            valid_count = sum(1 for v in all_validation_results if v['is_valid'])
            high_score_count = sum(1 for v in all_validation_results if v['final_score'] >= 0.8)

            analysis['validation_stats']['total_valid'] = valid_count
            analysis['validation_stats']['total_high_score'] = high_score_count
            analysis['validation_stats']['validity_rate'] = valid_count / len(all_validation_results)
            analysis['validation_stats']['high_score_rate'] = high_score_count / len(all_validation_results)

            # 平均评分
            final_scores = [v['final_score'] for v in all_validation_results]
            analysis['validation_stats']['average_scores']['final_score'] = np.mean(final_scores)

            # 各阶段平均评分
            for stage in ['stage1', 'stage2', 'stage3', 'stage4', 'stage5']:
                stage_scores = [v['validation_stages'][stage]['score'] for v in all_validation_results]
                analysis['validation_stats']['average_scores'][stage] = np.mean(stage_scores)

            # 验证等级分布
            for validation in all_validation_results:
                final_score = validation['final_score']
                if final_score >= 0.8:
                    grade = 'Excellent'
                elif final_score >= 0.6:
                    grade = 'Good'
                elif final_score >= 0.4:
                    grade = 'Fair'
                else:
                    grade = 'Poor'
                analysis['validation_stats']['validation_distribution'][grade] += 1

        self.analysis_results = analysis

        # 打印验证统计
        if 'validation_stats' in analysis:
            vs = analysis['validation_stats']
            print(f"Molecular Validation Results:")
            print(f"  Valid molecules: {vs['total_valid']}/{analysis['total_samples']} ({vs.get('validity_rate', 0):.1%})")
            print(f"  High-score molecules: {vs['total_high_score']}/{analysis['total_samples']} ({vs.get('high_score_rate', 0):.1%})")
            print(f"  Average final score: {vs['average_scores'].get('final_score', 0):.3f}")
            print(f"  Quality distribution: {dict(vs['validation_distribution'])}")

        return analysis

    def _calculate_tanimoto_similarity(self, smiles):
        """计算生成分子与原始数据集的最大Tanimoto相似性"""
        try:
            from rdkit import Chem
            from rdkit.Chem import AllChem, DataStructs

            # 生成分子的指纹
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return 0.0

            mol_fp = AllChem.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048, useChirality=True)

            # 如果有参考指纹，计算相似性
            if hasattr(self.validator, 'reference_fingerprints') and self.validator.reference_fingerprints:
                max_similarity = 0.0
                for ref_fp in self.validator.reference_fingerprints:
                    similarity = DataStructs.TanimotoSimilarity(mol_fp, ref_fp)
                    max_similarity = max(max_similarity, similarity)
                return max_similarity
            else:
                # 如果没有参考数据，返回默认值
                return 0.5

        except Exception as e:
            print(f"Warning: Failed to calculate Tanimoto similarity for {smiles}: {e}")
            return 0.0
    
    def save_results(self):
        output_dir = self.config['output_dir']
        os.makedirs(output_dir, exist_ok=True)
        
        all_data = []
        
        for result in self.generation_results:
            n_samples = len(result['toxicity_binary'])
            
            for i in range(n_samples):
                sample_data = {
                    'sample_id': f"generated_{result['condition_idx']}_{i:04d}",
                    'smiles': result['smiles'][i],
                    'chirality_count': result['smiles'][i].count('@'),  # 添加手性中心计数
                    'condition_idx': result['condition_idx'],
                    'condition_name': result['condition_name'],
                    'condition': str(result['condition'])
                }

                # 添加对映异构体信息
                if result.get('is_enantiomer_pair', False):
                    sample_data['is_enantiomer'] = True
                    # 找到对应的对映异构体
                    for pair_idx, (idx1, idx2) in enumerate(result['pair_indices']):
                        if i == idx1:
                            sample_data['enantiomer_pair_id'] = pair_idx
                            sample_data['enantiomer_type'] = 'R'
                            sample_data['enantiomer_partner_id'] = f"generated_{result['condition_idx']}_{idx2:04d}"
                            break
                        elif i == idx2:
                            sample_data['enantiomer_pair_id'] = pair_idx
                            sample_data['enantiomer_type'] = 'S'
                            sample_data['enantiomer_partner_id'] = f"generated_{result['condition_idx']}_{idx1:04d}"
                            break
                else:
                    sample_data['is_enantiomer'] = False
                    sample_data['enantiomer_pair_id'] = None
                    sample_data['enantiomer_type'] = None
                    sample_data['enantiomer_partner_id'] = None

                # 计算与原始数据集的Tanimoto相似性
                tanimoto_similarity = self._calculate_tanimoto_similarity(result['smiles'][i])
                sample_data['tanimoto_similarity'] = tanimoto_similarity

                for j, endpoint in enumerate(self.toxicity_endpoints):
                    sample_data[f'{endpoint}_prob'] = result['toxicity_probs'][i, j]
                    sample_data[f'{endpoint}_binary'] = int(result['toxicity_binary'][i, j])

                # 添加验证信息
                if 'validation_results' in result and i < len(result['validation_results']):
                    validation = result['validation_results'][i]
                    sample_data['is_valid'] = validation['is_valid']
                    sample_data['final_score'] = validation['final_score']
                    # clean_smiles就是通过验证的原始SMILES
                    sample_data['clean_smiles'] = result['smiles'][i]

                    # 各阶段通过状态
                    for stage in ['stage1', 'stage2', 'stage3', 'stage4', 'stage5']:
                        sample_data[f'{stage}_passed'] = validation['validation_stages'][stage]['passed']
                        sample_data[f'{stage}_score'] = validation['validation_stages'][stage]['score']

                    sample_data['warnings'] = '; '.join(validation['warnings'])
                    sample_data['errors'] = '; '.join(validation['errors'])
                else:
                    # 这种情况不应该发生，因为我们只保留通过验证的分子
                    sample_data['is_valid'] = True
                    sample_data['final_score'] = 1.0
                    sample_data['clean_smiles'] = result['smiles'][i]

                all_data.append(sample_data)
        
        df = pd.DataFrame(all_data)
        csv_path = os.path.join(output_dir, 'generated_toxicity_data.csv')
        df.to_csv(csv_path, index=False)
        
        if hasattr(self, 'analysis_results'):
            with open(os.path.join(output_dir, 'generation_analysis.pkl'), 'wb') as f:
                pickle.dump(self.analysis_results, f)
        
        with open(os.path.join(output_dir, 'generation_config.pkl'), 'wb') as f:
            pickle.dump(self.config, f)
        
        return output_dir
