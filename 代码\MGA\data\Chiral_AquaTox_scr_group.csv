smiles,group
CCOc1ccc2nc(S(N)(=O)=O)sc2c1,train
CCN1C(=O)NC(c2ccccc2)C1=O,train
CCCN(CC)C(CC)C(=O)Nc1c(C)cccc1C,train
CC(O)(P(=O)(O)O)P(=O)(O)O,train
CC(C)(C)OOC(C)(C)CCC(C)(C)OOC(C)(C)C,valid
O=S(=O)(Cl)c1ccccc1,train
O=C(O)Cc1cc(I)c(Oc2ccc(O)c(I)c2)c(I)c1,train
OC[C@H](O)[C@@H](O)[C@H](O)CO,train
NC(=O)c1ccc[n+]([C@@H]2O[C@H](COP(=O)([O-])OP(=O)(O)OC[C@H]3O[C@@H](n4cnc5c(N)ncnc54)[C@H](O)[C@@H]3O)[C@@H](O)[C@H]2O)c1,train
O=c1[nH]c(=O)n([C@H]2C[C@H](O)[C@@H](CO)O2)cc1I,test
CC(C)COC(=O)C(C)C,train
C=C(C)C(=O)OCCOC(=O)C(=C)C,train
Cl/C=C\C[N+]12CN3CN(CN(C3)C1)C2,train
O=C([O-])Cc1cccc2ccccc12,train
CCCCCCCCCCOCC(O)CN,valid
CCN(CC)C(=O)c1cccnc1,train
COc1cc(O)cc(O)c1,train
CCOc1ccc(S(=O)(=O)O)c2cccnc12,train
O=C(O)[C@H](O)c1ccccc1,train
Nc1ccc(/N=N/c2ccccc2)cc1,test
CN[C@@H]1C[C@@H](c2ccc(Cl)c(Cl)c2)c2ccccc21,train
CN1[C@H]2CC[C@@H]1C[C@H](OC(=O)c1cc(Cl)cc(Cl)c1)C2,train
CN(C)CCCN1c2ccccc2CCc2ccc(Cl)cc21,train
C#CCO,train
Nc1ccccc1S(=O)(=O)O,valid
CC(O)CC(C)(C)O,train
CC(C)(C)CC(C)(C)N,train
CC(=O)CC(C)C,train
CCCC(C)=O,train
Nc1nc2ccccc2[nH]1,test
Oc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl,train
c1ccc(-c2ccccc2)cc1,train
CNC(=O)Nc1ccc(Cl)c(Cl)c1,train
CC(=O)Nc1ccc(C)c(Cl)c1,train
CCCCNC(=S)NCCCC,valid
CCCCNC(=O)NCCCC,train
CC(C)N(c1ccccc1)C(C)C,train
CCc1cccc(C)c1,train
CC(C)CCCCCCCOP(OCCCCCCCC(C)C)Oc1ccccc1,train
CCCCCCCC/C=C\CCCCCCCC(=O)OC(CO)CO,test
CCCCCCCCCCC=CC1CC(=O)OC1=O,train
CC(C)C(Nc1ccc(C(F)(F)F)cc1Cl)C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1,train
CS(=O)(=O)NC(=O)c1cc(Oc2ccc(C(F)(F)F)cc2Cl)ccc1[N+](=O)[O-],train
CCOP(=S)(CC)Sc1ccccc1,train
CC/C=C\CCCCO,valid
C=C[C@H]1CN2CCC1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,train
CC(=O)CCC(C)=O,train
N#CCCNCCC#N,train
CCOc1ccc(N=Nc2ccc(C=Cc3ccc(N=Nc4ccc(OCC)cc4)cc3S(=O)(=O)[O-])c(S(=O)(=O)[O-])c2)cc1,train
O=C1c2ccccc2C(=O)C1c1ccc2cc(S(=O)(=O)[O-])cc(S(=O)(=O)[O-])c2n1,test
O=C(Nc1ccc2c(O)c(N=Nc3ccc(N=Nc4ccc(S(=O)(=O)[O-])cc4)cc3)c(S(=O)(=O)[O-])cc2c1)c1ccccc1,train
CSc1ccc2c(c1)C(N1CCN(C)CC1)Cc1ccccc1S2,train
COCCCC/C(=N\OCCN)c1ccc(C(F)(F)F)cc1,train
Cc1ccccc1CCO,train
Cc1nc(C)c(C)nc1C,valid
CC1=CC(O)CC(C)(C)C1,train
Cc1cnc(C)c(C)n1,train
CC(C)COC(=O)c1ccccc1,train
C=C(C)[C@@H]1CC=C(C)CC1,train
O=[N+]([O-])[O-].O=[N+]([O-])[O-].[Ca+2],test
Nc1ccc(N)c([N+](=O)[O-])c1,train
CC1COc2ccccc2N1,train
O=C(O)c1cc(Cl)cc(Cl)c1O,train
CCCCCCCCCCCC(=O)NCCCN(C)C,train
CC(C)CCCCCOC(=O)CCS,valid
O=[N+]([O-])c1cc([As](=O)(O)O)ccc1O,train
CCC(COC(=O)CCS)(COC(=O)CCS)COC(=O)CCS,train
C=CCOc1c(Br)cc(Br)cc1Br,train
F[B-](F)(F)F.[H+],train
CC(C)[C@H]1CC[C@H](C)C[C@@H]1O,test
C(=C/c1ccccc1)\c1ccccc1,train
Cc1ccc2c(ccc3ccccc32)c1,train
Cn1c(=O)c2c(ncn2CC2OCCO2)n(C)c1=O,train
C[C@H]1O[C@@H](n2cc(F)c(=O)[nH]c2=O)[C@H](O)[C@@H]1O,train
CN1CCc2cccc3c2[C@H]1Cc1ccc(O)c(O)c1-3,valid
COC(=O)C1=CCCN(C)C1,train
Cc1ccc(C(=O)O)cc1[N+](=O)[O-],train
Cc1cc(C(=O)O)ccc1[N+](=O)[O-],train
CCCC(CCC)C(=O)O[C@@H]1C[C@@H]2CC[C@H](C1)[N+]2(C)C,train
CCCCCC(C)O,test
O=C([O-])c1ccccc1O,train
NC(=O)c1ccccc1,train
CC(C)(C)[C@]1(O)CCN2C[C@H]3c4ccccc4CCc4cccc(c43)[C@@H]2C1,train
O=C1C(N(CO)C(=O)NCO)N(CO)C(=O)N1CO,train
OCCN1CCN(CCCN2c3ccccc3C=Cc3ccccc32)CC1,valid
CC(C)NC[C@@H](O)COc1ccc(CC(N)=O)cc1,train
CCNC(=O)N1CCN(CCCC(c2ccc(F)cc2)c2ccc(F)cc2)CC1,train
Nc1ccc([N+](=O)[O-])cc1N,train
[I-].[K+],train
O=C(C=Cc1ccc(O)c(O)c1)O[C@@H]1C[C@](O)(C(=O)O)C[C@@H](O)[C@H]1O,test
Oc1nc(Cl)c(Cl)cc1Cl,train
C/C=C/C=C/C=O,train
O=[N+]([O-])c1cc(C(F)(F)F)cc([N+](=O)[O-])c1Cl,train
C[Si](C)(C)N[Si](C)(C)C,train
C=CC(=O)OCCCl,valid
COCC(C)N(C(=O)CCl)c1c(C)csc1C,train
CN(C)CCn1nnnc1SCC1=C(C(=O)O)N2C(=O)[C@@H](NC(=O)Cc3csc(N)n3)[C@H]2SC1,train
C/C(=N\NC(=O)Nc1cc(F)cc(F)c1)c1ncccc1C(=O)[O-],train
CC1COC(Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,train
CCN(CC)CCOC(=O)C(Cc1cccc2ccccc12)CC1CCCO1,test
CCN[C@H]1C[C@H](C)S(=O)(=O)c2sc(S(N)(=O)=O)cc21,train
CCN[C@H]1CN(CCCOC)S(=O)(=O)c2sc(S(N)(=O)=O)cc21,train
CC(C)N(CCC(C(N)=O)(c1ccccc1)c1ccccn1)C(C)C,train
CC[C@H]1[C@@H]2C[C@H]3[C@@H]4N(C)c5ccccc5[C@]45C[C@@H](C2[C@H]5O)N3[C@@H]1O,train
CC1(C)O[C@@H]2C[C@H]3[C@@H]4C[C@H](F)C5=CC(=O)C=C[C@]5(C)[C@H]4[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1,valid
CSC(=O)c1c(C(F)F)nc(C(F)(F)F)c(C(=O)SC)c1CC(C)C,train
O=C(O)/C=C(\CC(=O)O)C(=O)O,train
CCCCCCCCCCCCCCCC(=O)O[C@@H]1CC(C)=C(/C=C/C(C)=C/C=C/C(C)=C/C=C\C=C(C)\C=C\C=C(C)\C=C\C2=C(C)C[C@@H](OC(=O)CCCCCCCCCCCCCCC)CC2(C)C)C(C)(C)C1,train
CC1=C(C(=O)Nc2ccccc2)S(=O)(=O)CCO1,train
CC(C)(C)C1CCC(=O)CC1,test
CN(C)[C@@H]1C(O)=C(C(=O)NCN2CCCC2)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)C3C[C@@H]12,train
CN1CCN=C(c2ccccc2)c2cc(Cl)ccc21,train
CN(C)CCc1c[nH]c2ccc(Cn3cncn3)cc12,train
CCCCC(=O)[O-],train
CCCCCCCCCCCCCC(=O)OC,valid
CCCCCCC(C)O,train
CCC[Si](OC)(OC)OC,train
CC1=C(CC=O)C(C)(C)CCC1,train
NCCNCCNCCN,train
C[C@]12CC[C@H]3[C@@H](CC[C@@]45O[C@@H]4C(O)=C(C#N)C[C@]35C)[C@@H]1CC[C@@H]2O,test
CCCC1COC(Cn2cncn2)(c2ccc(Cl)cc2Cl)O1,train
Cc1ccc(N)c(N)c1,train
CCCCCNCCCCC,train
COCC(C)O,train
c1ccc2c(c1)Oc1ccccc1S2,valid
CC1CN1,train
CCc1cnc(C2=NC(C)(C(C)C)C(=O)N2)c(C(=O)O)c1,train
Clc1ccc(C(Cn2ccnc2)OCc2c(Cl)cccc2Cl)c(Cl)c1,train
Clc1cnc(Oc2ccc(Oc3ncc(Cl)cc3Cl)cc2)c(Cl)c1,train
COc1ccccc1OCCNCC(O)COc1cccc2[nH]c3ccccc3c12,test
ClCOCCl,train
CC(O)CNCC(C)O,train
C[C@H](CCC(=O)[O-])[C@H]1CC[C@H]2[C@H]3[C@H](C[C@H](O)[C@@]21C)[C@@]1(C)CC[C@@H](O)C[C@H]1C[C@H]3O,train
CC(=O)[C@H]1[C@H](C#N)C[C@H]2[C@@H]3CC=C4C[C@@H](O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
O=[N+]([O-])c1ccc([As](=O)(O)O)cc1,valid
CCOC(=O)C1OC1c1ccccc1,train
ONc1ccccc1,train
O=CC(=O)c1ccccc1,train
[Cu]I,train
CCCCC(CC)CCC(CC(C)C)OS(=O)(=O)[O-],test
ClCc1ccc(Cl)cc1Cl,train
O=C(O)CCCCCCCCC(=O)O,train
CCCCCCCC(=O)OC,train
CC(O)COCC(C)O,train
O=C([O-])COc1nn(Cc2ccccc2)c2ccccc12,valid
Cc1ncc[nH]1,train
COc1ccc2sc(C(=O)Nc3nnn[n-]3)c(OC(C)C)c2c1,train
Oc1ccc2c(c1)OC[C@@H](N1CCC(O)(c3ccc(F)cc3)CC1)[C@H]2O,train
O=C(O)CCN(C1(C(=O)NO)CCCC1)S(=O)(=O)c1ccc(Oc2ccc(F)cc2)cc1,train
O=C(NO)C1(NS(=O)(=O)c2ccc(Oc3ccc(F)cc3)cc2)CCOCC1,test
Cc1nc(C)nc(N2C[C@H](C)N(c3ccnc([C@@H](C)O)n3)[C@H](C)C2)n1,train
CC[C@H](C)[C@@H](C(=O)O)n1sc2ccccc2c1=O,train
Cc1cc(SC2=C(O)C[C@@](CCc3ccc(N)cc3)(C(C)C)OC2=O)c(C(C)(C)C)cc1CO,train
CCn1nc(C)c2c1C(=O)NCC(c1ccc(O)cc1)=N2,train
C[C@]12C[C@H](O)[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)COP(=O)([O-])[O-],valid
CO[C@H]1C[C@H](O[C@@H]2[C@@H](C)C(=O)O[C@H](C)[C@H](C)[C@H](OC(C)=O)[C@@H](C)C(=O)[C@@]3(CO3)C[C@H](C)[C@H](O[C@@H]3O[C@H](C)C[C@H](N(C)C)[C@H]3OC(C)=O)[C@H]2C)O[C@@H](C)[C@@H]1OC(C)=O,train
CO[Si](C)(C)OC,train
CC(O)(c1ccc(Cl)cc1)c1ccc(Cl)cc1,train
CN(C)C(=O)Nc1ccc(Oc2ccc(Cl)cc2)cc1,train
CC(=O)c1ccccc1O,test
O=C(O)Cc1c(Cl)ccc(Cl)c1Cl,train
O=C(O)c1cccc(Cl)n1,train
CCCCCCCCCC=O,train
Cc1ccc(C(C)(C)C)cc1,train
BrCBr,valid
Nc1cc(Cl)cc(Cl)c1,train
CCCCCCCCCC(=O)O,train
CC(C)(C)c1cc([N+](=O)[O-])cc(C(C)(C)C)c1O,train
O.O.O.O.O.O.O=[N+]([O-])[O-].O=[N+]([O-])[O-].[Mg+2],train
CCCCCCCCCOS(=O)(=O)[O-],test
O=Cc1ccc(C(=O)O)cc1,train
CCC(Cl)CCl,train
CC(C)(O)c1ccccc1,train
O=C1CCCN1,train
ClCc1ccccc1Cl,valid
Cc1ccc([N+](=O)[O-])c([N+](=O)[O-])c1,train
N#CC1(N=NC2(C#N)CCCCC2)CCCCC1,train
C=CC(=O)OCCOC(=O)C=C,train
CCCC[P+](CCCC)(CCCC)CCCC,train
N#CCc1cccc(C(F)(F)F)c1,test
COc1cccc(Br)c1,train
CCCCCCCCNC,train
CCC1OCC(COc2ccc(Oc3ccccc3)cc2)O1,train
CC1=C(C(=O)Nc2ccccc2)SCCO1,train
CCCCN(CCCC)SN(C)C(=O)Oc1cccc2c1OC(C)(C)C2,valid
Cc1cc(OC(=O)N(C)C)nn1C(=O)N(C)C,train
Cc1ccc2c(Br)cc(Br)c(O)c2n1,train
O=c1c(O)c(-c2ccc(O)cc2)oc2cc(O)cc(O)c12,train
CC(O)COc1ccccc1,train
O=P1(NCCCl)OCCCN1CCCl,test
CC(C)O,train
CC(C)OC(=O)Nc1cccc(Cl)c1,train
CC(C)OC(=O)Nc1ccccc1,train
CC=Cc1ccc2c(c1)OCO2,train
CCCC(CCC)C(=O)[O-],valid
CC1COc2c(N3CCN(C)CC3)c(F)cc3c(=O)c(C(=O)O)cn1c23,train
CC(=O)Oc1cc(C)c(OC(C)=O)c2ccccc12,train
CCN(Cc1ccc(Cl)nc1)/C(=C/[N+](=O)[O-])NC,train
CC1CCC(C(C)C)C(OC(=O)c2ccccc2N)C1,train
O=C(c1ccccc1)c1cc(Cl)ccc1O,test
OC[C@]1(O)OC[C@@H](O)[C@H](O)[C@@H]1O,train
Cc1ccc(C=C2C(=O)C3CCC2C3(C)C)cc1,train
CC(C)C[P+](C)(CC(C)C)CC(C)C,train
C=C1C[C@]23CC[C@@H]4[C@](C)(C(=O)O[C@@H]5O[C@H](CO)[C@@H](O)[C@H](O)[C@H]5O)CCC[C@]4(C)[C@@H]2C[C@@H](O[C@@H]2O[C@H](CO)[C@@H](O)[C@H](O[C@@H]4O[C@H](CO)[C@@H](O)[C@H](O)[C@H]4O)[C@H]2OC2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)[C@@H]1C3,train
CCCCCCCC[P+](CCCCCCCC)(CCCCCCCC)CCCCCCCC,valid
CCCCC(CC)COC(=O)c1ccc(C(=O)OCC(CC)CCCC)c(C(=O)OCC(CC)CCCC)c1,train
O=c1n(CCO)c(=O)n(CCO)c(=O)n1CCO,train
Cc1cc(C)cc(OP(=O)(Oc2cc(C)cc(C)c2)Oc2cc(C)cc(C)c2)c1,train
O=P(OC(CCl)CCl)(OC(CCl)CCl)OC(CCl)CCl,train
O=c1n(CC2CO2)c(=O)n(CC2CO2)c(=O)n1CC1CO1,test
Cc1cc(-c2ccc(N=Nc3c(S(=O)(=O)[O-])cc4cc(S(=O)(=O)[O-])cc(N)c4c3O)c(C)c2)ccc1N=Nc1c(S(=O)(=O)[O-])cc2cc(S(=O)(=O)[O-])cc(N)c2c1O,train
O=C(O)c1ccc(O)cc1O,train
O=C1c2c(O)ccc([N+](=O)[O-])c2C(=O)c2c([N+](=O)[O-])ccc(O)c21,train
CC1=CC(C)(C)Nc2ccccc21,train
Cc1cc(=O)oc2cc(O)cc(O)c12,valid
CC(C)CNCC(C)C,train
CNC1(c2ccccc2Cl)CCCCC1=O,train
Cc1ccccc1OCC(O)CNCCOc1ccc(C(N)=O)cc1,train
O=c1oc2cc(O)ccc2c2oc3cc(O)ccc3c12,train
COc1ccc(-c2coc3cc(O)cc(O)c3c2=O)cc1,test
O=c1cc(-c2ccccc2)oc2cc(O)cc(O)c12,train
O=c1cc(-c2ccc(O)cc2)oc2cc(O)cc(O)c12,train
CCC(=O)N(c1ccccc1)C1CCN(CCc2ccccc2)CC1,train
O=C(O)CCC(=O)c1ccc(-c2ccccc2)cc1,train
CCOC(=O)C1=C(C)NC(C)=C(C(=O)OC)C1c1cccc(Cl)c1Cl,valid
NC(=O)OCC(COC(N)=O)c1ccccc1,train
CCNC(C)Cc1cccc(C(F)(F)F)c1,train
COC(=O)c1ccc(C)cc1C1=NC(=O)C(C)(C(C)C)N1,train
CCOP(=S)(NC(C)C)Oc1ccccc1C(=O)OC(C)C,train
CSc1nc(NC2CC2)nc(NC(C)(C)C)n1,test
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@@H]2C(=O)CO,train
CCC1(C)CC(=O)NC(=O)C1,train
O=C1NCN(c2ccccc2)C12CCN(CCCOc1ccc(F)cc1)CC2,train
NC(=S)NNC(N)=S,train
NC(=S)C(N)=S,valid
CC1CN1P(=O)(N1CC1C)N1CC1C,train
O=C(Oc1ccccc1)Oc1ccccc1,train
C[Hg]Cl,train
S=c1[nH]cnc2[nH]cnc12,train
[Hg+2],test
CCCCCCCCCCCCNC(=N)N,train
CN(C)CCN(Cc1cccs1)c1ccccn1,train
COc1nn(CSP(=S)(OC)OC)c(=O)s1,train
NC1=NCC2c3ccccc3Cc3ccccc3N12,train
CC(=O)[C@H]1CC[C@H]2[C@@H]3CC[C@H]4C[C@](C)(O)CC[C@]4(C)[C@H]3CC[C@]12C,valid
O=C([O-])CCC/C=C\C[C@H]1[C@@H](O)C[C@@H](O)[C@@H]1/C=C/[C@@H](O)COc1cccc(Cl)c1,train
O=C(O)Cc1ccc(CCNS(=O)(=O)c2ccc(Cl)cc2)cc1,train
NC(=O)c1cn(Cc2c(F)cccc2F)nn1,train
COc1ccc(C=CC(=O)OCCC(C)C)cc1,train
O=C(NC1CCN(CCc2c[nH]c3ccccc23)CC1)c1ccccc1,test
CCn1cc[n+](C)c1C.O=S(=O)([O-])C(F)(F)F,train
Clc1ccc2cc3ccccc3cc2c1,train
CCCCn1cc[n+](C)c1.F[B-](F)(F)F,train
F/C(COc1ccc2c(c1)[nH]c1ccccc12)=C1/CN2CCC1CC2,train
CC(C)Cc1ccc([C@@H](C)C(=O)NS(C)(=O)=O)cc1,valid
CCCCN(CCCC)C(=S)SSC(=S)N(CCCC)CCCC,train
CCC[n+]1ccn(C)c1C.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
Brc1c2ccccc2cc2ccccc12,train
CCO/C=C1\N=C(c2ccccc2)OC1=O,train
CNc1cc(OC)c(C(=O)N[C@H]2CCN(Cc3ccccc3)[C@H]2C)cc1Cl,test
CCN1CCCC1CNC(=O)c1cc(S(=O)(=O)CC)c(N)cc1OC,train
COc1cc2c(cc1OC)C1CC(=O)C(CC(C)C)CN1CC2,train
Cc1cc(C)cc(C(=O)OC2C[C@@H]3CC[C@H](C2)N3C)c1,train
CC[N+]1(C)CCCC1.O=S(=O)([O-])C(F)(F)F,train
COP(=O)(OC)SCn1c(=O)oc2cc(Cl)cnc21,valid
CNC(=O)/C=C(\C)OP(=O)(OC)OC,train
Cc1occc1SSc1ccoc1C,train
Cc1cc(C(F)(C(F)(F)F)C(F)(F)F)ccc1NC(=O)c1cccc(I)c1C(=O)NC(C)(C)CS(C)(=O)=O,train
CC=CC(=O)CC,train
CC1OCCC1=O,test
CC1CCCC(=O)C1=O,train
CC1=C(O)C(=O)OC1C,train
CCCCCc1ccco1,train
c1cnc2c(n1)CCCC2,train
CCCCc1ccc2cccc(S(=O)(=O)[O-])c2c1,valid
Cc1cc(S(=O)(=O)[O-])ccc1/N=N/c1c(O)ccc2ccccc12,train
Cc1ccc(N=Nc2c(O)ccc(N=Nc3ccc(S(=O)(=O)[O-])cc3)c2O)c(C)c1,train
Nc1cnn([C@@H]2O[C@H](CO)[C@@H](O)[C@H]2O)c(=O)n1,train
CCNc1nc(Cl)nc(NC(C)(C)C)n1,train
NS(=O)(=O)c1cc2c(cc1Cl)N=CNS2(=O)=O,test
Oc1c(Cl)cc(Cl)c2cccnc12,train
NC(=O)OCC(O)COc1ccc(Cl)cc1,train
CN(C)CCCN1c2ccccc2Sc2ccc(Cl)cc21,train
CCCCCCCCCCCCCCn1cc[n+](C)c1,train
O=[Cr](=O)([O-])O[Cr](=O)(=O)[O-],valid
O=P(Cl)(Cl)Cl,train
CCN(Cc1cccc(S(=O)(=O)[O-])c1)c1ccc(C(=C2C=CC(=[N+](CC)Cc3cccc(S(=O)(=O)[O-])c3)C=C2)c2ccccc2)cc1,train
CC(C)COC(=O)COc1cc(Cl)c(Cl)cc1Cl,train
O=C(OC[C@H]1O[C@@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@@H]1OC(=O)c1cc(O)c(O)c(OC(=O)c2cc(O)c(O)c(O)c2)c1)c1cc(O)c(O)c(OC(=O)c2cc(O)c(O)c(O)c2)c1,train
CN(C)CCOC(c1ccccc1)c1ccccc1,test
COC(=O)c1ccc(C)cc1,train
CN(C)CCCN1c2ccccc2C(C)(C)c2ccccc21,train
COc1ccc([C@@H]2Sc3ccccc3N(CCN(C)C)C(=O)[C@@H]2OC(C)=O)cc1,train
CC(=O)C=Cc1ccccc1,train
Cc1c[nH]c(=S)[nH]c1=O,valid
COc1ccc2cc1Oc1cc3c(cc1OC)CC[N+](C)(C)[C@H]3Cc1ccc(cc1)Oc1c(OC)c(OC)cc3c1[C@@H](C2)[N+](C)(C)CC3,train
CCCCC[C@H](O)/C=C/[C@H]1[C@H](O)CC(=O)[C@@H]1C/C=C\CCCC(=O)O,train
CCCCC[C@H](O)/C=C/[C@H]1[C@H](O)C[C@H](O)[C@@H]1C/C=C\CCCC(=O)O,train
CC12CCC(CC1)C(C)(C)O2,train
C=COCC1CCC(CO)CC1,test
CC(=O)/C=C/C1=C(C)CCCC1(C)C,train
CC(=O)NC1CCSC1=O,train
CC(C)(C)CC(C)(C)c1ccc(O)c(Cc2ccc(Cl)cc2Cl)c1,train
CC1COc2ccccc2N1C(=O)C(Cl)Cl,train
CC(N)CN,valid
CCC(C)O,train
CCCCC(CC)CNC(=N)NC(=N)NCCCCCCNC(=N)NC(=N)NCC(CC)CCCC,train
CC(O)CN,train
CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)[O-])=C(CSc3nc(=O)c([O-])nn3C)CS[C@H]12)c1csc(N)n1,train
O=c1oc2cc(O)ccc2s1,test
C=CCc1ccc(O)c(OC)c1,train
COC(=O)[C@@H](N)CCCN/C(N)=N/[N+](=O)[O-],train
CC1(S(=O)(=O)[O-])CC(=O)c2ccccc2C1=O,train
Cc1nnc2n1-c1sc(CCC(=O)N3CCOCC3)cc1C(c1ccccc1Cl)=NC2,train
C[C@H](N[C@H](CCc1ccccc1)C(=O)O)C(=O)N1CCC[C@H]1C(=O)O,valid
CCC(C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](CC=C(C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@H](NC(C)=O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)C=CC=C3CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,train
COc1c(Br)cc(Br)c(C)c1Br,train
C=C[C@H]1CN2CC[C@H]1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12.C=C[C@H]1CN2CC[C@H]1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,train
CN(C)CCc1c[nH]c2ccc(CS(=O)(=O)N3CCCC3)cc12,train
O=C1/C(=C2\Nc3ccc(S(=O)(=O)O)cc3C2=O)Nc2ccc(S(=O)(=O)O)cc21,test
CC(=O)O[Cr](O)OC(C)=O.CC(=O)O[Cr](O)OC(C)=O.CC(=O)O[Cr](OC(C)=O)OC(C)=O,train
CO[C@H]1CC(O[C@H]2C[C@H]([C@H]3O[C@](C)(O)[C@H](C)C[C@@H]3C)O[C@H]2[C@]2(C)CC[C@H]([C@]3(C)CC[C@]4(C[C@H](O)[C@@H](C)[C@@H]([C@@H](C)[C@@H]5O[C@](O)(CC(=O)[O-])[C@@H](C)[C@H](OC)[C@H]5OC)O4)O3)O2)O[C@@H](C)[C@@H]1OC,train
C=CC(=O)OCCn1c(=O)n(CCOC(=O)C=C)c(=O)n(CCOC(=O)C=C)c1=O,train
C=C(C)C(=O)OCCNC(C)(C)C,train
NS(=O)(=O)c1ccccc1OC(F)(F)F,valid
O=C=NCC1CCCC(CN=C=O)C1,train
C=C[C@H]1CN2CCC1C[C@@H]2[C@@H](O)c1ccnc2ccccc12,train
O=C(O)c1ccccc1O.Oc1cccc2cccnc12,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2OP(=O)(O)O,train
O=C(O)[C@@H](S)[C@H](S)C(=O)O,test
CN1CCN(CCCN2c3ccccc3Sc3ccc(C(F)(F)F)cc32)CC1,train
NS(=O)(=O)c1ccc(C(=O)O)cc1,train
O=C(CCS)OCC(COC(=O)CCS)(COC(=O)CCS)COC(=O)CCS,train
OCCOCCN1CCN(C2=Nc3ccccc3Sc3ccccc32)CC1,train
Cc1cc(/C=C/c2ccc3cc(N(C)C)ccc3[n+]2C)c(C)n1-c1ccccc1.Cc1cc(/C=C/c2ccc3cc(N(C)C)ccc3[n+]2C)c(C)n1-c1ccccc1.O=C([O-])c1cc2ccccc2c(Cc2c(O)c(C(=O)[O-])cc3ccccc23)c1O,valid
COc1ccc(CN(CCN(C)C)c2ccccn2)cc1,train
CN(C)C(=O)Oc1ccc[n+](C)c1,train
Cc1ncc(CO)c(CO)c1O,train
CCC1NC(=O)c2cc(S(N)(=O)=O)c(Cl)cc2N1,train
C=C[C@H]1CN2CCC1C[C@@H]2[C@@H](O)c1ccnc2ccc(OC)cc12,test
Brc1cc2ccccc2c2ccccc12,train
CC(C)(N)CO,train
CC(C)(CO)CO,train
O=S1(=O)CCCC1,train
O=[N+]([O-])C(CO)(CO)CO,valid
OCC(CO)(CO)COCC(CO)(CO)CO,train
O=[N+]([O-])OCCN(CCO[N+](=O)[O-])CCO[N+](=O)[O-],train
NC(CO)(CO)CO,train
O=C(Cl)c1cc(C(=O)Cl)cc(C(=O)Cl)c1,train
CO[Si](CCCS)(OC)OC,test
COc1cc2c3cc1Oc1cc(ccc1O)C[C@@H]1c4c(cc(OC)c(O)c4Oc4ccc(cc4)C[C@@H]3N(C)CC2)CC[N+]1(C)C,train
O=C(O[C@@H]1C[C@@H]2CC[C@H](C1)[N+]21CCCC1)C(O)(c1ccccc1)c1ccccc1,train
COc1cc(C(=O)NC2CCCNC2)cc(OC)c1OC,train
C[N+](C)=CCl,train
CC(=O)c1cccnc1,valid
O=S1(=O)OC(c2ccc([O-])cc2)(c2ccc(O)cc2)c2ccccc21,train
O=CN1CCOCC1,train
COC(=O)CCC(=O)O,train
NCc1cccnc1,train
CCCCCCCCn1sc(Cl)c(Cl)c1=O,test
Cc1cc(O)cc(C)c1Cl,train
O=[Zr](Cl)Cl,train
CSc1ccc2c(c1)N(CCC1CCCCN1C)c1ccccc1S2,train
c1ccc2cnncc2c1,train
COC(=O)c1ccc(CBr)cc1,valid
CN1CCc2cc(Cl)c(O)cc2[C@H]2c3ccccc3CC[C@@H]21,train
O=P(O)(OCc1ccccc1)OCc1ccccc1,train
S=C=NCc1ccccc1,train
Oc1ccc(Cl)cc1Cc1ccccc1,train
ClCc1ccccc1,test
OCc1ccccc1,train
CC(=O)OCc1ccccc1,train
COCCc1ccc(OCC(O)CNC(C)C)cc1.COCCc1ccc(OCC(O)CNC(C)C)cc1,train
CN(C)C(=N)NC(=N)N,train
CCCC[Sn](CCCC)(OC(C)=O)OC(C)=O,valid
C[NH+](C)CCC(c1ccccc1)c1cccc[nH+]1,train
CCOc1ccc(N)cc1,train
CC(C)=CCC[C@H](C)CCO,train
CCOc1cccc(N)c1,train
Nc1ccccc1C(=O)OCCc1ccccc1,test
CC(C)CC(O)CC(C)C,train
C=C(C)C(=O)OCCOP(=O)(O)OCCOC(=O)C(=C)C,train
c1ccc2c(c1)OCC(CN1CCCCC1)O2,train
C=CCN1CCCC1CNC(=O)c1cc(S(N)(=O)=O)cc(OC)c1OC,train
C=C(C)OC(C)=O,valid
Cc1cc(O)cc2c1O[C@](C)(CCC[C@H](C)CCC[C@H](C)CCCC(C)C)CC2,train
Cc1ncc(C[n+]2csc(CCOP(=O)(O)OP(=O)(O)O)c2C)c(N)n1,train
NC(=O)[C@H]1O[C@@H](O)[C@H](O)[C@@H](O)[C@@H]1O,train
O=C1CC[C@@H](C(=O)O)N1,train
CN1C(=S)CN=C(c2ccccc2)c2cc(Cl)ccc21,test
CC(C)(C)OC(=O)c1ncn2c1[C@@H]1CCCN1C(=O)c1c(Br)cccc1-2,train
CCC(Cc1c(I)cc(I)c(O)c1I)C(=O)O,train
CCOc1cc(NC(C)=O)ccc1C(=O)OC,train
CC(O)C#CC(C)O,train
COc1ccc(N)cc1N,valid
CC1(C)[C@@H](O[C@H]2O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]2O[C@@H]2O[C@H](C(=O)[O-])[C@@H](O)[C@H](O)[C@H]2O)CC[C@@]2(C)[C@H]1CC[C@]1(C)[C@@H]2C(=O)C=C2[C@@H]3C[C@@](C)(C(=O)O)CC[C@]3(C)CC[C@]21C,train
O=C1NC(=O)C(=O)C(=O)N1,train
CC(C)(C)c1cc(/C=C2\SC(=N)NC2=O)cc(C(C)(C)C)c1O,train
OCCCC1CCCCC1,train
Cc1cc2c3c(c1)C(c1ccccc1)=N[C@@H](NC(=O)c1ccncc1)C(=O)N3CC2,test
CCc1cc(C2=C(C(=O)[O-])N(c3ccccc3C(F)(F)F)S(=O)(=O)c3ccccc32)cc2c1OCO2,train
O=S(=O)([O-])c1ccc2c(/N=N\c3ccc(S(=O)(=O)[O-])c4ccccc34)c(O)c(S(=O)(=O)[O-])cc2c1,train
O=C=Nc1ccc(Cl)cc1,train
CC(C)OC(=O)c1ccccc1C(=O)OC(C)C,train
CO[C@H]1[C@H]([C@@]2(C)O[C@@H]2CC=C(C)C)[C@]2(CC[C@H]1OC(=O)/C=C/C=C/C=C/C=C/C(=O)O)CO2,valid
C1CCC2(CCCCO2)OC1,train
C[C@H]1O[C@H](O[C@@H]2[C@@H](O)[C@@H](O)[C@H](O)[C@H](O)[C@H]2O)[C@@H](N)C[C@@H]1NC(=N)C(=O)O,train
CN(C)CCCN1c2ccccc2CCc2ccccc21,train
CCCCOCCO,train
[O-][n+]1ccccc1[S-],test
CCCN(CCC)C(=O)SCC,train
O=S(=O)([O-])c1cccc2ccccc12,train
CC(C)C1=CC2=CC[C@H]3[C@](C)(C(=O)[O-])CCC[C@]3(C)[C@H]2CC1,train
CN(C)c1ccc(C(=O)c2ccc(N(C)C)cc2)cc1,train
N#CCCC#N,valid
Cc1ncc([N+](=O)[O-])n1CCO,train
Nc1c(CC(=O)[O-])cccc1C(=O)c1ccccc1,train
C[N+]1(C)[C@H]2CC[C@@H]1C[C@H](OC(=O)C(CO)c1ccccc1)C2,train
CC(Cl)(Cl)C(=O)O,train
CN[C@@H]1[C@H](O[C@H]2[C@H](O[C@@H]3[C@@H](NC(=N)N)[C@H](O)[C@@H](NC(=N)N)[C@H](O)[C@H]3O)O[C@@H](C)[C@]2(O)C=O)O[C@@H](CO)[C@H](O)[C@H]1O.CN[C@@H]1[C@H](O[C@H]2[C@H](O[C@@H]3[C@@H](NC(=N)N)[C@H](O)[C@@H](NC(=N)N)[C@H](O)[C@H]3O)O[C@@H](C)[C@]2(O)C=O)O[C@@H](CO)[C@H](O)[C@H]1O,test
O=C(CCl)CCl,train
CC(=O)C(Cl)Cl,train
CC(C)(c1ccccc1)c1ccc(O)cc1,train
Cc1cc(O)c2c(O)c3c(O)cccc3cc2c1,train
CCC(=O)[N-]S(=O)(=O)c1ccc(-c2c(-c3ccccc3)noc2C)cc1,valid
Cc1ccccc1N1CCN(CCc2nnc3n2CCCC3)CC1,train
C=Cc1ccc(S(=O)(=O)[O-])cc1,train
C[C@]12CC[C@@H]3c4ccc(OC(=O)N(CCCl)CCCl)cc4CC[C@H]3[C@@H]1CC[C@@H]2OP(=O)(O)O,train
CC1Cc2ccccc2N1NC(=O)c1ccc(Cl)c(S(N)(=O)=O)c1,train
CC(=O)OC(C)C(=O)OCC[N+](C)(C)C.CC(=O)OC(C)C(=O)OCC[N+](C)(C)C.O=S(=O)([O-])c1cccc2c(S(=O)(=O)[O-])cccc12,test
CC(=O)CC(=O)Nc1ccc2[nH]c(=O)[nH]c2c1,train
CCO[Si](C)(CCCOCC1CO1)OCC,train
O=[N+]([O-])c1cc(C(F)(F)F)c(Cl)c([N+](=O)[O-])c1Cl,train
CCCCOCCOCCOCCO,train
Cc1cccc(Cc2c[nH]cn2)c1C,valid
CCOC(=O)CC(=O)OCC,train
COc1ccc(CNCC(O)COc2ccc3[nH]c(=O)ccc3c2)cc1OC,train
COC(=O)C1=C(C)NC(COC(N)=O)=C(C(=O)OC(C)C)C1c1cccc(Cl)c1Cl,train
CCNC(=O)NCCCOc1cccc(CN2CCCCC2)c1,train
CC(=O)SCC(CC(=O)c1ccc(C)cc1)C(=O)O,test
CCOC(=O)Cn1cccc1-c1nc(-c2ccc(OC)cc2)c(-c2ccc(OC)cc2)s1,train
O=C(CCCN1CCN(c2ccc(F)cc2)CC1)NC1c2ccccc2CSc2ccccc21,train
CC(C)(C)NC[C@H](O)c1ccc(O)cc1Cl,train
CCCCC/C=C\C/C=C\CCCCCCCC(=O)NC(C)c1ccccc1,train
CC(NN)c1ccccc1,valid
CCN(C)C(=O)Oc1cccc([C@H](C)N(C)C)c1,train
CNC(=O)c1cc(Oc2ccc(NC(=O)Nc3ccc(Cl)c(C(F)(F)F)c3)cc2)ccn1,train
CCN(CC)C(C)C(=O)c1ccccc1,train
CCN1CC(CCN2CCOCC2)C(c2ccccc2)(c2ccccc2)C1=O,train
Cc1cccc(C(=O)O)c1[N+](=O)[O-],test
CCN(CC)CCOC(=O)C1(c2ccccc2)CCCC1.CCN(CC)CCOC(=O)C1(c2ccccc2)CCCC1.O=S(=O)(O)CCS(=O)(=O)O,train
Clc1ccccn1,train
CCC(=O)/C=C/C1C(C)=CCCC1(C)C,train
CC1CC(OC(=O)c2ccccc2O)CC(C)(C)C1,train
CCCCCCCCCO,valid
CCCCCCNCCCCCC,train
CCN(CC)c1ccc(N)cc1,train
ClCCCCl,train
CCCCCCOC(C)=O,train
CCCCC(CC)COC(=O)CCCCCCCCC(=O)OCC(CC)CCCC,test
CCOC(C)=O,train
NCCNCCN,train
CCOP(=O)(CC)OCC,train
Cc1c2oc3c(C)ccc(C(=O)N[C@@H]4C(=O)N[C@H](C(C)C)C(=O)N5CCC[C@H]5C(=O)N(C)CC(=O)N(C)[C@@H](C(C)C)C(=O)O[C@@H]4C)c3nc-2c(C(=O)N[C@@H]2C(=O)N[C@H](C(C)C)C(=O)N3CCC[C@H]3C(=O)N(C)CC(=O)N(C)[C@@H](C(C)C)C(=O)O[C@@H]2C)c(N)c1=O,train
NC(=O)CCCCC(N)=O,valid
CNC(=O)ON=CC(C)(C)SC,train
C=CCOc1ccc(CC(=O)O)cc1Cl,train
NN,train
N[C@@H](Cc1cnc[nH]1)C(=O)O,train
NNc1nc(-c2ccccc2)cs1,test
NNc1nc(-c2ccc(N)cc2)cs1,train
Cc1ccccc1CO[C@H]1C[C@]2(C(C)C)CC[C@@]1(C)O2,train
NNc1ccc(C(=O)O)cc1,train
CCCCCCOc1ccccc1C(=O)O,train
O=C(OCc1ccccc1)C(=O)OCc1ccccc1,valid
CCCSc1ccc2[nH]c(NC(=O)OC)nc2c1,train
CCc1cccc(C)c1N(C(=O)CCl)[C@@H](C)COC,train
O=C(O)c1ccccc1C(=O)Nc1cccc2ccccc12,train
CCC(=O)OC1(c2ccccc2)CCN(C)CC1,train
COc1cc(-c2ccc(=O)[nH]n2)ccc1OC(F)F,test
CCOc1ccc([N+](=O)[O-])cc1,train
CCCCN(CCCC)CCCOC(=O)c1ccc(N)cc1,train
CCCCOc1cc(C(=O)NCCN(CC)CC)c2ccccc2n1,train
CC(C)(c1cc(Br)c(OCC(Br)CBr)c(Br)c1)c1cc(Br)c(OCC(Br)CBr)c(Br)c1,train
CC(C)c1ccc(C(C)C)cc1,valid
COc1c2occc2cc2ccc(=O)oc12,train
COC(C)(C)C,train
COC(N)=O,train
COC(=O)NN,train
Cc1cccc(/N=N/c2ccc(N(C)C)cc2)c1,test
CCCc1nn(C)c2c(=O)[nH]c(-c3cc(S(=O)(=O)N4CCN(C)CC4)ccc3OCC)nc12.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
O=C(O)CNC(=O)c1ccccc1,train
CCCCN1CCCC[C@H]1C(=O)Nc1c(C)cccc1C,train
CC(C)(C)OC(=O)N1C[C@@H](F)C[C@H]1C(=O)O,train
CC(=O)Oc1c(C)cc(OCC(O)CNC(C)C)c(C)c1C,valid
O=C(OCC1CO1)C1CCCCC1C(=O)OCC1CO1,train
Oc1nc(O)nc(O)n1,train
N#CC1(O)CCCCC1,train
CCOC(=O)c1ncn2c1CN(C)C(=O)c1cc(F)ccc1-2,train
CNCCC(Oc1ccc(C(F)(F)F)cc1)c1ccccc1,test
C[C@]12C[C@H](O)[C@@]3(F)[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)CO,train
CCCCCCCCCCCCCCCCCCOCC(O)CO,train
OCCN1CCN(CCCN2c3ccccc3Sc3ccc(C(F)(F)F)cc32)CC1,train
CSCCC=O,train
CC(C)(C)C(=O)Cl,valid
CCCCC(CC)COC(=O)Cl,train
CCCCC(CC)C(=O)[O-],train
BrC1CCC(Br)C(Br)CCC(Br)C(Br)CCC1Br,train
COCN(COC)c1nc(N(COC)COC)nc(N(COC)COC)n1,train
O=Cc1cccc(Br)c1,test
CC(C)(C)CC(C)(C)c1ccc(O)c(-n2nc3ccccc3n2)c1,train
C/C=C/C=C/C(=O)[O-],train
Cc1cc(C)nc(Nc2ccccc2)n1,train
CC(C)C[C@@H](NC(=O)[C@@H](O)[C@H](N)Cc1ccccc1)C(=O)O,train
Nc1cnn(-c2ccccc2)c(=O)c1Cl,valid
CCOC(=O)COc1cc(-c2nn(C)c(OC(F)F)c2Cl)c(F)cc1Cl,train
CCCCCC=CCC(=O)OC,train
S=C1SCN(Cc2ccccc2)CN1Cc1ccccc1,train
CCCCCCCCC(=O)OCC,train
CCCCCCCC#N,test
CCCCCCCCCCCCCC(=O)OCC,train
CCCCCCC(O)CCOC(C)=O,train
C=CCCCCCCCCO,train
CC(=O)NC1(c2cccc(F)c2)CCN(CC[C@@]2(c3ccc(Cl)c(Cl)c3)CN(c3ccccc3)C(=O)CO2)CC1.O=C(O)CCC(=O)O,train
COCCOc1cc2cc(C(=O)NC3CCN(C(C)C)CC3)n(CC(=O)Nc3ccc(Cl)cn3)c2cn1,valid
O=C([O-])c1ccc2c(c1)nc(C(=O)NC1CCN(C3CC3)CC1)n2Cc1cc(-c2ccc(Cl)s2)on1,train
CCCOc1sc(C(=O)N2CCC(c3cc(CN)ccc3F)CC2)c(C)c1Br,train
CCOC(=O)c1ccc([C@H]2CC[C@H](NC[C@H](O)COc3ccc(O)c(NS(C)(=O)=O)c3)CC2)cc1,train
Nc1nc(N)c(-c2ccccc2)s1,train
Cc1cccc(C)c1NC(=O)CN(CC(=O)O)CC(=O)O,test
CCCCCCCCCCCCCCCC[n+]1ccccc1,train
COc1ccc(C(=O)N2CCN(CC(=O)N3CCOCC3)CC2)cc1,train
COc1cc(C=C2CCCC(=Cc3ccc(O)c(OC)c3)C2=O)ccc1O,train
CNC(=O)NC(O)C(Cl)(Cl)Cl,train
CCNC(=O)C1CC(C)CCC1C(C)C,valid
CCc1ccc(O)c(OC)c1,train
CCCCCCC/C=C/C=O,train
CC/C=C\CC/C=C/C(OCC)OCC,train
CS(=O)(=O)c1ccc(C(=O)C2C(=O)CCCC2=O)c(Cl)c1COCC(F)(F)F,train
CN(C)C1CSSSC1,test
c1ccc(CN(Cc2ccccc2)Cc2ccccc2)cc1,train
C[C@@H](O)C(=O)O,train
OC[C@H]1O[C@@H]2O[C@@H]3[C@@H](CO)O[C@H](O[C@@H]4[C@@H](CO)O[C@H](O[C@@H]5[C@@H](CO)O[C@H](O[C@@H]6[C@@H](CO)O[C@H](O[C@@H]7[C@@H](CO)O[C@H](O[C@@H]8[C@@H](CO)O[C@H](O[C@@H]9[C@@H](CO)O[C@H](O[C@H]1[C@H](O)[C@H]2O)[C@H](O)[C@H]9O)[C@H](O)[C@H]8O)[C@H](O)[C@H]7O)[C@H](O)[C@H]6O)[C@H](O)[C@H]5O)[C@H](O)[C@H]4O)[C@H](O)[C@H]3O,train
CC1=CC[C@@]23CC1C(C)(C)[C@@H]2CC[C@H]3C,train
O=C(OCCCCCOC(=O)c1ccccc1)c1ccccc1,valid
CCCCOCCOCCSC#N,train
O=C(N[C@H]1CN2CCC1CC2)c1ccc2c(c1)OCCO2,train
Nc1nc(Cl)nc2c1ncn2[C@H]1C[C@H](O)[C@@H](CO)O1,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)C(C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(OC)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O,train
COc1cc(N)c(Cl)cc1C(=O)NC1CCN(CCCOc2ccc(F)cc2)CC1OC,test
CCn1nc(C(=O)O)c(=O)c2cc3c(cc21)OCO3,train
C(=C/c1ccccc1)\CN1CCN(C(c2ccccc2)c2ccccc2)CC1,train
BrCCCBr,train
COc1ccc(C(Cl)=C(c2ccc(OC)cc2)c2ccc(OC)cc2)cc1,train
CCCCCCCCCCCCCOS(=O)(=O)[O-],valid
Cc1cc(C)nc(NS(=O)(=O)c2ccc(N)cc2)n1,train
O=S(=O)([O-])[O-].[Na+].[Na+],train
Cc1noc(NS(=O)(=O)c2ccc(N)cc2)c1C,train
COc1cc([C@H]2Oc3cc([C@H]4Oc5cc(O)cc(O)c5C(=O)[C@@H]4O)ccc3O[C@@H]2CO)ccc1O,train
O=S1(=O)CC=CC1,test
CCCCC1(COC(=O)CCC(=O)O)C(=O)N(c2ccccc2)N(c2ccccc2)C1=O,train
CNCCO,train
NS(=O)(=O)c1cc(Cl)c(Cl)c(S(N)(=O)=O)c1,train
CN(C)c1ccc2nc3ccc(N(C)C)cc3[s+]c2c1,train
C=CCc1ccc(OC)c(OC)c1,valid
CCN(CC)CCOC(=O)C1(C2CCCCC2)CCCCC1,train
CCOC(C)(C)C,train
COC=O,train
CNC(=O)C[C@@H](N)C(=O)N[C@@H](C(=O)N[C@@H]1C(=O)N2[C@@H](C(=O)O)C(C)(C)S[C@H]12)c1ccc(O)cc1,train
C#C[C@]1(OC(C)=O)CC[C@H]2[C@@H]3CCC4=C/C(=N/O)CC[C@@H]4[C@H]3CC[C@@]21CC,test
CC1=C(/C=C/C(C)=C/C=C/C(C)=C/C(=O)Oc2c(C)c(C)c3c(c2C)CC[C@@](C)(CCC[C@H](C)CCC[C@H](C)CCCC(C)C)O3)C(C)(C)CCC1,train
COC(=O)CCC[N+](C)(C)C,train
C[N+]1(CC2COC(c3ccccc3)(C3CCCCC3)O2)CCCCC1,train
CC[C@H](C)[C@H](NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](C)NC(=O)[C@H](CC(=O)O)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CO)NC(=O)[C@@H]1CCCN1C(=O)[C@@H]1CCC(=O)N1)C(=O)NCC(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CCSC)C(N)=O,train
CC(CC=O)CCCC(C)(C)O,valid
CCCCCCCCO[C@@H]1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,train
CC(NCCC(c1ccccc1)c1ccccc1)c1ccccc1,train
COc1ccc(C[C@H](N)C(=O)N[C@@H]2[C@@H](CO)O[C@@H](n3cnc4c(N(C)C)ncnc43)[C@@H]2O)cc1,train
CCN(CC)CCNC(=O)COc1ccc(OC)cc1,train
COc1ccccc1N1CCN(CCCC(=O)c2ccc(F)cc2)CC1,test
Cc1cc(-c2ccccc2)nnc1NCCN1CCOCC1,train
CC(=O)Nc1ccc(OC(=O)c2ccccc2O)cc1,train
CC(=O)Oc1ccc2c(c1)CC[C@@H]1[C@@H]2CC[C@]2(C)[C@@H](O)CC[C@@H]12,train
Cc1ccc(C(=O)C(C)CN2CCCCC2)cc1,train
CC(C)COC(=O)CCCCC(=O)OCC(C)C,valid
O=[N+]([O-])c1ccc(C=Cc2ccc([N+](=O)[O-])cc2S(=O)(=O)[O-])c(S(=O)(=O)[O-])c1,train
CCNC1(c2cccs2)CCCCC1=O,train
CCN(O)CC,train
O=C1CN=C(c2ccccc2)c2cc(Cl)ccc2N1,train
COc1cc(CC(C)N)c(OC)cc1Br,test
Cc1ccc2c(Cl)cc(Cl)c(O)c2n1,train
CN(C(=O)C(Cl)Cl)c1ccc(OC(=O)c2ccco2)cc1,train
CCCCCc1cc(O)c2c(c1)OC(C)(C)c1ccc(C)cc1-2,train
CN1CC[C@@]23CCCC[C@@H]2[C@@H]1Cc1ccc(O)cc13,train
CO[C@@]1(NC(=O)Cc2cccs2)C(=O)N2C(C(=O)[O-])=C(COC(N)=O)CS[C@@H]21,valid
COC(=O)CCc1ccc(OCC(O)CNC(C)C)cc1,train
C=CC(=O)NC(C)C,train
O=C(Nc1ccccc1SSc1ccccc1NC(=O)c1ccccc1)c1ccccc1,train
CCC(=O)OCC(=O)[C@@]1(OC(=O)CC)[C@H](C)C[C@H]2[C@H]3[C@H]([C@@H](O)C[C@@]21C)[C@@]1(C)C=CC(=O)C=C1C[C@H]3Cl,train
CC(C)c1cccc(C(C)C)c1O,test
CNC(C)CC1CCCCC1,train
Cc1c(C(C)C)c(=O)n(-c2ccccc2)n1C,train
[O-][Cl+3]([O-])([O-])[O-],train
CCN(C)N=O,train
CCCCCO,valid
O=NN(c1ccccc1)c1ccccc1,train
O=Nc1ccc(Nc2ccccc2)cc1,train
CCCN(CCC)N=O,train
C[N+](C)(C)C,train
O=NN1CCCCCCC1,test
COc1cc(OC)c(OC)cc1C=O,train
C=C(C)C(=O)OC(C)C,train
N=C(N)c1ccccc1,train
CCCC[N+](CCCC)(CCCC)CCCC,train
CC1CCN(CCCC(=O)c2ccc(F)cc2)CC1,valid
CC(=O)CC(=O)OC(C)(C)C,train
N=C(N)NN,train
OCCSSCCO,train
Cc1ccc(C(N)=O)cc1N,train
ClCCCCCCCl,test
CN1C(=O)CC(C)(c2ccccc2)C1=O,train
C[C@](N)(Cc1ccc(O)c(O)c1)C(=O)O,train
CCC(C)Nc1c([N+](=O)[O-])cc(C(C)(C)C)cc1[N+](=O)[O-],train
CN1CCN(C2=Nc3ccccc3Oc3ccc(Cl)cc32)CC1.O=C(O)CCC(=O)O,train
Nc1ccc2cc(S(=O)(=O)O)ccc2c1,valid
c1ccc2c(c1)cc1ccc3cccc4ccc2c1c34,train
Nc1ccc(-c2ccc(N)cc2)cc1,train
N#CC(CCc1ccc(Cl)cc1)(Cn1cncn1)c1ccccc1,train
[Ba+2],train
c1ccccc1,test
O=Cc1ccccc1,train
C=CCCC,train
CCOC(=O)CP(=O)(OCC)OCC,train
CC(C)(c1ccccc1)c1ccccc1,train
O=C(O)COCC(=O)O,valid
Clc1ccc(C(Cl)(Cl)Cl)cc1Cl,train
CCCCC,train
N#CC(C#N)=C(C#N)C#N,train
C1CCC2=NCCCN2CC1,train
O=C(O)c1c2ccccc2cc2ccccc12,test
O=C(CBr)c1ccccc1,train
C[N+](C)(C)CC(=O)[O-],train
Nc1nc(Cl)cc(Cl)n1,train
COc1ccc(C=O)cc1O,train
CCN(CC)CCNC(=O)c1ccc(N)cc1,valid
O=c1c(=O)c2cccc3cccc1c32,train
O=c1[nH][nH]c2ccccc12,train
CC1(C)C(=O)N(Cl)C(=O)N1Br,train
O=C1C(I)=CC(=C(c2cc(I)c([O-])c(I)c2)c2ccccc2C(=O)[O-])C=C1I,train
CN(CCOc1ccc(CC2SC(=O)NC2=O)cc1)c1ccccn1,test
CNC(=O)N(c1ccccc1)c1ccccc1,train
CN(CCO)CCO,train
CC1CCCC1,train
C=CC(=O)NCNC(=O)C=C,train
C[C@H](CN1CCOCC1)C(C(=O)N1CCCC1)(c1ccccc1)c1ccccc1,valid
N#CSCSC#N,train
Nc1ccccc1-c1ccccc1,train
Cc1ccc(C(=O)c2ccccc2)cc1,train
CCCCCCCCCCCCCC[P+](CCCC)(CCCC)CCCC,train
CC(C)(C)c1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,test
CS(=O)(=O)c1ccc(C2=C(c3ccccc3)C(=O)OC2)cc1,train
CC[C@@H](O)C(C[C@@H](C)N(C)C)(c1ccccc1)c1ccccc1,train
CCCCS,train
CC[C@H](O)C(C[C@H](C)N(C)C)(c1ccccc1)c1ccccc1,train
Brc1c(NC2=NCCN2)ccc2nccnc12,valid
C[C@]12C=CC3=C4CCC(=O)C=C4CC[C@H]3[C@@H]1CC[C@@H]2O,train
C[S+](C)CCC(N)C(=O)O,train
NC(=O)OC[C@@H]1[C@H](NC(=O)/C(=N\OCC(=O)[O-])c2csc(N)n2)C(=O)N1S(=O)(=O)[O-],train
Cc1ccc(C(C)(C)c2ccc(C)cc2)cc1,train
COc1ccc(C(=O)c2ccc(C)cc2)c(O)c1,test
CCCCCCOC(=O)c1cccnc1,train
C/C(=N\Cc1ccccc1)c1ccccc1O,train
CCCC(C)(COC(N)=O)COC(=O)NC1CC1,train
CN1C(=O)CN2CCc3ccccc3C2c2cc(Cl)ccc21,train
CCCCNC(=O)NS(=O)(=O)c1ccc(C)cc1,valid
CCCCCC[C@@H](O)C/C=C\CCCCCCCC(=O)OCC(O)CO,train
CCCCCCCC(=O)OCC(O)CO,train
CCCCCCCCCCCCCC(=O)OCC(O)CO,train
Cc1ccc(S(=O)(=O)NC(=O)NN2CCCCCC2)cc1,train
C=CCN(CC=C)C(=O)C(Cl)Cl,test
CCCCCCCCCCCCCCCCO,train
C=CCc1ccc(OC)cc1,train
C=CC(=O)OC,train
O=C1c2cccc(O)c2C(=O)c2c(O)cc(CO)cc21,train
C1CSCCS1,valid
CN(C)C(=O)C(c1ccccc1)c1ccccc1,train
OC[C@H]1O[C@@H]2O[C@@H]3[C@@H](CO)O[C@H](O[C@@H]4[C@@H](CO)O[C@H](O[C@@H]5[C@@H](CO)O[C@H](O[C@@H]6[C@@H](CO)O[C@H](O[C@@H]7[C@@H](CO)O[C@H](O[C@H]1[C@H](O)[C@H]2O)[C@H](O)[C@H]7O)[C@H](O)[C@H]6O)[C@H](O)[C@H]5O)[C@H](O)[C@H]4O)[C@H](O)[C@H]3O,train
CCCCCC[C@@H](O)C/C=C\CCCCCCCC(=O)OC,train
Nc1ncn([C@H]2C[C@H](O)[C@@H](CO)O2)c(=O)n1,train
CC(=O)CC(=O)Nc1ccc(C)cc1C,test
COc1cc(Cl)c(OC)cc1N,train
CCOC(=O)c1ccccc1C,train
C[Si](C)(C)O[Si](C)(C)O[Si](C)(C)C,train
CCCCCCCCOC(=O)c1cc(O)c(O)c(O)c1,train
CC[Sn](Br)(CC)CC,valid
C=CC(=O)OCCOc1ccccc1,train
Cc1cc(C)c(N)c(Cl)c1,train
CCC(C)(C)C1CCC(O)CC1,train
CCCOC(=O)c1ccccc1,train
CCC(CO)(CO)COCC(CC)(CO)CO,test
N=C1NC(=N)c2ccccc21,train
C=C(C)C(=O)OCCCCOC(=O)C(=C)C,train
CCCCCCCCCCCC(=O)N(CC)CC,train
N#CCCOCCOCCC#N,train
C=CC(=O)OCCCCCCCCCC,valid
Cc1ccc(N(CCO)CCO)cc1,train
CN(C)CCN(C)CCO,train
C(COCC1CO1)OCC1CO1,train
CCC(=O)Nc1cccc(N)c1,train
S=C(Nc1ccccc1)Nc1ccccc1,test
O=C(N[C@H]1N=C(c2ccccc2F)c2cccc3c2N(CC3)C1=O)c1cc2ccccc2[nH]1,train
CCOC(=O)Cc1cccc(-c2cc(CCCCCCc3ccc(O[C@H]4O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]4OC(C)=O)c(-c4cccc(CC(=O)OCC)c4)c3)ccc2O[C@H]2O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]2OC(C)=O)c1,train
CCc1cc(S(=O)(=O)[O-])c2cc(C(C)C)cccc1-2,train
O=C(N[C@@H]1N=C(c2ccccc2F)c2cccc3c2N(CC3)C1=O)c1cc2ccccc2[nH]1,train
CCC(C(=O)NC(=O)NC(C)=O)c1ccccc1,valid
O=C(O)CNC(=O)CNC(=O)CNC(=O)CSC(=O)c1ccccc1,train
Nc1ccn(C2CO[C@H](CO)O2)c(=O)n1,train
CCN1CCN(c2cc(-c3ccc(F)cc3)c3c(n2)CCCCCC3)CC1,train
CCOC(=O)OCC/C(SC(=O)OCC)=C(\C)N(C=O)Cc1cnc(C)nc1N,train
CC[C@]1(O)C[C@@H]2CN(CCc3c([nH]c4ccccc34)[C@@](C(=O)OC)(c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(=O)OC)[C@H](OC(C)=O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)C2)C1,test
CCC(=O)c1ccc2c(c1)N(CCCN1CCN(CCO)CC1)c1ccccc1S2,train
OC(CCN1CCCCC1)(c1ccccc1)C1CC2C=CC1C2,train
CCOP(=O)(OCC)OCC,train
Nc1ccc(F)cc1,train
Oc1ccc(Cl)cc1Sc1cc(Cl)ccc1O,valid
CCC(C)n1c(=O)[nH]c(C)c(Br)c1=O,train
CCOc1cc(OCC)c(C(C)=O)cc1OCC,train
CC(=O)OCCOCCOCCOC(C)=O,train
OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
Cn1cnc2c1c(=O)[nH]c(=O)n2C,test
CCN(CC)c1ccc2c(C)cc(=O)oc2c1,train
CCN(CC)c1cccc(O)c1,train
Oc1ccc2ccccc2c1/N=N/c1ccccc1,train
NC(=S)Nc1ccccc1,train
Nc1c2ccccc2nc2ccccc12,valid
CC[N+](CC)(CC)CCC(O)(c1ccccc1)C1CCCCC1,train
CNP(=O)(OC)Oc1ccc(C(C)(C)C)cc1Cl,train
CC(=O)NNc1ccccc1,train
CC(=O)C1C(=O)C=C(C)OC1=O,train
Nc1ccc(Nc2ccccc2)cc1,test
C/C=C(C(=C/C)/c1ccc(O)cc1)\c1ccc(O)cc1,train
NNCCO,train
Cc1ncc(C[n+]2csc(CCO)c2C)c(N)n1,train
CCOc1ccc(NC(=O)CC(C)O)cc1,train
CCOP(=S)(OCC)SCSCC,valid
C=CC(=O)OCCN(C)C,train
Nc1ccc2cc3ccccc3cc2c1,train
CN(C)CCCOc1nn(Cc2ccccc2)c2ccccc12,train
CCN1CCCC(OC(=O)C(c2ccccc2)c2ccccc2)C1,train
CCCCC1C(=O)N(c2ccccc2)N(c2ccc(O)cc2)C1=O,test
CCOCC(C)O,train
CN(C)CCCN,train
O=C1CCCc2ccccc21,train
OC1CCCc2ccccc21,train
c1ccc(N=NNc2ccccc2)cc1,valid
O=C(O)C1(C(=O)Nc2ccc(Cl)cc2Cl)CC1,train
COC(C)OC,train
CCc1ccc(C(c2ccc(CC)cc2)C(Cl)Cl)cc1,train
CC(O)c1ccccc1,train
Clc1cccc(-c2cc(Cl)cc(Cl)c2)c1,test
Clc1ccccc1-c1nnc(-c2ccccc2Cl)nn1,train
CCOc1ccc(NC(N)=O)cc1,train
CCOc1ccccc1C(N)=O,train
CCOc1ccc2c(c1)C(C)=CC(C)(C)N2,train
C=CC(=O)OCC,valid
CN1CCCC1=O,train
CNc1ncnc2[nH]cnc12,train
CCOC(=O)C(C)Oc1ccc(Oc2cnc3cc(Cl)ccc3n2)cc1,train
COP(=S)(OC)Oc1ccc([N+](=O)[O-])cc1,train
COc1cc(N)ccc1C,test
CC(=O)c1ccccc1,train
Oc1ccc(Cl)cc1Cc1cc(Cl)ccc1O,train
Nc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],train
CC(C)N(CCO)C(C)C,train
CCC(C)=NO,valid
CCC(=O)CC,train
CC[N+](CC)(CC)CCOc1cccc(OCC[N+](CC)(CC)CC)c1OCC[N+](CC)(CC)CC,train
OC[C@H]1OC(O)[C@H](O)[C@@H](O)[C@H]1O,train
CCCCc1ccc(C(=O)O)nc1,train
C/C(=C(/CCO)SSCC1CCCO1)N(C=O)Cc1cnc(C)nc1N,test
CCCC(C)CC,train
CCOP(=O)([O-])C(N)=O,train
O=C(Cl)c1cc(Cl)cc(Cl)c1,train
COC(=O)c1cccc(Cl)c1,train
C=C(C)C(=O)OCCN(C)C,valid
Cc1cc(=O)nc(C(C)C)[nH]1,train
CC1(C)CC(N)CC(C)(CN)C1,train
CCCOCCO,train
COC(=O)CCS,train
CCCCOC(=O)[C@@H](C)Oc1ccc(Oc2ccc(C(F)(F)F)cn2)cc1,test
CC(C)NCC(O)COc1ccccc1-n1cccc1,train
OC[C@H]1O[C@@H](n2cnc3c(S)ncnc32)[C@H](O)[C@@H]1O,train
NC(=O)c1ncn([C@@H]2O[C@H](CO)[C@@H](O)[C@H]2O)c1N,train
N=C(N)NN.N=C(N)NN,train
CCC(COC(=O)c1cc(OC)c(OC)c(OC)c1)(c1ccccc1)N(C)C,valid
CC[C@H](NC(C)C)[C@H](O)c1ccc(O)c2[nH]c(=O)ccc12,train
CCCCCCC1(C)CCC(=O)O1,train
OC[C@H]1OC(S[Au])[C@H](O)[C@@H](O)[C@@H]1O,train
CCCCCCC#CC(=O)OC,train
CCCCCC/C=C/C(=O)OC,test
CCCCCCCC(=O)OCCCCCC,train
CCCCCCCC/C=C\CCCCCCCC(=O)OCC,train
C/C=C/C=C/CO,train
CCN(CC)CC1CCCCN1CC(=O)N1c2ccccc2C(=O)Nc2cccnc21,train
CC1(C)Oc2ccc(C#N)cc2[C@@H](N2CCCC2=O)[C@@H]1O,valid
O=C(O)CCCCCCC(=O)O,train
CCOC(=O)[C@]1(c2ccccc2)Oc2ccccc2[C@@H]1N,train
CCCN1C=CN(C)C1,train
Cc1ccccc1/N=N/c1ccc(/N=N/c2c(O)ccc3ccccc23)c(C)c1,train
CCCCCCCCCCCCCBr,test
Cc1ncsc1CCCl,train
CCN(CC)CCOc1ccc(C(=C(Cl)c2ccccc2)c2ccccc2)cc1,train
Clc1cccc(Cl)c1N=C1NCCN1,train
CC1CCCC(C)N1NC(=O)c1ccc(Cl)c(S(N)(=O)=O)c1,train
Nc1ccc2ccccc2c1S(=O)(=O)O,valid
O=[N+]([O-])c1ccc(Cl)cc1,train
CCCCOC(O)CC,train
CCCCCCCCCCCCOS(=O)(=O)[O-],train
CCCCC(CC)COS(=O)(=O)[O-],train
CCOC(=O)C1=NOC(c2ccccc2)(c2ccccc2)C1,test
O=C(Nc1ccc([N+](=O)[O-])cc1Cl)c1cc(Cl)ccc1O,train
CC(C)(C)C(O)/C(=C\c1ccc(Cl)cc1Cl)n1cncn1,train
Cc1cc([N+](=O)[O-])ccc1N,train
Cc1c(N)cccc1[N+](=O)[O-],train
CCCCC(=O)O[C@]1(C(=O)CO)[C@H](C)C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,valid
CC(C)(CO)[C@@H](O)C(=O)NCCCO,train
CC(=O)OCC(=O)[C@@]1(O)[C@H](C)C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
C/C=C(\C)C(=O)OC/C=C(\C)CCC=C(C)C,train
N[C@@H](Cc1ccc(Oc2cc(I)c(O)c(I)c2)c(I)c1)C(=O)O,train
CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)[O-])=C(C[n+]3cccc4c3CCC4)CS[C@H]12)c1csc(N)n1,test
CC/C(=C(/CC)c1ccc(OP(=O)(O)O)cc1)c1ccc(OP(=O)(O)O)cc1,train
CCN(CC)CCO[C@H]1CC[C@@]2(C)C(=CC[C@H]3[C@@H]4CCC(=O)[C@@]4(C)CC[C@@H]32)C1,train
COC(CNC(=O)c1ccccc1OCC(=O)O)C[Hg]O,train
O=C(C=Cc1ccccc1)OCCCc1ccccc1,train
CC1=C(C=O)C(C)(C)CCC1,valid
CCCCCOC(=O)CCCC,train
CCCc1ccccc1,train
CC(C)=CC(=O)O,train
CCCCCCCCC(=O)Cl,train
CN1CCCC1,test
N=C(NCCCCCCNC(=N)NC(=N)Nc1ccc(Cl)cc1)NC(=N)Nc1ccc(Cl)cc1,train
CC(=O)Nc1cc([As](=O)(O)O)ccc1O,train
OCc1ccccc1O,train
CC=CC=CCOC(=O)C(C)C,train
C[n+]1c2cc(N)ccc2cc2ccc(N)cc21.Nc1ccc2cc3ccc(N)cc3nc2c1,valid
Oc1c(Br)cc(Br)c2cccnc12,train
CNCCCN1c2ccccc2CCc2ccccc21,train
CCC(C)OC(=O)N1CCCCC1CCO,train
Cc1ccc(NC(=O)c2ccc(CN3CCN(C)CC3)cc2)cc1Nc1nccc(-c2cccnc2)n1,train
CCCCCC1CCC(=O)O1,test
CC/C=C\CCOC(C)OCC,train
CCCOc1ccc(C(=O)OCCN(CC)CC)cc1N,train
COC(=O)C(Cl)C(=O)OC,train
NCCC[C@H](N)C(=O)O,train
COC(=O)C1=C(C)NC(C)=C(C(=O)OCC(C)C)C1c1ccccc1[N+](=O)[O-],valid
CN1CCN(CCCN2c3ccccc3Sc3ccc(Cl)cc32)CC1,train
CCN(CC)CCNC(=O)c1ccc(NS(C)(=O)=O)cc1,train
CC(N)Cc1ccc2c(c1)OCO2,train
CC(C)(C)c1ccc(O)c(O)c1,train
CC(C)(C)N,test
CCCCOC(=O)c1ccccc1N,train
O=C1CCCCCCCCCCCN1,train
c1ccc(B(c2ccccc2)c2ccccc2)cc1,train
Cc1cccc(C(=O)O)c1O,train
NCCOCCO,valid
[S-]c1ccccc1,train
CC(C)(C)c1ccc(C=O)cc1,train
CC[N+]1(CC)CCC(=C(c2ccccc2)c2ccccc2)C1C,train
CC(=O)OCC(=O)[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@H]3[C@@H](O)C[C@@]21C,train
O=C1c2c(O)cccc2Cc2cccc(O)c21,test
COc1ccc(N)cc1,train
CC(C)c1cccc(C(C)C)c1N,train
O=C(O)c1cc(C(=O)O)c(C(=O)O)cc1C(=O)O,train
Nc1ccc(S(=O)(=O)O)c(N)c1,train
O=C(O)C1CCCN1,valid
C=CCCCCCCCCC=O,train
CCO[Si](OCC)(OCC)c1ccccc1,train
C=CCOc1nc(OCC=C)nc(OCC=C)n1,train
NCCOS(=O)(=O)O,train
COc1cc2nc(N3CCN(C(=O)C4COc5ccccc5O4)CC3)nc(N)c2cc1OC,test
Cc1ccc(C(=O)c2ccc(CC(=O)[O-])n2C)cc1,train
CO[C@@]1(NC(=O)C(C(=O)[O-])c2ccc(O)cc2)C(=O)N2C(C(=O)[O-])=C(CSc3nnnn3C)CO[C@@H]21,train
C=C1c2c(Cl)ccc(O)c2C(=O)C2=C(O)[C@]3(O)C(=O)C(C(N)=O)=C(O)[C@@H](N(C)C)[C@@H]3[C@@H](O)[C@H]12.O=C(O)c1cc(S(=O)(=O)O)ccc1O,train
NC(CO)(CO)CO.O=C(c1ccccc1)c1ccc2n1CC[C@H]2C(=O)O,train
CO[C@@]1(NC(=O)CSCC#N)C(=O)N2C(C(=O)[O-])=C(CSc3nnnn3C)CS[C@@H]21,valid
CCOC(=O)c1ccc(OC(=O)CCCCCNC(=N)N)cc1,train
CC(C)OC(=O)COc1ccc(Cl)cc1Cl,train
CCCCC(CC)COC(=O)COc1ccc(Cl)cc1Cl,train
O=S(=O)(Oc1ccc(Cl)cc1Cl)c1ccccc1,train
CN(C)C(=O)Nc1ccc(Cl)c(Cl)c1,test
CC(Oc1cc(Cl)ccc1Cl)C(=O)O,train
CC(Oc1ccc(Cl)cc1Cl)C(=O)O,train
CCCCOC(=O)COc1ccc(Cl)cc1Cl,train
O=C(O)COc1ccc(Cl)cc1Cl,train
Cn1cnc([N+](=O)[O-])c1Sc1ncnc2nc[nH]c12,valid
[N-]=[N+]=CC(=O)OC[C@H](N)C(=O)O,train
COP(=O)(OC)OC=C(Cl)Cl,train
CC(Cl)CCl,train
CC(C)N(C)C,train
CN(C)CCCNCCCN,test
CCn1cc[n+](C)c1,train
CC(=O)OCC(=O)[C@@]12OC3(CCCC3)O[C@@H]1C[C@H]1[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]12C,train
Clc1ccc(CCC(Cn2ccnc2)Sc2c(Cl)cccc2Cl)cc1,train
COc1ccc(Cc2nccc3cc(OC)c(OC)cc23)cc1OC,train
Cl[Pd]Cl,valid
Cc1occc1C(=O)Nc1ccc(C(=O)N2CCC(F)(F)/C(=C\C(=O)N3CCC(N4CCCCC4)CC3)c3ccccc32)cc1.Cc1occc1C(=O)Nc1ccc(C(=O)N2CCC(F)(F)/C(=C\C(=O)N3CCC(N4CCCCC4)CC3)c3ccccc32)cc1,train
CC(C)CC(C)(c1ccc(O)cc1)c1ccc(O)cc1,train
CCCCCCCCCCCCCC[n+]1ccccc1,train
COc1c(O[C@@H]2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)cc2c(c1OC)-c1ccc(SC)c(=O)cc1[C@@H](NC(C)=O)CC2,train
Clc1sc(Cl)c(Cl)c1Cl,test
OCCN1CCN(CC/C=C2/c3ccccc3Sc3ccc(Cl)cc32)CC1,train
ClC=CCl,train
O=c1[nH]c(=O)n(Cl)c(=O)n1Cl,train
O=c1[n-]c(=O)n(Cl)c(=O)n1Cl,train
O=[N+]([O-])c1cccc(Cl)c1Cl,valid
O=[N+]([O-])c1ccc(Cl)cc1Cl,train
O=[N+]([O-])c1ccc(Cl)c(Cl)c1,train
CCCCCCCC/C=C/CCCOC(C)=O,train
NCCNC(=O)c1ccc(Cl)cn1,train
O=C(O)Cn1c(C(=O)Nc2nc(-c3ccccc3Cl)cs2)cc2ccccc21,test
CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)[O-])=C(CSc3nc(C)c(CC(=O)[O-])s3)CS[C@H]12)c1csc(N)n1,train
CCN(CC)CCOC(=O)c1cccnc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
O=S(=O)([O-])CCCS(=O)(=O)[O-],train
Nc1c2c(nc3c1CCC3)CCCC2,train
CC(C)C[C@H](NC(=O)[C@@H](COC(C)(C)C)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@H](CO)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)[C@H](Cc1c[nH]cn1)NC(=O)[C@@H]1CCC(=O)N1)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N1CCC[C@H]1C(=O)NNC(N)=O,valid
O=C(Nc1ccc(Oc2ccc(Cl)cc2)c(Cl)c1)c1cc(I)cc(I)c1O,train
O=C(N/N=C/c1ccc([N+](=O)[O-])o1)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,train
O=S(c1cc(Cl)cc(Cl)c1O)c1cc(Cl)cc(Cl)c1O,train
OC[C@H]1O[C@H](O[C@@]2(CO)O[C@@H](O)[C@H](O)[C@@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,train
CCOc1ccc(N=Nc2ccc(N)cc2N)cc1,test
c1ccc(OCc2ccc(CCCN3CCOCC3)cc2)cc1,train
Cc1c(Cl)c(=O)oc2cc(OP(=O)(OCCCl)OCCCl)ccc12,train
CC(NNC(=O)c1ccccc1)c1ccccc1,train
CC(=O)Nc1ccc(OCCOCCOc2ccc(NC(C)=O)cc2)cc1,train
O=C1c2ccccc2C(=O)C1c1ccc(Cl)cc1,valid
CCN(CC)C(=O)SCc1ccc(Cl)cc1,train
[TlH2+],train
Oc1ccc(Cc2ccc(O)cc2)cc1,train
COC(=O)NC(=S)Nc1ccccc1NC(=S)NC(=O)OC,train
[Fe+2].c1cc[cH-]c1.c1cc[cH-]c1,test
CC(C)(C)c1cc(CCC(=O)NN)cc(C(C)(C)C)c1O,train
C=C(C)C(=O)OCCCCCCCCCCCCCCCCCC,train
CC(=O)OC1CCC(C(C)(C)C)CC1,train
CC(C)(O)CCCC1=CCC(C=O)CC1,train
Cc1cc(-c2ccc(N)c(C)c2)ccc1N,valid
CC(C)OP(C)(=O)OC(C)C,train
O=C1OC2(c3ccc([O-])cc3Oc3cc([O-])ccc32)c2ccccc21,train
CC1=C(C)S(=O)(=O)CCS1(=O)=O,train
COS(=O)(=O)OC,train
CNC,test
CCCCCCCCCCCCCCCCCC(=O)OCC(CC)CCCC,train
CCCCCCCCCCCCCCCCCC(=O)OCCCCCCCCCCCCCC(C)C,train
CC(C)(CO)[C@@H](O)C(=O)NCCC(=O)[O-],train
CCCCOCCOC(=O)c1ccccc1C(=O)OCCOCCCC,train
C1=CCC(c2ccccc2)CC1,valid
Cc1ccc(-c2ccccc2)cc1,train
CCC12COCN1COC2,train
O=c1c(O)c(-c2ccc(O)cc2O)oc2cc(O)cc(O)c12,train
CCC(=C(CC)c1ccc(O)cc1)c1ccc(O)cc1,train
CC(C)c1nc(CN(C)C(=O)N[C@H](C(=O)N[C@@H](Cc2ccccc2)C[C@H](O)[C@H](Cc2ccccc2)NC(=O)OCc2cncs2)C(C)C)cs1,test
CCCCOC(=O)C(=O)Nc1cccc(-c2nnn[nH]2)c1,train
Cc1ccc(-n2nc(C(C)(C)C)cc2NC(=O)Nc2ccc(OCCN3CCOCC3)c3ccccc23)cc1,train
O=C([O-])c1ccccc1O.O=C([O-])c1ccccc1O.[Zn+2],train
CC(=O)NC(CCC(N)=O)C(=O)O,train
CCC(CN)CC(C)CCNCC(O)CNCC(C)CC(CC)CN,valid
c1ccc(-c2nnn[nH]2)cc1,train
O=C(Nc1ccc(Cl)c(Cl)c1)C1CC1,train
OC[C@H]1O[C@H](O[C@@H]2[C@@H](CO)O[C@H](O[C@@H]3[C@@H](CO)O[C@H](O)[C@H](O)[C@H]3O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,train
O=P([O-])([O-])OC(CO)CO,train
COc1ccc(CCNCC(O)COc2cccc(C)c2)cc1OC,test
CCCCCCCCP(=O)(O)O,train
c1cc(CCCc2ccncc2)ccn1,train
C=CC(=O)OCC(O)COc1ccccc1,train
CN(C)C(=O)CNC(=O)Cc1ccccc1,train
CCOC(=O)c1ccc(NC(=O)CN2CCCCC2)cc1,valid
CC(C(O)c1ccccc1)N(C)CCOC(c1ccccc1)c1ccccc1,train
CC(=O)O[C@@H]1C(=O)O[C@H]2[C@@H](OC(C)=O)C(=O)O[C@@H]12,train
N[C@H]1CN(c2c(F)cc3c(=O)c(C(=O)O)cn(C4C[C@H]4F)c3c2Cl)CC12CC2,train
O=C1NC2CCCCN2C12CCN(CCCN1c3ccccc3CCc3ccc(Cl)cc31)CC2,train
CO[C@]1(NC(=O)CSC(F)F)C(=O)N2C(C(=O)O)=C(CSc3nnnn3CCO)CO[C@H]21,test
CN(CC1(C)CCCO1)S(=O)(=O)c1ccc(Cl)c(S(N)(=O)=O)c1,train
C=CCOc1cc(Cl)ccc1C(=O)NCCN(CC)CC,train
Cc1ccc2c(n1)Oc1ccc(C(C)C(=O)OCC(=O)N(C)C)cc1C2,train
O=[N+]([O-])c1ccc(Cl)c(Cl)c1Cl,train
O=[N+]([O-])c1cc(Cl)c(Cl)cc1Cl,valid
C=CC1CC2C=CC1C2,train
Oc1ccc(Cl)c(Cl)c1Cl,train
O=[N+]([O-])c1c(Cl)cc(Cl)cc1Cl,train
O=[N+]([O-])c1cc(Cl)c(Cl)c(Cl)c1,train
Oc1cc(Cl)cc(Cl)c1Cl,test
Oc1c(Cl)ccc(Cl)c1Cl,train
C=C(Cl)CCl,train
Clc1nc2ccccc2nc1Cl,train
OC(CCl)CCl,train
O=C(O)c1cccc(C(=O)O)n1,valid
CC(C)(OOC(C)(C)c1ccccc1)c1ccccc1,train
ClC(Cl)c1ccccc1,train
CC(C)C1=CC2=CC[C@H]3[C@](C)(C(=O)O)CCC[C@]3(C)[C@H]2CC1,train
O=c1cc(-c2ccccc2)oc2ccccc12,train
Cc1cc(C)c(O)c(C)c1,test
C1CCC(NC2CCCCC2)CC1,train
Cl[Ba]Cl,train
O=C(O)c1nc(Cl)c(Cl)c(Cl)c1Cl,train
C[N+]1([O-])[C@H]2C[C@H](OC(=O)[C@H](CO)c3ccccc3)C[C@@H]1[C@H]1O[C@@H]21,train
O=C(O)CP(=O)(O)O,valid
CN(C)CCC=C1c2ccccc2C=Cc2ccccc21,train
CNCCC=C1c2ccccc2CCc2ccccc21,train
Cc1cc(C(=O)O)c(C)n1-c1cccc(C(=O)O)c1.O=C1C[C@@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@H]3C[C@H]46,train
O=C([O-])CC(O)(CC(=O)O)C(=O)[O-],train
Nc1ccc(-c2ccccc2)cc1,test
NCC1(CC(=O)O)CCCCC1,train
Nc1nc[nH]n1,train
CC1CCCCC1NC(=O)Nc1ccccc1,train
CCNP(=S)(OC)O/C(C)=C/C(=O)OC(C)C,train
O=C(c1ccc(F)cc1)C1CCN(CCn2c(=S)[nH]c3ccccc3c2=O)CC1,valid
Cc1nc2sccn2c(=O)c1CCN1CCC(=C(c2ccc(F)cc2)c2ccc(F)cc2)CC1,train
Cc1cc(O)ccc1O,train
Cc1cccc2ccccc12,train
Cc1cc(Cc2cc(C)cc(C(C)(C)C)c2O)c(O)c(C(C)(C)C)c1,train
O=C1CCCCCO1,test
O=C(Cl)OCc1ccccc1,train
Cc1ccc2ccccc2c1,train
Clc1ccc(COC(Cn2ccnc2)c2ccc(Cl)cc2Cl)c(Cl)c1,train
CN1CCN2c3ccccc3Cc3ccccc3C2C1,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](NC(=O)N3CCN(S(C)(=O)=O)C3=O)c3ccccc3)C(=O)N2[C@H]1C(=O)O,valid
CC(C)(C(=O)c1cccnc1)c1cccnc1,train
CN1C(CCl)Nc2cc(Cl)c(S(N)(=O)=O)cc2S1(=O)=O,train
CC(=O)Nc1c(I)c(C(=O)N[C@H]2C(O)O[C@H](CO)[C@@H](O)[C@@H]2O)c(I)c(N(C)C(C)=O)c1I,train
c1ccc2[nH]cnc2c1,train
Cc1ccccc1O,test
CCN(CCO)c1cccc(C)c1,train
CCN(CC)c1ccccc1,train
CC(=O)CC(=O)c1ccccc1,train
C1CCC(C2CCCCC2)CC1,train
O=C(/C=C/C=C/c1ccc2c(c1)OCO2)N1CCCCC1,valid
CC(C)=CCCC(C)=CC=O,train
O=CC=Cc1ccccc1,train
O=C/C=C/c1ccccc1,train
BrCCc1ccccc1,train
CC(C)SCc1ccco1,test
ClC(Cl)Cl,train
COC(=O)CCSCCC(=O)OC,train
ClC(Br)Br,train
CC(C)(CCl)C(=O)Cl,train
ClCc1ccccn1,valid
ClCc1cccnc1,train
C[C@]1(Cn2ccnn2)[C@H](C(=O)[O-])N2C(=O)C[C@H]2S1(=O)=O,train
CC(C)(C)C(=O)OCC(=O)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]12C,train
C[C@H]1O[C@H](O[C@@H]2[C@@H](CO)O[C@H](O[C@@H]3[C@@H](CO)O[C@@H](O)[C@H](O)[C@H]3O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1N[C@H]1C=C(CO)[C@@H](O)[C@H](O)[C@H]1O,train
C/C=C1\[C@H]2C=C(C)C[C@]1(N)c1ccc(=O)[nH]c1C2,test
COc1cccc(-c2ccc(/C=C\CN3CCCCCC3)cc2Cl)c1,train
COc1cc(C)ccc1C(C)C,train
CCCC1CCC(=O)O1,train
O=CCCc1ccccc1,train
CC(C)c1ccc(C(=O)N=C(N)N)cc1S(C)(=O)=O,valid
O=C(N[C@@H](c1ccccc1)[C@@H]1CCCCN1)c1cccc(C(F)(F)F)c1Cl,train
CC1CCC(=O)O1,train
N#C[C@@H]1C[C@@H]2C[C@@H]2N1C(=O)[C@@H](N)C12CC3CC(CC(O)(C3)C1)C2,train
CC1CC(O)CC(C)(C)C1,train
NC(=O)NC(N)=O,test
CC(C)CC(C)O,train
CC1COC(=O)O1,train
CC1(C)S[C@@H]2[C@H](NC(=O)C3(N)CCCCC3)C(=O)N2[C@H]1C(=O)O,train
NS(=O)(=O)c1cc2c(cc1Cl)NC(CC1CCCC1)NS2(=O)=O,train
COc1cc2c(cc1OC)C(=O)C(CC1CCN(Cc3ccccc3)CC1)C2,valid
CC(C)NCC(O)c1ccccc1Cl,train
CCCc1nc(C)c2c(=O)nc(-c3cc(S(=O)(=O)N4CCN(CC)CC4)ccc3OCC)[nH]n12.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
C[C@H]1COc2c(C3(N)CC3)c(F)cc3c(=O)c(C(=O)O)cn1c23,train
CNC1CCCN(c2c(F)cc3c(=O)c(C(=O)O)cn(C4CC4)c3c2OC)C1,train
COc1ccc(C(=O)NCc2ccc(OCCN(C)C)cc2)cc1OC,test
CCC(C)(C)c1ccc(CC(C)CN2C[C@H](C)O[C@H](C)C2)cc1,train
CCOC(=O)c1ccc(C#Cc2ccc3c(c2)C(C)(C)CCS3)nc1,train
C=COCCCCO,train
OCC1CO1,train
CCCCOC(=O)c1ccccc1,valid
Cc1c(Cl)cccc1Cl,train
CCC1COC(=O)O1,train
Cc1cc(C(C)(C)C)c(O)c(C)c1CC1=NCCN1,train
CC(=O)O[C@H](CC(=O)[O-])C[N+](C)(C)C,train
CC(C)COP(=O)(OCC(C)C)OCC(C)C,test
O=C(O)C(c1ccc(Cl)cc1)c1ccc(Cl)cc1,train
CCOc1ccccc1O,train
CC(C)c1ccc(O)c(C(C)C)c1,train
CC(C)C(=O)OCCOc1ccccc1,train
C=C(C)[C@H]1CN[C@H](C(=O)O)[C@H]1CC(=O)O,valid
O=C1c2ccccc2-c2n[nH]c3cccc1c23,train
Cc1ccccc1C,train
c1ccc(C(O[C@H]2CCCN(CCc3ccc4c(c3)OCO4)C2)c2ccccc2)cc1,train
Cc1ccc(Nc2c(F)cccc2Cl)c(CC(=O)O)c1,train
C[C@@H](C1=C(CCN(C)C)Cc2ccccc21)c1ccccn1,test
CC(C)OC(=O)c1cc2c(OCC(O)CNC(C)(C)C)cccc2[nH]1,train
CC1=NN(c2ccc(S(=O)(=O)O)cc2)C(=O)C1,train
FC(F)(F)C(=NOCC1OCCO1)c1ccc(Cl)cc1,train
CN(C)Cc1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,train
CC1CC(=O)CC(C)(C)C1,valid
CCn1c2ccccc2c2cc([N+](=O)[O-])ccc21,train
CCc1ccc2c(c1)C(=O)c1ccccc1C2=O,train
CC(C)(C1CCC(O)CC1)C1CCC(O)CC1,train
CCC(C)(C)c1cc(O)c(C(C)(C)CC)cc1O,train
C=C(C)C(=O)OC1CC(C)CC(C)(C)C1,test
CN1[C@H]2C[C@H](OC(=O)[C@H](CO)c3ccccc3)C[C@@H]1[C@H]1O[C@@H]21,train
C=CCc1ccc2c(c1)OCO2,train
CC(C)(C)NCC(O)c1ccc(O)c(CO)c1,train
O=C(O)c1cc(/N=N/c2ccc(S(=O)(=O)Nc3ccccn3)cc2)ccc1O,train
O=C1NS(=O)(=O)c2ccccc21,valid
O=C1[N-]S(=O)(=O)c2ccccc21,train
C=CCCCCCCCCCCCC,train
CN(C)CCC(O)(c1ccccc1)c1ccccc1Cl,train
NC(=O)[O-],train
CC(C)(CO)COC(=O)C(C)(C)CO,test
O=C(O)C[C@H](NC(=O)OCc1ccccc1)C(=O)O,train
CC(=O)C=CC1=C(C)CCCC1(C)C,train
Clc1ccc2c(c1)CCc1cccnc1C2=C1CCNCC1,train
O=C(Nc1ccccc1)c1ccccc1O,train
CCOC(=O)c1ccccc1C(=O)OCC,valid
CCCCOC(=O)c1ccccc1C(=O)OCCCC,train
O=[N+]([O-])c1ccc(O)cc1C(F)(F)F,train
NC(=O)c1ccccc1N,train
Nc1ccc(/N=N\c2ccccc2)c(N)c1,train
CC1CC(OC(=O)C(O)c2ccccc2)CC(C)(C)N1C,test
c1ccc2c(c1)[nH]c1cnccc12,train
C#C[C@]1(OC(C)=O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@@]21C,train
NC(=O)N=NC(N)=O,train
COc1ccc(N=[N+]([O-])c2ccc(OC)cc2)cc1,train
[O-][N+](=Nc1ccccc1)c1ccccc1,valid
CC[C@@H](C)CCCCC(=O)N[C@@H](CCNCS(=O)(=O)[O-])C(=O)N[C@H](C(=O)N[C@@H](CCNCS(=O)(=O)[O-])C(=O)N[C@H]1CCNC(=O)[C@H]([C@@H](C)O)NC(=O)[C@H](CCNCS(=O)(=O)[O-])NC(=O)[C@H](CCNCS(=O)(=O)[O-])NC(=O)[C@H](CC(C)C)NC(=O)[C@@H](CC(C)C)NC(=O)[C@H](CCNCS(=O)(=O)[O-])NC1=O)[C@@H](C)O,train
ClC(Cl)=C(Cl)C(Cl)=C(Cl)Cl,train
Clc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl,train
CCOC(=O)CC(C)=O,train
C1CCCNCCC1,test
CCCCC(CC)COC(=O)/C=C\C(=O)OCC(CC)CCCC,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H]1Cl,train
CC(C)(O)C#CC(C)(C)O,train
CC(=O)[O-].CC(=O)[O-].[Mg+2],train
O=C(O)CNCC(=O)O,valid
CCCCCCCC/C=C\CCCCCCCC(=O)OCCCC,train
ClC1=C(Cl)C(Cl)(Cl)C(Cl)=C1Cl,train
CN1CCN(C2=Nc3cc(F)ccc3Cc3ccccc32)CC1,train
C=CCCC(=O)O,train
CN=C=O,test
[Na]I,train
C[N+](C)(C)Cc1ccccc1,train
COc1cccc(C=O)c1,train
C[N+](C)(C)CC(=O)O,train
Cc1ccccc1CCl,valid
CCC(C)c1cccc(C(C)CC)c1O,train
CCCC[N+](C)(CCCC)CCCC,train
CC(=O)CC(c1ccccc1)c1c(O)c2ccccc2oc1=O,train
CN1[C@H]2CC[C@@H]1C[C@H](O)C2,train
O=C1c2ccccc2CCc2ccccc21,test
N=C(N)Nc1ccc(C(=O)Oc2ccc3cc(C(=N)N)ccc3c2)cc1,train
BrCCCCCBr,train
NCCc1ccc(O)cc1,train
CC(C)C(=O)OCC(=O)[C@@]12O[C@H](C3CCCCC3)O[C@@H]1C[C@H]1[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@H]3[C@@H](O)C[C@@]12C,train
C[C@@H]1O[C@@H](O[C@@H]2[C@@H](O)[C@H](OCCc3ccc(O)c(O)c3)O[C@H](CO[C@@H]3O[C@H](CO)[C@@H](O)[C@H](O)[C@H]3O)[C@H]2OC(=O)/C=C/c2ccc(O)c(O)c2)[C@H](O)[C@H](O)[C@H]1O,valid
CCCCCCCCCCCC[N+](C)(C)CCOc1ccccc1,train
NCC(O)c1ccc(O)cc1,train
O=CNNC=O,train
CCNC(=S)NCC,train
CC/C(=C(/CC)c1ccc(O)cc1)c1ccc(O)cc1,test
CCOC(=O)/C=C\C(=O)OCC,train
CCN(C=O)CC,train
OCCOCCO,train
CC(C)(C)S,train
CCCOC(=O)c1ccc(O)cc1,valid
O=C(OCc1ccccc1)c1ccc(O)cc1,train
CCCCC(CC)COC(=O)c1ccc(O)cc1,train
CC(=O)c1ccc(Cl)c(Cl)c1Cl,train
CCCCCCCOC(=O)c1ccc(O)cc1,train
CC(C)COC(=O)c1ccccc1C(=O)OCC(C)C,test
CC(C)CCCCCCOC(=O)c1ccccc1C(=O)OCCCCCCC(C)C,train
CCOC(=O)C=Cc1ccccc1,train
COC(=O)c1ccc(O)cc1,train
CCOC(=O)c1ccc(O)cc1,train
NCCc1cnc[nH]1,valid
CCCCCCCC/C=C\CCCCCCCC(=O)O,train
CN1C(=O)C(C)(C2=CCCCC2)C(=O)N=C1O,train
COS(=O)(=O)[O-].C[N+]1(C)CCN(CC(O)(c2ccccc2)C2CCCCC2)CC1,train
CCCCCCCCn1sccc1=O,train
C=CCCCCCC,test
CCCCCCCCOc1ccc(C(=O)O)cc1,train
CCN(CC)CCNc1ccc(CO)c2sc3ccccc3c(=O)c12,train
O=c1ccc2cc(O[C@@H]3O[C@H](CO)[C@@H](O)[C@H](O)[C@H]3O)c(O)cc2o1,train
c1ccc2c(c1)CCCC2C1=NCCN1,train
Cc1cccc(N(C)C(=S)Oc2ccc3ccccc3c2)c1,valid
CC(CN(C)C)CN1c2ccccc2CCc2ccccc21,train
CC1=C2[C@H]3OC(=O)[C@@H](C)[C@@H]3CC[C@@]2(C)C=CC1=O,train
C[C@H]1[C@H]2[C@H](C[C@H]3[C@@H]4CC[C@H]5C[C@@H](O)CC[C@]5(C)[C@H]4CC(=O)[C@@]32C)O[C@]12CC[C@@H](C)CO2,train
c1ccc2sc(SSN3CCOCC3)nc2c1,train
N#Cc1nn(-c2c(Cl)cc(C(F)(F)F)cc2Cl)c(N)c1S(=O)C(F)(F)F,test
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@H](C)O[C@H](C)C1,train
CC(C)C(=O)Nc1ccc([N+](=O)[O-])c(C(F)(F)F)c1,train
FC(F)C(F)(F)OCC(Cn1cncn1)c1ccc(Cl)cc1Cl,train
CCOP(=O)(OCC)OP(=O)(OCC)OCC,train
O=C1N=C2SCCN2C(=O)C1Cc1ccc(Cl)cc1,valid
O=C1NC2NC(=O)NC2N1,train
CN1[C@H]2CC[C@@H]1[C@@H](C(=O)O)[C@@H](OC(=O)c1ccccc1)C2,train
C[N@+]12CCCC[C@@H]1CCC(=C(c1cccs1)c1cccs1)C2,train
CC1(C)O[C@@H]2C[C@H]3[C@@H]4CCC5=CC(=O)C=C[C@]5(C)[C@H]4[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1,train
CCOc1cc(N)c(Cl)cc1C(=O)NCC1CN(Cc2ccc(F)cc2)CCO1.O=C(O)CC(O)(CC(=O)O)C(=O)O,test
O=C(c1cc2ccccc2o1)N1CCN(Cc2ccccc2)CC1,train
C[C@H]1CN(c2c(F)c(F)c3c(=O)c(C(=O)O)cn(C4CC4)c3c2F)C[C@@H](C)N1,train
COc1ccc(Cc2cnc(N)nc2N)cc1OC,train
C=C[C@]1(C)C[C@@H](OC(=O)CSC(C)(C)CNC(=O)[C@H](N)C(C)C)[C@]2(C)[C@H](C)CC[C@]3(CCC(=O)[C@H]32)[C@@H](C)[C@@H]1O,train
Cc1cnc(C(=O)O)c[n+]1[O-],valid
O=C1c2ccccc2C(=O)c2cc(O)ccc21,train
CCNc1nc(NC(C)(C)C)nc(SC)n1,train
CNC(=O)N(C)c1nnc(C(C)(C)C)s1,train
Cc1[nH]c(=O)n(C(C)(C)C)c(=O)c1Cl,train
CCCCC(C#N)(Cn1cncn1)c1ccc(Cl)cc1,test
O=C([O-])CF,train
CCCCOCC(C)OCC(C)O,train
C=CC(=O)OCCOCC(CC)(COCCOC(=O)C=C)COCCOC(=O)C=C,train
C=CC(=O)OCCCCCC(C)C,train
Cl/C=C/Cl,valid
Cl/C=C\Cl,train
O=C(O)CCCOc1ccc(Cl)cc1Cl,train
C=Cc1ccc(C)cc1,train
OCC(Cl)CCl,train
OCCN1C[C@H](O)[C@@H](O)[C@H](O)[C@H]1CO,test
CCCCOC(=O)CC(CC(=O)OCCCC)(OC(C)=O)C(=O)OCCCC,train
NC(=O)Nc1ccc(Cl)c(Cl)c1,train
O=C(O)c1cccc(C(F)(F)F)c1,train
O=S1(=O)CC(Cl)(Cl)C(Cl)(Cl)C1,train
COC(=O)c1ccc(OC)cc1,valid
COC(OC)OC,train
O=C(O)c1snc(Cl)c1Cl,train
Nc1ccc(Cl)cc1[N+](=O)[O-],train
CN(C)CCN(Cc1cccs1)c1ccccn1.CN(C)CCN(Cc1cccs1)c1ccccn1,train
CCCC(=O)OCCC(C)CCC=C(C)C,test
C=CCc1ccc(OC(=O)Cc2ccccc2)c(OC)c1,train
Cc1ccc(OC(=O)CC(C)C)cc1,train
CCC(C)C1N=C(C)C(C)S1,train
CCCC(=O)C(CC)Sc1ccoc1C,train
Cc1nc2ccc(Cl)cc2[nH]1,valid
N[C@@H](CCC(=O)O)C(=O)O,train
CCCC(=O)OC/C=C(\C)CCC=C(C)C,train
CC(C)(C)OC(=O)c1ccccc1C(=O)O,train
CC1(COc2ccc(CC3SC(=O)NC3=O)cc2)CCCCC1,train
CC(C)C[C@H](NC(=O)OCC1c2ccccc2-c2ccccc21)C(=O)O,test
C[C@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(Cl)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)CO,train
CC1(C)CCC(C)(C)c2cc(C(=O)Nc3ccc(C(=O)O)cc3)ccc21,train
CP(=O)([O-])OCCC[Si](O)(O)O,train
N#Cc1ccc(CC(=O)c2cccc(C(F)(F)F)c2)cc1,train
O=C(c1ccc(O)cc1)c1ccc(O)cc1O,valid
OCCC1CCCCN1,train
CC(C)(O)C(=O)c1ccccc1,train
CCCCOC(=O)C(C)OC(=O)CCC,train
COC(C=C(C)CCC=C(C)C)OC,train
CCC(=O)OC(C)(C)C1CC=C(C)CC1,test
C=C[Si](OC(=C)C)(OC(=C)C)OC(=C)C,train
O=S(=O)(O)NC1CCCCC1,train
CC(C)(C)c1ccc(OP(=O)(Oc2ccccc2)Oc2ccccc2)cc1,train
CC(C)(C)c1ccc(OCC2CO2)cc1,train
CC(=O)[C@H]1CC[C@H]2[C@@H]3C=CC4=CC(=O)CC[C@@]4(C)[C@@H]3CC[C@]12C,valid
Cn1c(=O)c2c(ncn2CC(O)CO)n(C)c1=O,train
C=CN1CCCC1=O,train
OCC(CO)N[C@H]1C[C@](O)(CO)[C@@H](O)[C@H](O)[C@H]1O,train
C1CCCCCCCCCCC1,train
Cc1cccc(C)c1,test
Cc1ccc(N)c(C)c1,train
Oc1cccc(Cl)c1,train
O=C(O)c1cc(C(=O)O)cc(S(=O)(=O)[O-])c1,train
Nc1c(S(=O)(=O)[O-])cc(Br)c2c1C(=O)c1ccccc1C2=O,train
CC(C)CCON=O,valid
CC(=O)C(C)C,train
O=Cc1ccccc1[N+](=O)[O-],train
O=Cc1ccc([N+](=O)[O-])cc1,train
COc1cc(OC)nc(Oc2cccc(Oc3nc(OC)cc(OC)n3)c2C(=O)[O-])n1,train
CC(C)=C[C@@H]1[C@@H](C(=O)OCc2coc(Cc3ccccc3)c2)C1(C)C,test
CCCCc1oc2ccc(NS(C)(=O)=O)cc2c1C(=O)c1ccc(OCCCN(CCCC)CCCC)cc1,train
CCCCCCCN(CC)CCCC(O)c1ccc(NS(C)(=O)=O)cc1.CCCCCCCN(CC)CCCC(O)c1ccc(NS(C)(=O)=O)cc1,train
COc1cc([C@@H]2c3cc4c(cc3C(O[C@@H]3O[C@@H]5CO[C@@H](C)O[C@H]5[C@H](O)[C@H]3O)C3COC(=O)[C@@H]32)OCO4)cc(OC)c1OP(=O)(O)O,train
Nc1ccc(S(=O)(=O)[N-]c2ncccn2)cc1.[Ag+],train
COC(=O)C1=C(C)NC(C)=C(C(=O)OCC(C)=O)C1c1ccccc1[N+](=O)[O-],valid
C=C1/C(=C\C=C2/CCC[C@]3(C)[C@@H]([C@H](C)OCCC(C)(C)O)CC[C@@H]23)C[C@@H](O)C[C@@H]1O,train
Cc1nc(-c2ccc(OCC(C)C)c(C#N)c2)sc1C(=O)O,train
COc1ccc(C(=O)N2CCCC2=O)cc1,train
CC(C)(C)NCC(O)c1ccc(O)c(CO)n1,train
CC(N)C(=O)OC(C)(C)Cc1ccc(Cl)cc1,test
CC1=C(C)C(=O)C(CCCCC#CCCCC#CCO)=C(C)C1=O,train
C=CCc1ccccc1OCC(O)CNC(C)C,train
CC(C)(C)c1nnc(NS(=O)(=O)c2ccccc2)s1,train
Cc1ccc2c(c1N)C(=O)c1ccccc1C2=O,train
CCn1c2ccccc2c2cc(N)ccc21,valid
CCOc1ccc(NC(C)=O)cc1N,train
Nc1c(Br)cc(Br)c2c1C(=O)c1ccccc1C2=O,train
Nc1nnc(-c2ccc([N+](=O)[O-])o2)o1,train
CCCCCCCCCCCCCCBr,train
CCOCn1c(-c2ccc(Cl)cc2)c(C#N)c(Br)c1C(F)(F)F,test
Cn1cc(S(C)=O)c(=O)c2ccc(F)cc21,train
c1ccc2c(CCC3CCNCC3)c[nH]c2c1,train
CC(=O)c1ccc(OCC(=O)N2CCCCC2)cc1,train
C/C(=N\O)c1ccc(OCC(=O)N2CCCCC2)cc1,train
O=C(O)CCCCCCNC1c2ccccc2CCc2ccccc21,valid
O=C(O)Cc1ccccc1Oc1ccc(Cl)cc1Cl,train
CCCOC(=O)Cc1ccc(OCC(=O)N(CC)CC)c(OC)c1,train
CCC(=O)O[C@H]1[C@@H](N2CCCCC2)C[C@H]2[C@@H]3CC[C@H]4C[C@H](OC(C)=O)[C@@H](N5CCCCC5)C[C@]4(C)[C@H]3CC[C@@]21C,train
COc1ccc2c(C(=O)c3ccc(Cl)cc3)c(C)n(CC(=O)O)c2c1,train
Cc1nc([N+](=O)[O-])cn1-c1ccc([N+](=O)[O-])cc1,test
CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,train
O=[Mo](=O)=O,train
CCOP(=S)(S)OCC,train
c1nc[nH]n1,train
ClC1(Cl)C2(Cl)C3(Cl)C4(Cl)C(Cl)(Cl)C5(Cl)C3(Cl)C1(Cl)C5(Cl)C24Cl,valid
CCCCCCCC/C=C\CCCCCCCC(N)=O,train
CNC(=O)Oc1cc(C)c(N(C)C)c(C)c1,train
Cc1cc(=O)[nH]c(=S)[nH]1,train
CCCCC(CC)COP(=O)(O)OCC(CC)CCCC,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(Cl)[C@@H](O)C[C@]2(C)[C@@]1(OC(=O)c1ccco1)C(=O)CCl,test
CCC(C)(C)c1ccc(O)cc1,train
Cc1c(N)cccc1Cl,train
Cc1ccc(Cl)cc1,train
CC(C)(c1cc(Cl)c(O)c(Cl)c1)c1cc(Cl)c(O)c(Cl)c1,train
Nc1ccc(Br)cc1,valid
CCCCOC(=O)CCCCC(=O)OCCCC,train
Cc1ccc(O)c(C)c1,train
COc1ccc(CCN(C)CCCN2CCc3cc(OC)c(OC)cc3CC2=O)cc1OC,train
Cc1ccc(O)cc1,train
Cc1ccc(C)cc1,test
COc1ccc(S(=O)(=O)N2c3ccc(Cl)cc3[C@@](O)(c3ccccc3Cl)[C@H]2C(=O)N2CCC[C@@H]2C(N)=O)cc1OC,train
O=[PH](Oc1ccccc1)Oc1ccccc1,train
C[NH+](C)CCC(c1ccc(Cl)cc1)c1ccccn1,train
CCCNC(=O)NS(=O)(=O)c1ccc(Cl)cc1,train
C[N+](C)(C)CCO,valid
O=[Cr]O[Cr]=O,train
[Cr+3],train
O=C1c2cccc(O)c2C(=O)c2c(O)cccc21,train
CN/C(=N\C#N)NCCSCc1nc[nH]c1C,train
CCCCNS(=O)(=O)c1ccccc1,test
C=C(C)c1cccc(C(=C)C)c1,train
CC=CC(=O)O,train
CC1OC(=O)C(C)OC1=O,train
Nc1ccc(N)c2c1C(=O)c1ccccc1C2=O,train
CC(C)Nc1ccc(NC(C)C)c2c1C(=O)c1ccccc1C2=O,valid
OCCCCCO,train
CC(C)(C)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,train
CNC(=N)NC(=O)Nc1c(C)cccc1C,train
CSc1ccc(/C=C2/C(C)=C(CC(=O)O)c3cc(F)ccc32)cc1,train
CC(C(=O)O)c1ccc2c(c1)CC(=O)c1ccccc1S2,test
CC(C(=O)O)c1ccc(C2CCCCC2)c2ccccc12,train
CC[C@]1(O)C[C@H]2CN(CCc3c([nH]c4ccccc34)[C@@](C(=O)OC)(c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)C2)C1,train
CCOC(=O)CN[C@@H](C(=O)N1CC[C@H]1C(=O)NCc1ccc(/C(N)=N/O)cc1)C1CCCCC1,train
COc1cc2nc(N3CCN(C(=O)c4ccco4)CC3)nc(N)c2cc1OC,train
[NH3+]c1ccccc1,valid
NC(=S)c1c(Cl)cccc1Cl,train
CC(C)c1ccc(S(=O)(=O)[O-])cc1,train
CCCCCCCCCCCC(=O)OCC(C)OC(=O)CCCCC(=O)O,train
CCCCCCCCCCCCCCCS(=O)(=O)Oc1ccccc1,train
CC[C@H](C)C(=O)O[C@H]1C[C@H](O)C=C2C=C[C@H](C)[C@H](CC[C@@H](O)C[C@@H](O)CC(=O)[O-])[C@H]21,test
NC[C@H]1O[C@H](O[C@@H]2[C@@H](N)C[C@@H](N)[C@H](O[C@H]3O[C@H](CO)[C@@H](O)[C@H](N)[C@H]3O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,train
COc1cc2c(cc1OC)[C@@]13CCN4CC5=CCO[C@H]6CC(=O)N2[C@H]1[C@H]6[C@H]5C[C@H]43,train
c1cc[n+]2c(c1)-c1cccc[n+]1CC2,train
C[C@H](CCC(=O)O)[C@H]1CC[C@H]2[C@@H]3C(=O)C[C@@H]4CC(=O)CC[C@]4(C)[C@H]3CC(=O)[C@@]21C,train
COCCOCCOCCOC,valid
C[N+](C)(C)CCCCCCCCCC[N+](C)(C)C,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=Cc5oncc5C[C@]4(C)[C@H]3CC[C@@]21C,train
Cc1ccccc1Br,train
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(C)=O)C[C@@H]3O[C@H]1C[C@H](N)[C@H](O)[C@H](C)O1,train
CC(O)CCCCn1c(=O)c2c(ncn2C)n(C)c1=O,test
Cc1ccc(C(=O)Nc2ccc(S(=O)(=O)O)c3cc(S(=O)(=O)O)cc(S(=O)(=O)O)c23)cc1NC(=O)c1cccc(NC(=O)Nc2cccc(C(=O)Nc3cc(C(=O)Nc4ccc(S(=O)(=O)O)c5cc(S(=O)(=O)O)cc(S(=O)(=O)O)c45)ccc3C)c2)c1,train
CCCCCCOc1ccc(C(=N)N(CCCC)CCCC)c2ccccc12,train
COc1cc(-c2nc(NC(=O)c3cc4cc(C)cc(C)c4n3CC(=O)O)sc2CCC2CCCCC2)c(OC)cc1Cl,train
COc1cc(C(C)C)c2c(c1)S(=O)(=O)N(COc1cc(=O)n3cccc(OCCN4CCCCC4)c3n1)C2=O,train
CCOC(=O)COc1ccc2c(c1)CC(NCC(O)c1cccc(Cl)c1)CC2,valid
COc1cccc([C@H](O)C2CCN(CCc3ccc(F)cc3)CC2)c1OC,train
CN(C)CCC=C1c2ccccc2C(C)(C)c2ccccc21,train
O=C(N[C@H](c1ccccc1)[C@@H]1CN2CCC1CC2)c1c(Cl)ccc(C(F)(F)F)c1Cl,train
O=C(Nc1ccc2[nH]ccc2c1)c1cc2cc(F)ccc2n1Cc1cccc(F)c1,train
CNC(=O)Oc1cccc(C(C)C)c1,test
O=C(O)CC(O)(CC(=O)O)C(=O)O.Oc1cccc2cccnc12,train
COC(=O)c1cc(Oc2ccc(Cl)cc2Cl)ccc1[N+](=O)[O-],train
CCC(=O)Nc1ccc(O)cc1,train
CN(C)CCCN1c2ccccc2Sc2ccccc21,train
CC(=O)N1C[C@H](O)C[C@H]1C(=O)O,valid
CCC(C)(S(=O)(=O)CC)S(=O)(=O)CC,train
CN1C(=O)/C(=N/NC(N)=S)c2ccccc21,train
CC(=O)Oc1cc(C(C)C)c(OCCN(C)C)cc1C,train
CCN(CC)CCNC(C(=O)OCCC(C)C)c1ccccc1,train
CCNC(=O)/C=C/c1cccc(Br)c1,test
O=S(=O)(c1ccccc1)c1ccc(Cl)cc1,train
CN(C)c1ccc(SC#N)cc1,train
OC(c1ccc(Cl)cc1)(c1ccc(Cl)cc1)c1cccnc1,train
CCCCCCCCCCCCCCCC(=O)NCCO,train
CCCCCCC1CCCC(=O)O1,valid
CC(CC=O)c1ccccc1,train
C1CNCCN1,train
CCOC(=O)CCc1ccccc1,train
CCOC(=O)CC(=O)c1ccccc1,train
CCCCCC#CC(=O)OC,test
CC/C=C\CC1=C(C)CCC1=O,train
CCC(=O)OC(C)c1ccccc1,train
Cc1cccc2nccnc12,train
C/C=C/C=C/C(=O)O,train
CCN(CC)C(=S)[S-],valid
[O-]c1c(Cl)cc(Cl)cc1Sc1cc(Cl)cc(Cl)c1[O-],train
Brc1cc(Br)c(Oc2cc(Br)c(Br)cc2Br)cc1Br,train
CC(C)NCC(O)c1ccc(NS(C)(=O)=O)cc1,train
CCC(OCCCOC)OC(C)=O,train
Cc1cscn1,test
NC1CCCCC1N,train
O=C(Cl)c1c(Cl)c(Cl)c(C(=O)Cl)c(Cl)c1Cl,train
C[Sn](C)(Cl)Cl,train
CCCCC(CC)C(=O)Cl,train
CCOCCC(=O)OCC,valid
N#Cc1ccc2c(c1)N(CCCN1CCC(O)CC1)c1ccccc1S2,train
CN(N=O)c1ccccc1,train
O=NN1CCOCC1,train
NC(=O)Nc1nc2ccccc2[nH]1,train
O=NN1CCNCC1,test
OC(CCN1CCCCC1)(c1ccccc1)C1CCCCC1,train
COc1cc2c(cc1OC)[C@@H](c1ccccc1)CN(C)CC2,train
COc1ccc(CCNC[C@H](O)c2ccc(O)cc2)cc1OC,train
COc1cc2c(cc1OC)-c1c/c(=N\c3c(C)cc(C)cc3C)n(C)c(=O)n1CC2,train
Cc1ccc(S(=O)(=O)N[C@@H](CCCCN)C(=O)CCl)cc1,valid
CN(C)CCCN1c2ccccc2Sc2ccc(C(F)(F)F)cc21,train
CC(C)(C)NCC(O)c1ccccc1Cl,train
CCN(CC)C(=O)N[C@H]1C[C@@H]2c3cccc4[nH]cc(c34)C[C@H]2N(C)C1,train
COC(=O)CC(C)CCC=C(C)C,train
CC1(C)[C@@H](O)CC[C@@]2(C)[C@H]1CC[C@]1(C)[C@@H]2C(=O)C=C2[C@@H]3C[C@@](C)(C(=O)O)CC[C@]3(C)CC[C@]21C,test
O=S1OCCO1,train
OCC(O)CCl,train
COC(C)(C)CCO,train
C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
OCC(O)CO,valid
O=S(=O)(O)c1ccc(O)c2ncccc12,train
Cc1ccc(S(=O)(=O)O)cc1.O,train
Oc1ccc(C(c2ccc(O)cc2)(C(F)(F)F)C(F)(F)F)cc1,train
COC(OC)(C(=O)c1ccccc1)c1ccccc1,train
C[C@@H]1CC[C@H]2C(C)(C)[C@H]3C[C@]21CC[C@@]3(C)O,test
CC(c1ccc(O)cc1)(c1ccc(O)cc1)c1ccc(O)cc1,train
O=S(=O)(O)O,train
O=C(c1ccc(O)c(O)c1)c1ccc(O)cc1O,train
[Fe+2],train
c1ccc2c(c1)ccc1cc3c(ccc4ccccc43)cc12,valid
Nc1ccc(O)c(N)c1,train
Nc1ccc(NC(=O)c2ccc(N)cc2)cc1,train
Cc1ccc(N)cc1N,train
CCOP(=S)(OCC)Oc1cc(C)nc(C(C)C)n1,train
CN1C(=O)CN=C(c2ccccc2)c2cc(Cl)ccc21,test
CC(C)(C)C(=O)O,train
CC(C)(CO)[N+](=O)[O-],train
CCOC(=O)[C@@H](C)Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1,train
CCCCCCCCCCCCS,train
Clc1ccc(COC(Cn2ccnc2)c2ccc(Cl)cc2Cl)cc1,valid
CSC1=N[C@@](C)(c2ccccc2)C(=O)N1Nc1ccccc1,train
CCCCCCCCCCCCc1ccc(O)cc1,train
C[N+]1(C)CCC(OC(=O)C(O)(c2ccccc2)C2CCCC2)C1,train
CC(C)CN(C[C@@H](OP(=O)([O-])[O-])[C@H](Cc1ccccc1)NC(=O)O[C@H]1CCOC1)S(=O)(=O)c1ccc(N)cc1,train
N[C@@H](CCC(=O)N[C@@H](CS)C(=O)NCC(=O)O)C(=O)O,test
OCCN(CCO)c1cccc(Cl)c1,train
CCOC(=O)C(Cl)Cc1cc(-n2nc(C)n(C(F)F)c2=O)c(F)cc1Cl,train
O=C(O)c1cccc(-c2noc(-c3ccccc3F)n2)c1,train
CCOC(=O)C1CC(=O)C(=C(O)C2CC2)C(=O)C1,train
CC(C)=CC1C(C(=O)OC(C#N)c2cccc(Oc3ccccc3)c2)C1(C)C,valid
CCCCCCCCCC[N+](C)(C)CCCCCCCCCC,train
COc1ccccc1NC(=O)CC(C)=O,train
Cn1cnc2c(=O)nc(N)[nH]c21,train
CC1(C)O[C@@H]2C[C@H]3[C@@H]4CCC5=CC(=O)CC[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CCl)O1,train
N=C(N)NC(=N)N1CCOCC1,test
Cc1cccc(C)c1NC(=O)CN1CCN(CCCC(c2ccc(F)cc2)c2ccc(F)cc2)CC1,train
CCOC(=O)[C@@](C)(N)Cc1ccc(O)c(O)c1,train
CC(=O)[C@H]1CC[C@H]2[C@@H]3C[C@H](C)C4=CC(=O)CC[C@]4(C)[C@H]3[C@@H](O)C[C@]12C,train
CCN(CCCCOC(=O)c1ccc(OC)c(OC)c1)C(C)Cc1ccc(OC)cc1,train
CCCCc1cc2ccccc2c(OCCN(C)C)n1,valid
CCCN(CCc1cccs1)[C@H]1CCc2c(O)cccc2C1,train
CC1=CC(=O)[C@H]2C[C@@H]1C2(C)C,train
Nc1c(Cl)c(F)nc(OCC(=O)O)c1Cl,train
CO/N=C(/C1=NOCCO1)c1ccccc1Oc1ncnc(Oc2ccccc2Cl)c1F,train
Cc1ccc(C(C)C)c(O)c1,test
O=c1cc[nH]c(=S)[nH]1,train
Nc1ccc(Sc2ccc(N)cc2)cc1,train
CC(=O)OCCOC(C)=O,train
CCCCCCCCCCCCCCCCCC(=O)OCCO,train
CCCCCCCC,valid
N=C1NC(=N)c2cc3ccccc3cc21,train
C1=CCCC=CCC1,train
CCCCCCCCCCCC(=O)OC,train
Oc1ccc(O)c2c(O)cccc12,train
N#C[S-],test
CC(N)=S,train
Cc1cc(O)c(C(C)(C)C)cc1Sc1cc(C(C)(C)C)c(O)cc1C,train
COc1ccccc1N1CCNCC1,train
Nc1ccccc1Nc1ccccc1,train
CCC[N+]1(C)CCCC1,valid
CCCCCCCCCCCCCCCCI,train
O=[N+]([O-])c1ccc2[nH]ncc2c1,train
CCC(C)(C)CC,train
CN1CC(=O)N=C1NC(=O)Nc1cccc(Cl)c1,train
CCCC(=O)O[C@]1(C(=O)COC(C)=O)CC[C@H]2[C@@H]3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,test
C=CCCC=C,train
BrCCOc1ccccc1,train
CC(C)CN(C[C@@H](O)[C@H](Cc1ccccc1)NC(=O)O[C@H]1CO[C@H]2OCC[C@@H]12)S(=O)(=O)c1ccc(N)cc1,train
C[C@]12O[C@H](C[C@]1(O)CO)n1c3ccccc3c3c4c(c5c6ccccc6n2c5c31)CNC4=O,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@H]4C=C[C@H]3C4)[C@@]1(Cl)C2(Cl)Cl,valid
CC(=O)O[C@@H]1C[C@H]2CC[C@]1(C)C2(C)C,train
CC1(C)[C@@H]2CC[C@@]1(C)[C@H](O)C2,train
COP(N)(=S)Oc1ccccc1C(=O)OC(C)C,train
CCC(=O)O[C@@H]1C[C@H]2CC[C@]1(C)C2(C)C,train
CC(C)CCCCCOC(=O)c1ccccc1C(=O)OCCCCCC(C)C,test
CCCCCCCCCC(CC)c1ccc(S(=O)(=O)O)cc1.OCCN(CCO)CCO,train
CC(C)CC(C)(C)C,train
CCCSC(=O)N(CCC)CCC,train
O=P(OCC(Br)CBr)(OCC(Br)CBr)OCC(Br)CBr,train
COc1c(Cl)ccc(Cl)c1C(=O)O,valid
Brc1ccc(Oc2ccc(Br)cc2)cc1,train
ClC(Cl)(Br)Br,train
Brc1ccc(Br)cc1,train
OC1CCCCC1c1ccccc1,train
O=S(=O)([O-])c1ccc2ccccc2c1,test
CCC(C)COC(=O)CC(C)C,train
Cc1ccccc1OCC(=O)O,train
CC=CC(=O)CCCC,train
CCCCCC(CC)OC(=O)CCCOc1ccc(Cl)cc1Cl,train
Fc1c(F)c(F)c(Br)c(F)c1F,valid
CCCCc1ccc(O)cc1,train
C/C=C(\C)C(=O)OCc1ccccc1,train
CC(=O)Oc1ccc([N+](=O)[O-])cc1CCl,train
CSCCCN=C=S,train
CC1=NC(C)OC1C,test
CC(C)(C)c1ccccc1,train
O=C1CCCC=CCCCCCCCCCC1,train
CCOC(=O)COc1ccc(C)cc1,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)COP(=O)([O-])[O-],train
CC(C)(O)C#N,valid
NO,train
[NH3+]O.[NH3+]O,train
NCCNCCO,train
N#CC(O)c1ccccc1,train
O=C(NO)c1ccccc1,test
Oc1ccc2cc(SSc3ccc4cc(O)ccc4c3)ccc2c1,train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nnn[nH]2)cc1)[C@@H](C(=O)O)C(C)C,train
CCCC(CCC)C(N)=O,train
Cl[V](Cl)(C1C=CC=C1)C1C=CC=C1,train
N#CCc1ccccc1[N+](=O)[O-],valid
O=[N+]([O-])c1ccc(CCO)cc1,train
O=[N+]([O-])c1cccc(O)c1,train
Cc1ccc2ccccc2c1[N+](=O)[O-],train
O=[N+]([O-])c1ccccc1CCO,train
O=C1CCCC(=O)C1,test
COc1cc(/C=C/C(=O)CC(=O)/C=C/c2ccc(O)c(OC)c2)ccc1O,train
CCOC(N)=O,train
NC(N)=O,train
COc1ccccc1N1CCN(CCCNc2cc(=O)n(C)c(=O)n2C)CC1,train
O=c1cc[nH]c(=O)[nH]1,valid
CCc1ccc(Br)cc1,train
CCCC[Sn](Cl)(Cl)Cl,train
O=C(O)c1ccc2cc(C(=O)O)ccc2c1,train
CO/C=C(/C(=O)OC)c1ccccc1Oc1cc(Oc2ccccc2C#N)ncn1,train
CCCOCC(C)O,test
C=CCOC(=O)C(C)(C)OC(=O)c1cc(-n2c(=O)cc(C(F)(F)F)n(C)c2=O)ccc1Cl,train
CCCCCCC(CO)CCCC,train
O=C([O-])c1cc(C(=O)O)cc(S(=O)(=O)O)c1,train
CO/N=C(/C(=O)N[C@@H]1C(=O)N2C(C(=O)[O-])=C(C[n+]3ccn4ncccc43)CS[C@H]12)c1nsc(N)n1,train
COC(C)CCO,valid
CO/N=C(/C(=O)N[C@@H]1C(=O)N2C(C(=O)OCOC(=O)C(C)(C)C)=C(C)CS[C@H]12)c1csc(N)n1,train
CN(Cc1ccc(C(C)(C)C)cc1)Cc1cccc2ccccc12,train
CCCC(=O)N1CCCN(c2nc(N)c3cc(OC)c(OC)cc3n2)CC1,train
CC(C)(S)C(=O)N[C@@H](CS)C(=O)O,train
C/C(=C(\CCOC(=O)c1ccccc1)SS/C(CCOC(=O)c1ccccc1)=C(/C)N(C=O)Cc1cnc(C)nc1N)N(C=O)Cc1cnc(C)nc1N,test
CC#CCC(C)[C@H](O)/C=C/[C@@H]1[C@H]2c3cccc(CCCC(=O)[O-])c3O[C@H]2C[C@H]1O,train
CCCCOC(=O)CCCCCCCC(=O)OCCCC,train
C=CCBr,train
CCC1(C2=NCCN2)Cc2ccccc2O1,train
Cc1ccccc1C(=O)Nc1cccc(OC(C)C)c1,valid
CSc1ccc(C(=O)c2[nH]c(=O)[nH]c2C)cc1,train
CN(C)CC/C=C1\c2ccccc2COc2ccccc21,train
Cc1cc(N)c2ccccc2[n+]1CCCCCCCCCC[n+]1c(C)cc(N)c2ccccc21,train
CN(C)CCOC(=O)COc1ccc(Cl)cc1,train
CN(C(=O)CCCOc1ccc2[nH]c(=O)ccc2c1)C1CCCCC1,test
N=C(N)NC(=N)NCCc1ccccc1,train
C=CCOC(=O)CCCCCCCC,train
COc1cc([N+](=O)[O-])ccc1N,train
C=CCSSCCC,train
C=CCNC(N)=S,valid
C[Si](Cl)(c1ccccc1)c1ccccc1,train
CC(C)C(O)C(C)(C)CO,train
C[C@]12CC[C@@H]3[C@H]4CCC(=O)C=C4CC[C@H]3[C@@H]1CC[C@@H]2OC(=O)CCc1ccccc1,train
CCCCCCCCCC(=O)O[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@]12C,train
C[C@]12CC[C@@H]3[C@H]4CCC(=O)C=C4CC[C@H]3[C@@H]1CC[C@@H]2O,test
CCCc1c2oc(C(=O)O)cc(=O)c2cc2c(=O)cc(C(=O)O)n(CC)c12,train
Cl[Nd](Cl)Cl,train
CCO[Si](CCC1CCC2OC2C1)(OCC)OCC,train
CCOC(=O)c1ccc(N(C)C)cc1,train
CC(C)C(=O)OCCc1ccccc1,valid
CC(=O)OCC=Cc1ccccc1,train
OCCc1ccccn1,train
CCNc1cccc(C)c1,train
c1ccc(Oc2ccccc2)cc1,train
CNc1ccccc1,test
N#Cc1ccccn1,train
ON=C1CCCCC1,train
O=C(O)c1ccccc1C(=O)OCc1ccccc1,train
COc1ccccc1NC(=O)c1cc2ccccc2cc1O,train
O=C(Oc1ccccc1)c1ccc2ccccc2c1O,valid
CC(C)CCCCCOC(=O)CCCCC(=O)OCCCCCC(C)C,train
O=[N+]([O-])c1ccc(Oc2ccc([N+](=O)[O-])cc2)cc1,train
COc1ccc(OC)c(OC)c1,train
CC(=O)/C=C/C1C(C)=CCCC1(C)C,train
O=c1ccc2ccccc2o1,test
COc1ccc(N)c(C)c1,train
CN[C@H]1CCc2cc(OC)c(OC)c(OC)c2-c2ccc(OC)c(=O)cc21,train
CC(=O)OCCC(C)CC(C)(C)C,train
[Co+2],train
COc1ccc2c3c1O[C@H]1[C@@H](O)C=C[C@H]4[C@@H](C2)N(C)CC[C@]314,valid
CCCCCCCCCCCC(=O)OCC(O)CO,train
CCOP(=S)(OCC)Oc1ccc2c(C)c(Cl)c(=O)oc2c1,train
CCCc1c(OCCCCc2nnn[nH]2)ccc(C(C)=O)c1O,train
CN(C)C(=S)[S-].CN(C)C(=S)[S-].[Cu+2],train
CCCC[n+]1cccc(C)c1,test
O=C1c2ccccc2C(=O)c2cc(Cl)ccc21,train
Cc1nc2ccccc2s1,train
N#Cc1c2ccccc2cc2ccccc12,train
O=CC=C(c1ccccc1)c1ccccc1,train
O=C(/C=C/c1ccc([N+](=O)[O-])cc1)c1ccccc1,valid
CCC1(c2cnc[nH]2)Cc2ccccc2C1,train
CCCn1cc[n+](C)c1,train
COc1ccc2c3c1O[C@H]1C[C@@H](O)C=C[C@@]31CCN(C)C2,train
CCCCC=O,train
O=C(O)CCCC(=O)O,test
CCCCCCC[N+](CC)(CC)CCCCc1ccc(Cl)cc1,train
CCN1CCCC1CNC(=O)c1cc(S(N)(=O)=O)ccc1OC,train
O=C1OC2(c3cc(Br)c(O)c(Br)c3Oc3c2cc(Br)c(O)c3Br)c2c(Cl)c(Cl)c(Cl)c(Cl)c21,train
C[C@]12CC[C@H]3[C@@H](C=CC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@@]2(O)CCC(=O)[O-],train
CC(C)CCCCCCCOC(=O)c1ccccc1C(=O)OCCCCCCCC(C)C,valid
CC(=O)c1ccc(C(C)=O)cc1,train
CCCCCCCCCOC(=O)c1ccccc1C(=O)OCCCCCCCCC,train
CN(C)c1ccncc1,train
Fc1ccc(C(OCCN2CCN(CCCc3ccccc3)CC2)c2ccc(F)cc2)cc1,train
CCc1nn(C)c(C(=O)NCc2ccc(C(C)(C)C)cc2)c1Cl,test
O=C(c1ccc(Cl)cc1)c1ccc(Cl)cc1,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(OC)ccc4[C@H]3CC[C@@]21C,train
C=C(C#N)C(=O)OCC,train
Cc1c(-c2cccnc2)c2ccccc2n1CCCCC(=O)O,train
O=C(O)C1(c2ccc3c(c2)OC[C@H](Cc2ccc(-c4ccccc4)cc2)[C@H]3O)CCCC1,valid
Cc1oc(-c2ccccc2)nc1CCOc1cccc2c1ccn2CCC(=O)O,train
Cc1cc(NC(=O)C(=O)O)cc(C)c1Oc1ccc(O)c2c1CCC2,train
CNC(=O)[C@H]1O[C@@H](n2cnc3c(NCc4cc(Cl)ccc4OCc4cc(C)no4)ncnc32)[C@H](O)[C@@H]1N,train
NC(=O)N1C(=O)C(C(=O)c2cc(Cl)cs2)c2cc(F)c(Cl)cc21,train
NC(=O)N(O)[C@@H]1C=C[C@H](Oc2cccc(Oc3ccc(F)cc3)c2)C1,test
O=C1NC(=O)C(c2ccccc2)(c2ccccc2)N1,train
CO[C@H]1O[C@@H]2O[C@]3(C)CC[C@H]4[C@H](C)CC[C@@H]([C@H]1C)C42OO3,train
O=C(O)CCNC(=O)c1ccc(/N=N/c2ccc(O)c(C(=O)O)c2)cc1,train
CC(C)C(Br)C(=O)NC(N)=O,train
CN1CCC[C@H]1c1cccnc1,valid
Cc1c(N(C)CS(=O)(=O)[O-])c(=O)n(-c2ccccc2)n1C,train
O=CCc1ccccc1,train
C/C(=C\C(=O)OCCCCCCCCC(=O)O)C[C@@H]1OC[C@H](C[C@@H]2O[C@H]2[C@@H](C)[C@H](C)O)[C@@H](O)[C@H]1O,train
CCN(CC)C(=O)/C(C#N)=C/c1cc(O)c(O)c([N+](=O)[O-])c1,train
Nc1nc(Cl)nc2c1ncn2[C@@H]1O[C@H](CO)[C@@H](O)[C@@H]1F,test
O=C(CS(=O)Cc1ccco1)NC/C=C\COc1cc(CN2CCCCC2)ccn1,train
C[C@@H](O)[C@H]1C(=O)N2C(C(=O)[O-])=C(SC3Cn4cnc[n+]4C3)[C@H](C)[C@H]12,train
CN1C[C@@H]2C[C@H]1CN2c1cc2c(cc1F)c(=O)c(C(=O)O)cn2C1CC1,train
CN(CCOc1ccc(NS(C)(=O)=O)cc1)CCc1ccc(NS(C)(=O)=O)cc1,train
C[C@@H](O)[C@H]1C(=O)N2C(C(=O)O)=C([C@H]3CCCO3)S[C@H]12,valid
C=C(C)C1CC=C(C)C(=O)C1,train
Cc1ccccc1S,train
O=C1CCCCC1C1CCCCC1,train
C[C@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)COP(=O)([O-])[O-],train
CCC(=O)C1CC(C(C)C)CC=C1C,test
CC(C)=CCOC(=O)c1ccccc1,train
COc1cc(CCC(C)=O)ccc1O,train
OC[C@H](O)[C@@H](O)[C@@H](O)[C@H](O)CO,train
COC1=C(OC)C(=O)C(C/C=C(\C)CC/C=C(\C)CC/C=C(\C)CC/C=C(\C)CC/C=C(\C)CC/C=C(\C)CC/C=C(\C)CC/C=C(\C)CC/C=C(\C)CCC=C(C)C)=C(C)C1=O,train
CCN(CC)CCOc1ccc(C(O)(Cc2ccc(Cl)cc2)c2ccc(C)cc2)cc1,valid
O=C(O)c1ccc(O)c(O)c1,train
O=C1c2ccccc2C(=O)c2c(O)c(O)cc(O)c21,train
CCCCn1cc[n+](C)c1.O=S(=O)([O-])C(F)(F)F,train
CCc1nc(N)nc(N)c1-c1ccc(Cl)cc1,train
O=c1c(O)c(-c2ccc(O)c(O)c2)oc2cc(O)cc(O)c12,test
COc1ccc(/C=C/C(=O)O)cc1,train
CCCCCCCCCCn1cc[n+](C)c1,train
O=S(=O)([O-])CO,train
C=CCCCCCCCC,train
O=C1CCCCCCCCCCC1,valid
O=[N+]([O-])c1ccc(Nc2ccccc2)cc1,train
O=[N+]([O-])c1ccc([O-])cc1,train
O=S(=O)([O-])c1ccc(O)cc1,train
c1cc(N(CC2CO2)CC2CO2)ccc1Cc1ccc(N(CC2CO2)CC2CO2)cc1,train
CC(=O)Nc1ccc(S(N)(=O)=O)cc1,test
O=C(OCc1ccccc1)c1ccccc1O,train
c1ccc(CSCc2ccccc2)cc1,train
O=C(Cc1ccccc1)OCc1ccccc1,train
CC(=O)C(Cl)C(=O)N(C)C,train
N#CC1(N)CCCCC1,valid
O=C1CCCCCN1SSN1CCCCCC1=O,train
CCOC(=O)NNc1ccc(N(CC)CC(C)O)nn1,train
COCCc1ccc(OCC(O)CNC(C)C)cc1.COCCc1ccc(OCC(O)CNC(C)C)cc1.O=C(O)CCC(=O)O,train
S=C(SSC(=S)N1CCCCC1)N1CCCCC1,train
COC(=O)CC#N,test
CC(C)CCCCCCN,train
CCC(=O)OCC(=O)[C@@]1(OC(=O)CC)[C@@H](C)C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(Cl)[C@@H](O)C[C@@]21C,train
CC(C)NC(=O)NC(C)C,train
CCCCCCCCCCCCCCC(Br)C(=O)O,train
C=CCOCC(O)CO,valid
CC(=O)c1ccc(C)cc1,train
O=C(O)c1cc(O)c2ccccc2c1O,train
O=S(=O)(O)CCCN1CCOCC1,train
CCCCNc1ccccc1,train
FC(F)(F)c1ccc(Cl)cc1Cl,test
Oc1ccc(Sc2ccc(O)cc2)cc1,train
O=C(c1ccc(O)cc1)c1ccc(Cl)cc1,train
Oc1ccc(Nc2ccccc2)cc1,train
C=CC(=O)OCCCCCCCC(C)C,train
O=C(c1ccc(O)cc1)c1ccc(O)c(O)c1O,valid
CCCCC(CC)COC(=O)CC#N,train
O=C(Nc1ccc(Br)cc1)c1cc(Br)cc(Br)c1O,train
CCCCN(CCCC)CCCC,train
N[C@@H](C(=O)N[C@@H]1C(=O)N2C(C(=O)O)=C(Cl)CS[C@H]12)c1ccccc1,train
O=NN(CCCl)C(=O)NCCCl,test
CC(=O)[C@]1(O)Cc2c(O)c3c(c(O)c2[C@@H](O[C@H]2C[C@H](N)[C@H](O)[C@H](C)O2)C1)C(=O)c1c(O)cccc1C3=O,train
CCCCNC(=O)NS(=O)(=O)c1ccc(N)cc1,train
CC1(c2ccccc2)OC(C(=O)O)=CC1=O,train
COc1cc(N)c(Cl)cc1C(=O)NC1CN2CCC1CC2,train
CCN1CCC[C@H]1CNC(=O)c1c(O)c(Cl)cc(Cl)c1OC,valid
CN(C(=O)c1c(O)c2ccccc2n(C)c1=O)c1ccccc1,train
CC(N/C(=N\C#N)Nc1ccncc1)C(C)(C)C,train
CC(Cc1ccccc1)(NC(=O)CN)c1ccccc1,train
CCO[Si](OCC)(OCC)OCC,train
O=C1c2ccccc2S(=O)(=O)N1CCCCN1CCN(c2ncccn2)CC1,test
O=C(O)CC/C=C\CC[C@H]1[C@@H](OCc2ccc(-c3ccccc3)cc2)C[C@H](O)[C@@H]1N1CCCCC1,train
O=Cc1ccco1,train
c1ccoc1,train
CCCCCCCCCCCCC1CO1,train
COc1ccccc1O,valid
Cc1ccccc1-n1c(CF)nc2ccc(N)cc2c1=O,train
C=CC1CO1,train
C=CCN1C(=O)C(CC(C)C)NC1=S,train
NS(=O)(=O)c1cc(C(=O)O)c(NCc2ccco2)cc1Cl,train
CCCCC1=NC2(CCCC2)C(=O)N1Cc1ccc(-c2ccccc2-c2nnn[nH]2)cc1,test
CC(C)CC(C)N,train
CNC(=O)c1c(I)c(C(=O)NCC(=O)Nc2c(I)c(C(=O)O)c(I)c(C(=O)NCCO)c2I)c(I)c(N(C)C(C)=O)c1I,train
CCCCCCCCSCCO,train
CN[C@H]1[C@@H](O)[C@@H](NC)[C@H](O)[C@H]2O[C@@H]3O[C@H](C)CC(=O)[C@]3(O)O[C@H]12,train
O=Cc1ccccc1C=O,valid
CN1CCCC(CC2c3ccccc3Sc3ccccc32)C1,train
Cc1cc(C)n(CO)n1,train
O=c1oc(=O)c2cc3c(=O)oc(=O)c3cc12,train
CCN1CN(CC)CN(CC)C1,train
Cc1ncc([N+](=O)[O-])n1CC(O)CCl,test
NCC1CCC(C(=O)O)CC1,train
COc1cc(NS(=O)(=O)c2ccc(N)cc2)ncn1,train
COc1cc(O)c(C(=O)c2ccccc2)cc1S(=O)(=O)O,train
COC(=O)Nc1ccc(Cl)c(Cl)c1,train
CN[C@H](CC(C)C)C(=O)N[C@H]1C(=O)N[C@@H](CC(N)=O)C(=O)N[C@H]2C(=O)N[C@H]3C(=O)N[C@H](C(=O)N[C@@H](C(=O)O)c4cc(O)cc(O)c4-c4cc3ccc4O)[C@H](O)c3ccc(c(Cl)c3)Oc3cc2cc(c3O[C@@H]2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O[C@H]2C[C@](C)(N)C(O)[C@H](C)O2)Oc2ccc(cc2Cl)[C@H]1O,valid
CC[C@H](C)[C@@H](NC(=O)[C@H](CCC(=O)O)NC(=O)[C@H](CC(C)C)NC(=O)[C@@H]1CSC([C@H](N)[C@H](C)CC)=N1)C(=O)N[C@@H]1CCCCNC(=O)[C@@H](CC(N)=O)NC(=O)[C@@H](CC(=O)O)NC(=O)[C@@H](Cc2cnc[nH]2)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@@H]([C@H](C)CC)NC(=O)[C@@H](CCCN)NC1=O,train
O=S(=O)([O-])c1ccccc1C=Cc1ccc(-c2ccc(C=Cc3ccccc3S(=O)(=O)[O-])cc2)cc1,train
O=C/C(Cl)=C(/Cl)C(=O)O,train
Cl[C@H]1OCCO[C@@H]1Cl,train
C#CC(C)(C)NC(=O)c1cc(Cl)cc(Cl)c1,test
Nc1cc(Cl)c(N)c(Cl)c1,train
Nc1c(Cl)cc([N+](=O)[O-])cc1Cl,train
O=C1C(Cl)=C(Cl)C(=O)c2ccccc21,train
C=C(C)C(=O)Nc1ccc(Cl)c(Cl)c1,train
O=C(O)C(Cl)Cl,valid
CCCCCC(=O)OCC,train
CCCCOC(C)=O,train
CCCCCCCCCCCCN,train
CCCCOP(=O)(OCCCC)OCCCC,train
Cc1cc2nc3ccc(N(C)C)cc3[s+]c2cc1N,test
Oc1cc(Cl)c(Cl)cc1Cl,train
CCC(C)(CCC(C)C)C(=O)O,train
CCCCc1ccccc1,train
O=c1[nH]c(=O)c2c[nH]nc2[nH]1,train
C=CCSSCC=C,valid
C=C(C)C(=O)O[Sn](CCCC)(CCCC)CCCC,train
CC(C)CCCC(C)(C)O,train
CCC/C=C/C=O,train
C1=COCCC1,train
O=C(O)c1ccco1,test
COc1cc(OC)c2ccc(=O)oc2c1,train
Cc1cc(=O)oc2cc(N(C)C)ccc12,train
C=CN1CCCCCC1=O,train
Cc1occc1S,train
CCCOC(C)=O,valid
Cc1ccc2c(c1)CCCN2,train
CC1(C)CCC[C@@]2(C)[C@H]1CC[C@@]1(C)OCC[C@H]21,train
O=C(COc1ccc(Cl)cc1)N1CCN(Cc2ccc3c(c2)OCO3)CC1,train
NNC(=O)OCc1ccccc1,train
CC(C)c1ccc(C#N)cc1,test
Cl[Yb](Cl)Cl,train
COc1cc(C)cc(O)c1,train
C=CC(=O)OCCCCCCOC(=O)C=C,train
C=CC(C)(C)CC(=O)OC,train
CCCCCCOC(=O)c1ccccc1,valid
CCCCCCc1ccc(-c2ccc(C#N)cc2)cc1,train
C=C(C)C(=O)OCCCCCC,train
COc1ccc2c(c1OC)C(=O)O[C@@H]2[C@H]1c2cc3c(cc2CCN1C)OCO3,train
O=[N+]([O-])c1ccccc1-c1ccccc1,train
N=C(N)N.N=C(N)N.O=C(O)O,test
O=[N+]([O-])c1ccccc1CCl,train
O=[N+]([O-])c1ccc(CCl)cc1,train
O=[N+]([O-])c1cccc(CCl)c1,train
O=C(Cl)c1cccc([N+](=O)[O-])c1,train
O=C(Cl)c1ccccc1[N+](=O)[O-],valid
CC(C)CC(=O)O,train
Nc1ccc(S(=O)(=O)[O-])cc1,train
O=[N+]([O-])c1ccccc1Nc1ccccc1,train
CCCCCCCCCCCCCCCCC(=O)O,train
NS(=O)(=O)O,test
[PbH2+2],train
CC(=O)Nc1ccc2c(c1)Cc1ccccc1-2,train
O=C(c1ccc2c(c1)C(=O)OC2=O)c1ccc2c(c1)C(=O)OC2=O,train
CC(C)(C)N(CCO)CCO,train
CC(CO)CO,valid
C=C(C)C(=O)OCCCCCCCCCCCCC,train
C=C(C)C(=O)OCCC[Si](OC)(OC)OC,train
C=CC(C)CCC=C(C)C,train
C=CCCCCCCCCCCC,train
OCC(S)CS,test
CC(=O)O[C@@]12CO[C@@H]1C[C@H](O)[C@@]1(C)C(=O)[C@H](O)C3=C(C)[C@@H](OC(=O)[C@H](O)[C@@H](NC(=O)OC(C)(C)C)c4ccccc4)C[C@@](O)([C@@H](OC(=O)c4ccccc4)[C@@H]12)C3(C)C,train
CCCN(CCC)CCc1c[nH]c2ccccc12,train
CN(C)CCc1c[nH]c2ccccc12,train
CCN(CC)CCc1c[nH]c2ccccc12.CCN(CC)CCc1c[nH]c2ccccc12,train
CCCCc1cc2c(=O)c(C(=O)OC)c[nH]c2cc1OCc1ccccc1,valid
NC1(c2ccccc2)CCCCC1,train
COc1ccc2[nH]cc(CCN(C(C)C)C(C)C)c2c1,train
O=C1O[C@@H]2[C@@H](O)[C@H](O)O[C@@H]2[C@H]1O,train
CN(C)CCc1c[nH]c2cccc(OP(=O)(O)O)c12,train
COC(=O)[C@H]1[C@@H](OC(=O)c2ccccc2)C[C@@H]2CC[C@H]1N2C,test
c1ccc(CN2CCNCC2)cc1,train
Cc1c(N)nc(C(CC(N)=O)NCC(N)C(N)=O)nc1C(=O)NC(C(=O)NC(C)C(O)C(C)C(=O)NC(C(=O)NCCc1nc(-c2nc(C(=O)NCCCN[C@@H](C)c3ccccc3)cs2)cs1)C(C)O)C(O[C@@H]1O[C@@H](CO)[C@@H](O)[C@H](O)[C@@H]1O[C@H]1O[C@H](CO)[C@@H](O)[C@H](OC(N)=O)[C@@H]1O)c1c[nH]cn1,train
CC(CN(C)C)CN1c2ccccc2S(=O)(=O)c2ccccc21,train
CN(C)C(=O)C(CCN1CCC(O)(c2ccc(Cl)cc2)CC1)(c1ccccc1)c1ccccc1,train
CC(C(=O)O)c1ccc(CC2CCCC2=O)cc1,valid
Cc1ccccc1N1C(=O)c2cc(S(N)(=O)=O)c(Cl)cc2NC1C,train
CCN(CC)CC(=O)Nc1c(C)cccc1C,train
c1ccc2c(c1)OCC(C1=NCCN1)O2,train
O=C1CCC(c2ccc(-n3ccnc3)cc2)=NN1,train
O=C1N(c2ccccc2)c2ccccc2C1(Cc1ccncc1)Cc1ccncc1,test
CCn1ccnc1CC1COc2ccccc2O1,train
CCN(CC)CCNC(=O)c1cc(Cl)c(N)cc1OC,train
Cc1ccccc1CNc1ncnc2c1ncn2[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,train
COc1cccc2c1cc([N+](=O)[O-])c1c(C(=O)[O-])cc3c(c12)OCO3,train
COc1ccccc1N,valid
Nc1ccccc1,train
CC(COc1ccc(C(C)(C)C)cc1)OS(=O)OCCCl,train
O=C([O-])C1O[Sb]2OC(=O)C(O2)C(C(=O)[O-])O[Sb]2OC(=O)C1O2,train
O=C1c2ccccc2C(=O)c2ccccc21,train
Nc1ccccc1C(=O)O,test
C=CCC1([C@H](C)CCC)C(=O)NC(=O)NC1=O,train
CCN[C@H](C)Cc1cccc(C(F)(F)F)c1,train
CN[C@H](C)Cc1ccccc1,train
CN[C@@H](C)Cc1ccccc1,train
C[C@@H](N)C(=O)c1ccccc1,valid
C[C@H](N)C(=O)c1ccccc1,train
CC(Cc1ccccc1)NCCn1cnc2c1c(=O)n(C)c(=O)n2C,train
CCN[C@@H](C)Cc1ccccc1,train
CCN[C@@H](C)Cc1cccc(C(F)(F)F)c1,train
C[C@H]1N=C(N)O[C@H]1c1ccccc1,test
C=CCOC(C)=O,train
CCN(CC)C(=O)C(Cl)=C(C)OP(=O)(OC)OC,train
Oc1ccc(-c2ccccc2)cc1,train
Oc1ccccc1-c1ccccc1,train
CC(=O)O[Hg]c1ccccc1,valid
O[Sn](c1ccccc1)(c1ccccc1)c1ccccc1,train
CCCOC(=O)CCCCC(=O)OCCC,train
NC(=O)c1ccccc1C(N)=O,train
CCOP(=S)(Oc1ccc([N+](=O)[O-])cc1)c1ccccc1,train
NC(=O)c1c(Cl)cccc1Cl,test
CCCCCCCCCCN,train
CNC(=O)Oc1ccc(N(C)C)c(C)c1,train
Clc1nc(Cl)c(Cl)c(Cl)c1Cl,train
CCCCCCOC(=O)c1ccc(O)cc1,train
NC[C@H](O)c1ccc(O)c(O)c1,valid
O=C(c1ccccc1)C1(O)CCCCC1,train
CC(C)OC(=O)c1ccccc1,train
CCCCNP(N)(N)=S,train
CN1Cc2c(N)cccc2C(c2ccccc2)C1,train
CNC(=C[N+](=O)[O-])NCCSCc1csc(CN(C)C)n1,test
CN/C=N/c1ccc(C)cc1C,train
COCCOC(=O)C1=C(C)NC(C)=C(C(=O)OC(C)C)C1c1cccc([N+](=O)[O-])c1,train
CC(C)OC(=O)CCC(=O)OC(C)C,train
O=C1CN=C(c2ccccc2)c2cc([N+](=O)[O-])ccc2N1,train
Fc1cccc(Cl)c1CCl,valid
CSc1ccc(C(=O)C(C)(C)N2CCOCC2)cc1,train
OC[C@H](O)[C@H](O)CO,train
Clc1cc(Cl)c(-c2cc(Cl)c(Cl)cc2Cl)cc1Cl,train
C/C=C/C[C@@H](C)[C@@H](O)[C@H]1C(=O)N[C@@H](CC)C(=O)N(C)CC(=O)N(C)[C@@H](CC(C)C)C(=O)N[C@@H](C(C)C)C(=O)N(C)[C@@H](CC(C)C)C(=O)N[C@@H](C)C(=O)N[C@H](C)C(=O)N(C)[C@@H](CC(C)C)C(=O)N(C)[C@@H](CC(C)C)C(=O)N(C)[C@@H](C(C)C)C(=O)N1C,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C=C(Cl)C4=CC(=O)[C@@H]5C[C@@H]5[C@]4(C)[C@H]3CC[C@@]21C,test
N[C@@H](CS)C(=O)O,train
c1ccc2sc(SNC3CCCCC3)nc2c1,train
NC1CCCCC1,train
ON=C1CCCC1,train
COc1ccc(C(=O)/C(Br)=C\C(=O)[O-])cc1,valid
CN(C)N=Nc1nc[nH]c1C(N)=O,train
CC1=CCC(C(C)C)=CC1,train
CCC(C)CCCC(C)(C)O,train
Cc1cc(Cl)ccc1/N=C/N(C)C,train
CC(C)n1c(/C=C/[C@@H](O)C[C@@H](O)CC(=O)[O-])c(-c2ccc(F)cc2)c2ccccc21,test
CC1C2CC(CC2C2CCCC(O)C2)C1(C)C,train
Cc1cc(O)c(C(=O)N[C@@H](C(=O)N[C@@H]2C(=O)N3C(C(=O)O)=C(CSc4nnnn4C)CS[C@H]23)c2ccc(O)cc2)cn1,train
CCOC(CN1CCN(CC(C)C(=O)c2ccccc2)CC1)c1ccccc1,train
Cc1onc(-c2ccccc2)c1-c1ccc(S(N)(=O)=O)cc1,train
CCCSSC(CCO)=C(C)N(C=O)Cc1cnc(C)nc1N,valid
CCOc1cc(OCC)c(C(=O)CCC(=O)O)cc1OCC,train
NNC(=O)CP(=O)(c1ccccc1)c1ccccc1,train
CC(CNC(=O)c1cccnc1)NC(=O)c1cccnc1,train
O=C(CCCN1CCN2CCCC2C1)c1ccc(F)cc1,train
OC(c1ccccc1)(c1ccccc1)C1CN2CCC1CC2,test
COc1ccc(/C=N\NC(=O)c2ccncc2)c(C(=O)O)c1OC,train
CCN(CC)CCOc1ccc2nc(N(C)C)sc2c1,train
CCN(CC)CCC(=O)N1c2ccccc2Sc2ccc(Cl)cc21,train
CN1CCN(CCC(=O)N2c3ccccc3Sc3ccc(C(F)(F)F)cc32)CC1,train
CCN(CC)CCOc1ccccc1C(=O)CCc1ccccc1,valid
Cc1nc(-c2ccc3c(c2)CCN(CCC2CCC(NC(=O)/C=C/c4ccc(F)cc4)CC2)CC3)no1,train
OCC(CO)n1cnc(-c2ccc(F)cc2)c1-c1ccnc(Oc2ccccc2)n1,train
CCn1nc(Cc2ccccc2)cc1C1CCN(C[C@H]2C[C@H](N(C)[C@@H](C(=O)O)C(C)C)C[C@@H]2c2cccc(F)c2)CC1,train
CCN1CCC2(CC1)COc1cc3c(cc12)N(C(=O)c1ccc(-c2ccc(-c4nnc(C)o4)cc2C)cc1)CC3,train
Cc1cc2c(cc1C(F)(F)F)N(C(=O)Nc1ccc(Oc3cccnc3C)nc1)CC2,test
NC(=O)c1nc(-c2cccc(-c3cc(F)ccc3OCC(F)(F)C(F)(F)F)c2)n[n-]1,train
Cc1cnc(C(=O)NCCc2ccc(S(=O)(=O)NC(=O)NC3CCCCC3)cc2)cn1,train
CC1(C)O[C@@H]2C[C@H]3[C@@H]4C[C@H](F)C5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1,train
CCC1=C(C)CN(C(=O)NCCc2ccc(S(=O)(=O)NC(=O)N[C@H]3CC[C@H](C)CC3)cc2)C1=O,train
COC(=O)Nc1nc2cc(Sc3ccccc3)ccc2[nH]1,valid
COC1=C(OC)C(=O)C(CCCCCCCCCCO)=C(C)C1=O,train
CC(C)Oc1ccc2c(=O)c(-c3ccccc3)coc2c1,train
C#Cc1cccc(Nc2ncnc3cc(OCCOC)c(OCCOC)cc23)c1,train
CC[C@H]1OC(=O)[C@H](C)C(=O)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(OC)C[C@@H](C)C(=O)[C@H](C)[C@H]2N(CCCCn3cnc(-c4cccnc4)c3)C(=O)O[C@]12C,train
Cc1cccc(C)c1OCC(=O)N[C@@H](Cc1ccccc1)[C@@H](O)C[C@H](Cc1ccccc1)NC(=O)[C@H](C(C)C)N1CCCNC1=O,test
Cc1ccc(-c2ncc(Cl)cc2-c2ccc(S(C)(=O)=O)cc2)cn1,train
CC(C)(O)c1ccccc1CC[C@@H](SCC1(CC(=O)[O-])CC1)c1cccc(/C=C/c2ccc3ccc(Cl)cc3n2)c1,train
CCCCCOC(=O)Nc1nc(=O)n([C@@H]2O[C@H](C)[C@@H](O)[C@H]2O)cc1F,train
C[C@]12CC[C@H]3[C@@H](CC[C@H]4NC(=O)C=C[C@]34C)[C@@H]1CC[C@@H]2C(=O)Nc1cc(C(F)(F)F)ccc1C(F)(F)F,train
Cc1c(C(=O)NN2CCCCC2)nn(-c2ccc(Cl)cc2Cl)c1-c1ccc(Cl)cc1,valid
NC[C@H]1CC[C@H](C(=O)Oc2ccc(CCC(=O)O)cc2)CC1,train
N[C@H](Cc1c[nH]c2ccccc12)C(=O)O,train
CCOP(=S)(OCC)Oc1cc(C)nc(N(CC)CC)n1,train
CC(C)(C)NC(N)=O,train
CCCCCCCCCCCC[N+](C)(C)C,test
CC(C)c1ccc(O)cc1,train
Cc1cccc(N)c1N,train
O=C([O-])COc1ccc(Cl)cc1Cl,train
CCCCCCCCCCCCCC[N+](C)(C)C,train
CC(C)(N=C=O)c1cccc(C(C)(C)N=C=O)c1,valid
CCO[Si](CCCNC(N)=O)(OCC)OCC,train
COC(=O)c1sccc1S(N)(=O)=O,train
NC(=O)CN1CCCC1=O,train
O=C(c1ccccc1)c1ccc(O)c(O)c1O,train
CCCCCOC(=O)CCCCC,test
Oc1cccc(O)c1,train
CCCCCCOC(=O)CC,train
CC/C=C\CCOC(=O)C(C)C,train
CCCCCC(=O)OCCc1ccccc1,train
CC/C=C\CCCCCO,valid
CCCCCc1cccc(=O)o1,train
O=C([O-])c1ccccc1-c1c2cc(I)c(=O)c(I)c-2oc2c(I)c([O-])c(I)cc12,train
Cc1cc(C)c(/N=N/c2c(O)c(S(=O)(=O)[O-])cc3cc(S(=O)(=O)[O-])ccc23)cc1C,train
COC(=O)[C@H]1[C@H]2C[C@@H]3c4[nH]c5cc(OC)ccc5c4CCN3C[C@H]2C[C@@H](OC(=O)c2cc(OC)c(OC)c(OC)c2)[C@@H]1OC,train
COc1ccc2cc3[n+](cc2c1OC)CCc1cc2c(cc1-3)OCO2,test
CCN(CC)c1cc2[o+]c3cc(N(CC)CC)c(C)cc3c(-c3ccc(S(=O)(=O)[O-])cc3S(=O)(=O)[O-])c2cc1C,train
CC[N+](C)(CC)CC,train
O=C(NS(=O)(=O)/C=C/c1cccs1)c1ccc(F)cc1Br,train
CN1C(=O)N(c2ccccc2Br)Cc2cnc(Nc3ccc4c(c3)OCC(CO)O4)nc21,train
O=C([N-]S(=O)(=O)/C=C/c1ccccc1)c1ccc(F)cc1Cl,valid
CCN(CC)C(=O)c1cc(S(=O)(=O)Cc2ccccc2)c(N2CCC(c3ccccc3)CC2)cc1N(CC)CCN(C)C,train
CS(=O)(=O)c1ccc([C@@H](C[C@H]2CCC(=O)C2)C(=O)Nc2cnccn2)cc1Cl,train
CCCC[C@H]1CN(CC2CCOCC2)C(=O)OC12CCN(C1(C)CCN(C(=O)c3c(C)ncnc3C)CC1)CC2,train
O=C1C(Cl)C(CCl)CN1c1cccc(C(F)(F)F)c1,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12,test
Nc1ccc(Oc2ccc(N)cc2)cc1,train
CCOC(=O)COC(=O)c1cc(Oc2ccc(C(F)(F)F)cc2Cl)ccc1[N+](=O)[O-],train
C=CCOc1ccccc1OCC(O)CNC(C)C,train
O=C(O)CF,train
CCCN1C[C@H](CSC)C[C@@H]2c3cccc4[nH]cc(c34)C[C@H]21,valid
CN1C(C(=O)Nc2ccccn2)C(=O)c2sc(Cl)cc2S1(=O)=O,train
O=C1CCCCCCCCCCCCCCO1,train
COc1ccc(C2NC(=S)N3C(c4ccc(OC)c(OC)c4)NC(=S)N23)cc1OC,train
C[N+](C)(CCCCCC[N+](C)(C)C1c2ccccc2-c2ccccc21)C1c2ccccc2-c2ccccc21,train
c1ccc(CNCc2ccccc2)cc1,test
COc1cc2[nH]c(C)c(CCN3CCN(c4ccccc4)CC3)c2cc1OC,train
C/N=C(\NC)NCc1ccccc1.C/N=C(\NC)NCc1ccccc1,train
OCCNCc1ccccc1,train
O=C(Nc1ccc(S(=O)(=O)N2CCOCC2)cc1)c1cc(Cl)ccc1NS(=O)(=O)c1ccc(Cl)s1,train
CN[C@@]1(c2ccccc2Cl)CCCCC1=O,valid
ClCCN(CCCl)CCCl,train
COC(=O)Cc1ccccc1,train
CCCCCC[n+]1ccccc1,train
CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)O)=C(CSc3nnnn3C)CS[C@H]12)c1csc(N)n1.CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)O)=C(CSc3nnnn3C)CS[C@H]12)c1csc(N)n1,train
CC[N+]1(C)CCCC1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,test
CC[N+]1(C)CCCC1,train
CCCC[N+]1(C)CCCC1,train
CCCC[N+]1(C)CCCC1.N#CN=C=[N-],train
CCC[N+]1(C)CCCC1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
CCCCCCCCn1cc[n+](C)c1.F[B-](F)(F)F,valid
O=S(=O)(O)c1cc(I)c(O)c2ncccc12,train
CCCCCCCCCCCCCC/C(O)=N\C(CO)CCCCCCCCCCCCCC,train
CO[C@@]1(NC(=O)C2SC(=C(C(N)=O)C(=O)O)S2)C(=O)N2C(C(=O)O)=C(CSc3nnnn3C)CS[C@@H]21,train
CO[C@@]1(NC(=O)Cc2cccs2)C(=O)N2C(C(=O)O)=C(COC(N)=O)CS[C@@H]21,train
CN1CCC(=C2c3ccccc3CCc3sccc32)CC1,test
CO[C@H](C(=O)[C@@H](O)[C@@H](C)O)C1Cc2cc3cc(O[C@H]4C[C@@H](O[C@H]5C[C@@H](O)[C@H](O)[C@@H](C)O5)[C@@H](O)[C@@H](C)O4)c(C)c(O)c3c(O)c2C(=O)[C@H]1O[C@H]1C[C@@H](O[C@H]2C[C@@H](O[C@H]3C[C@](C)(O)[C@H](O)[C@@H](C)O3)[C@H](O)[C@@H](C)O2)[C@H](O)[C@@H](C)O1,train
C[n+]1ccccc1/C=N/O,train
CCCN[C@H]1CCc2nc(N)sc2C1,train
CC(C(=O)O)c1ccc2c(c1)Cc1cccnc1O2,train
O=C(Cl)CCl,valid
CC(=O)Nc1nnc(S(N)(=O)=O)s1,train
CC(=O)CC(c1ccc([N+](=O)[O-])cc1)c1c(O)c2ccccc2oc1=O,train
COc1ccc2c(c1)c(CC(=O)OCC(=O)O)c(C)n2C(=O)c1ccc(Cl)cc1,train
CC(=O)Nc1ccccc1,train
CC1=C2N=C(C=C3N=C(C(C)=C4[C@@H](CCC(N)=O)[C@](C)(CC(N)=O)[C@](C)([C@@H]5N=C1[C@](C)(CCC(=O)NC[C@@H](C)OP(=O)([O-])O[C@@H]1[C@@H](CO)O[C@H](n6cnc7cc(C)c(C)cc76)[C@@H]1O)[C@H]5CC(N)=O)N4[Co+]C#N)[C@@](C)(CC(N)=O)[C@@H]3CCC(N)=O)C(C)(C)[C@@H]2CCC(N)=O,test
CCNc1cc(O)ccc1C,train
CC(C)C(C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,train
COP(=S)(OC)Oc1ccc(SC)c(C)c1,train
[Fe+3],train
CC(C)(C)NC(=O)[C@H]1CC[C@H]2[C@@H]3CC[C@H]4NC(=O)C=C[C@]4(C)[C@H]3CC[C@]12C,valid
CN(C)C(=S)[S-].CN(C)C(=S)[S-].CN(C)C(=S)[S-].[Fe+3],train
OC(Cn1cncn1)(Cn1cncn1)c1ccc(F)cc1F,train
O=C(NCC1CCCCN1)c1cc(OCC(F)(F)F)ccc1OCC(F)(F)F,train
O=C(Nc1ccc2c(c1)Cc1ccccc1-2)C(F)(F)F,train
CN(C)C(=O)Nc1cccc(C(F)(F)F)c1,test
CS/C(C)=N/OC(=O)N(C)SN(C)C(=O)O/N=C(\C)SC,train
CCCCOCCOC(=O)COc1nc(Cl)c(Cl)cc1Cl,train
Cc1c(F)c(F)c(COC(=O)C2C(/C=C(\Cl)C(F)(F)F)C2(C)C)c(F)c1F,train
C#CCC1=C(C)[C@H](OC(=O)[C@H]2[C@H](C=C(C)C)C2(C)C)CC1=O,train
CC(C)(C)c1ccc(CSc2cnn(C(C)(C)C)c(=O)c2Cl)cc1,valid
CCCCOC(=O)c1ccccc1C(=O)OCC(CC)CCCC,train
Cc1c(Cl)cccc1Nc1ccccc1C(=O)O,train
CCCCCCCCCCOC(=O)c1ccccc1C(=O)OCCCCCCCCCC,train
O=C1NC(=O)C2CC=CCC12,train
C[C@H]1c2cccc(O)c2C(=O)C2=C(O)[C@]3(O)C(=O)C(C(N)=O)=C(O)[C@@H](N(C)C)[C@@H]3[C@@H](O)[C@@H]21,test
O=C1OC(=O)C2CC=CCC12,train
O=C1NC(=O)c2ccccc21,train
O=C1OC(=O)C2CCCCC12,train
C#CCN1CC(=O)N(COC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)C1=O,train
C=CCOC(Cn1ccnc1)c1ccc(Cl)cc1Cl,valid
COCc1cnc(C2=NC(C)(C(C)C)C(=O)N2)c(C(=O)O)c1,train
CC(C)C1(C)N=C(c2ncccc2C(=O)O)NC1=O,train
CCBr,train
O=[N+]([O-])c1cc(N(CCO)CCO)ccc1NCCO,train
O=C1/C(=C2\Nc3ccc(S(=O)(=O)[O-])cc3C2=O)Nc2ccc(S(=O)(=O)[O-])cc21,test
CC(C)C[C@H]1C(=O)N2CCC[C@H]2[C@]2(O)O[C@](NC(=O)[C@@H]3C=C4c5cccc6[nH]c(Br)c(c56)C[C@H]4N(C)C3)(C(C)C)C(=O)N12,train
OB(O)O,train
C[C@@H](O)[C@H]1C(=O)N2C(C(=O)O)=C(SCCNC=N)C[C@H]12,train
Nc1ccc(S(=O)(=O)Nc2cnc3ccccc3n2)cc1,train
O[C@H]1[C@H](O)[C@@H](O)[C@H](O)[C@@H](O)[C@H]1O,valid
O=C(O)[C@H](O)[C@@H](O)C(=O)O,train
CC1(C)C(/C=C\C=C/C=C\C=C2\N(CCCCS(=O)(=O)[O-])c3ccc4ccccc4c3C2(C)C)=[N+](CCCCS(=O)(=O)[O-])c2ccc3ccccc3c21,train
CC(=O)N(CC(C)C(=O)O)c1c(I)cc(I)c(N)c1I,train
COc1cc([C@@H]2c3cc4c(cc3C(O[C@@H]3O[C@@H]5COC(c6cccs6)O[C@H]5[C@H](O)[C@H]3O)C3COC(=O)[C@@H]32)OCO4)cc(OC)c1O,train
CCCC[N+]1(C)CCCCC1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,test
COCC(O)CO,train
O=C(Nc1ccc([N+](=O)[O-])cc1)Nc1ccc([N+](=O)[O-])cc1,train
CN1C(=O)C[C@@H](C(=O)N[C@@H](Cc2c[nH]cn2)C(=O)N2CCC[C@H]2C(N)=O)NC1=O,train
CC(=O)Nc1cc(S(=O)(=O)[O-])cc2cc(S(=O)(=O)[O-])c(N=Nc3ccccc3)c(O)c12,train
C[C@@H]1O[C@@H](O[C@H]2C[C@@H](O)[C@]3(CO)[C@H]4[C@H](O)C[C@]5(C)[C@@H](C6=CC(=O)OC6)CC[C@]5(O)[C@@H]4CC[C@]3(O)C2)[C@H](O)[C@H](O)[C@H]1O,valid
O=C(O)CC(O)C(=O)O,train
C=C(C)C(=O)OCCC[Si](Cl)(Cl)Cl,train
CCOP(=O)(SC(C)CC)N1CCSC1=O,train
COC(=O)c1ccccc1S(=O)(=O)NC(=O)Nc1nc(C)cc(C)n1,train
Cn1c(=O)c2[nH]c(Br)nc2n(C)c1=O,test
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3O[C@H]1C[C@H](N)[C@H](O)[C@H](C)O1,train
CC(C=O)Cc1ccc(C(C)C)cc1,train
O=C(CCl)Nc1ccccc1,train
COc1ccc(OC)c(Cl)c1,train
CCCCC(CC)(CO)CO,valid
Cc1cc(O)c(Cl)cc1C,train
NC(=O)c1ccc([N+](=O)[O-])cc1Cl,train
CC1=CCC2C(C1)C2(C)C,train
O=C1CC2CC1C1CCCC21,train
Nc1cccnc1,test
CC(C)OCCO,train
CCc1nccnc1C,train
CC(C)c1ccc2ncccc2c1,train
Cc1cccc(C(C)(C)C)c1O,train
CN(C)CCCN(C)C,valid
CC(C)CCO,train
C=C(C)C(=O)OCC(C)C,train
C=C(C)C(=O)OCCCCCCCC(C)C,train
CC(C)CCCCCCCOP(=O)(Oc1ccccc1)Oc1ccccc1,train
O=C=NC1CCCCC1,test
COS(=O)(=O)[O-].C[N+]1(C)CCC(=C(c2ccccc2)c2ccccc2)CC1,train
CC(=O)c1cccc([N+](=O)[O-])c1,train
CC(=O)c1ccccc1[N+](=O)[O-],train
CC(=O)c1ccc([N+](=O)[O-])cc1,train
Nc1cccc([N+](=O)[O-])c1,valid
Nc1ccccc1[N+](=O)[O-],train
CC(C)(C)NCC(O)CSc1nc(-c2ccc(C(N)=O)s2)cs1,train
Cc1cc(O)c2c(c1)O[C@@]1(C)CC[C@H]3C(C)(C)CCCC34CO[C@@H]2[C@H]41,train
COc1nc(N)nc2c1ncn2[C@@H]1O[C@H](CO)[C@@H](O)[C@@H]1O,train
COc1ccc2c(=O)c(C)c(-c3ccccc3)oc2c1CN(C)C,test
CC(=O)CCl,train
CCCOC(C(=O)OC1CCN(C)CC1)(c1ccccc1)c1ccccc1,train
COC(=O)[C@H]1[C@H]2C[C@@H]3c4[nH]c5cc(OC)ccc5c4CCN3C[C@H]2C[C@H](OC)[C@@H]1OC,train
CC(CN1c2ccccc2Sc2cccnc21)N(C)C,train
COc1ccc2c(c1)OC(C)(C)[C@@H](c1ccccc1)[C@@H]2c1ccc(OCCN2CCCC2)cc1,valid
C=C(C)c1ccccc1,train
O=C(Cl)c1c(Cl)cccc1Cl,train
CCCCCCCCCCCCN1CCOCC1,train
C1CN(CCOCCN2CCOCC2)CCO1,train
COc1ccc(C=O)cc1OC,test
CCCCCCC(=O)OCC,train
CCCC(=O)OCC,train
CCOC(=O)CC,train
Sc1nnc(S)s1,train
C=CC(=O)NC(C)(C)C,valid
[Cu+2],train
COc1ccc(Nc2ccc(OC)cc2)cc1,train
CCOP(=S)(OCC)O/N=C(\C#N)c1ccccc1,train
COC(=O)CCCCC(=O)OC,train
COCCOC(=O)c1ccccc1C(=O)OCCOC,test
COc1cc(CCN)c(OC)cc1Br,train
COc1cc(C[C@@H](C)N)c(OC)cc1Br,train
Cc1nc2ccccc2c(=O)n1-c1ccccc1Cl,train
C=CCC1([C@@H](C)CCC)C(=O)NC(=O)NC1=O,train
CC[C@H]1C[C@@H]2C[C@H]3c4[nH]c5ccc(OC)cc5c4CCN(C2)[C@@H]13,valid
CCC(N)Cc1c[nH]c2ccccc12,train
COc1cc(CCN)cc(OC)c1OC,train
CCCSc1cc(OC)c(CCN)cc1OC,train
COc1cc(C[C@H](C)N)c(OC)cc1Br,train
COc1cc(CC(C)C)cc(OC)c1OC,test
CCCN(CCC)c1c([N+](=O)[O-])cc(C(F)(F)F)cc1[N+](=O)[O-],train
CN1C(=O)OC(C)(C)C1=O,train
Cc1ccc(OP(=O)(Oc2ccc(C)cc2)Oc2ccc(C)cc2)cc1,train
ClCC(Cl)CCl,train
OCCOCCOCCO,valid
OCCN(CCO)CCO,train
CC(C)NCC(O)c1ccc(O)c(O)c1,train
Cc1cc(C)c(N)cc1C,train
CC1(C)CCC(Cc2ccc(Cl)cc2)C1(O)Cn1cncn1,train
N#CN,test
O=c1[nH]sc2ccccc12,train
CN1C(C(=O)Nc2ccccn2)=C(O)c2ccccc2S1(=O)=O,train
CC(=O)Nc1ccccc1C,train
CC(=O)Nc1cccc(C)c1,train
CCCCCCCC/C=C/CCCCCCCC(=O)OCCOCC(OCCO)C1OC(OCCO)CC1OCCO,valid
Brc1cc(-c2ccc(Br)c(Br)c2Br)cc(Br)c1Br,train
CC(=O)Nc1ccc(OCC(O)CNC(C)C)cc1,train
COc1ccc(-c2ccccc2)cc1NNC(=O)OC(C)C,train
C=CCCCCCCC=C,train
CCCC[Sn](CCCC)(CCCC)CCCC,test
CC(C)(O)C(=O)Nc1ccc([N+](=O)[O-])c(C(F)(F)F)c1,train
CC1=C2[C@H](/C=C(\C)C(=O)O)CC[C@@H](C)[C@H]2[C@H](O)C1,train
Cl[In](Cl)Cl,train
O=CO[AlH3](OC=O)OC=O,train
CCCCOC(=O)CO,valid
CCCC(=O)[O-],train
CN(C1CCCCC1)C1CCCCC1,train
Cc1ccnc(N)c1,train
CN(C)C(C)(C)CO,train
CC(=O)CC(=O)Nc1ccc(S(=O)(=O)[O-])cc1,test
CC(=O)O[AlH3](O)O,train
[As]#[In],train
CC1(C)CC(N=C=O)CC(C)(CN=C=O)C1,train
CN(C)CCCO[C@H]1[C@@H](C(O)CO)O[C@@H]2OC(C)(C)O[C@H]12,train
CCN(CC)CCOC(=O)C(c1ccccc1)c1ccccc1,valid
COS(=O)(=O)[O-],train
CN(Cc1cc(Br)cc(Br)c1N)C1CCCCC1,train
CCCCC(CC)COC(=O)/C=C/c1ccc(OC)cc1,train
COCCOC(=O)CC#N,train
CCCC(=O)OC(C)(C)Cc1ccccc1,test
COC(C)=O,train
ClC1=C(Cl)C2(Cl)C3C(Cl)C(Cl)CC3C1(Cl)C2(Cl)Cl,train
N#CCCN(CCO)CCO,train
CN1CCN(C(c2ccccc2)c2ccc(Cl)cc2)CC1,train
OCC1CC=CCC1,valid
COC(=O)C1=C(C)NC(C)=C(C(=O)O[C@H]2CCN(Cc3ccccc3)C2)[C@H]1c1cccc([N+](=O)[O-])c1,train
COC[C@H](c1ccc(C(F)(F)F)cc1)N1CCN(C2(C)CCN(C(=O)c3c(C)ncnc3C)CC2)C[C@H]1C,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)CO,train
Cc1c(-c2ccc(O)cc2)n(Cc2ccc(OCCN3CCCCCC3)cc2)c2ccc(O)cc12,train
COC(=O)[C@H]1[C@H]2C[C@@H]3c4[nH]c5ccccc5c4CCN3C[C@H]2C[C@@H](OC(=O)c2cc(OC)c(OC)c(OC)c2)[C@@H]1OC,test
CC(=O)O[C@H]1CC[C@@]2(C)C(=CC[C@H]3[C@@H]4CCC(=O)[C@@]4(C)CC[C@@H]32)C1,train
CC1(C)[C@@H](C=C(Br)Br)[C@H]1C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1,train
N#CCCCC(C#N)CCC#N,train
CC1=CC=C(C(C)C)CC1,train
COc1nc(C)nc(N)n1,valid
CC(C)c1cc(C(C)C)cc(C(C)C)c1,train
NCC1CCCC(CN)C1,train
O=C(Nc1ccccc1)Nc1ccnc(Cl)c1,train
CC(O)CC(C)O,train
c1ccc(OCCOc2ccccc2)cc1,test
Cc1ccc(S(=O)(=O)O)cc1,train
CN(C)C(=O)CCSC(SCCC(=O)[O-])c1cccc(/C=C/c2ccc3ccc(Cl)cc3n2)c1,train
CCCNC(C)C(=O)Nc1ccccc1C,train
FCOC(C(F)(F)F)C(F)(F)F,train
COS(=O)(=O)[O-].C[N+]1(C)CCCCC1COC(=O)C(O)(c1ccccc1)c1ccccc1,valid
FC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
O=C1c2cccc3c2[C@H](CCC3)CN1[C@@H]1CN2CCC1CC2,train
C=CCNC1=C2C[C@@H](C)C[C@H](OC)[C@H](O)[C@@H](C)C=C(C)[C@H](OC(N)=O)[C@@H](OC)C=CC=C(C)C(=O)NC(=CC1=O)C2=O,train
Clc1ccc(C(Cn2ccnc2)OCc2ccsc2Cl)c(Cl)c1,train
c1ccc2c(c1)ccc1ccccc12,test
CCOP(=S)(OCC)SCn1c(=O)oc2cc(Cl)ccc21,train
CCCCC/C=C\C/C=C\CCCCCCCC(=O)OCC,train
CC1=C2[C@H](/C=C(\C)C(=O)O)CC[C@@H](C)[C@H]2CC1,train
C=C[Si](OC(C)=O)(OC(C)=O)OC(C)=O,train
C#C[C@]1(O)C=C[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@@]21CC,valid
CC(=C\c1ccccc1)/C=C1/SC(=S)N(CC(=O)O)C1=O,train
CC(C)(C)c1ccc(C(=O)CCCN2CCC(OC(c3ccccc3)c3ccccc3)CC2)cc1,train
COc1cccc(N(C)C(=S)Oc2ccc3c(c2)CCCC3)n1,train
CC(CCc1ccc(O)cc1)NCC(O)c1ccc(O)cc1,train
O=C([O-])c1ccc(NC(=O)[C@H](Cc2ccc(O)cc2)NC(=O)c2ccccc2)cc1,test
COc1cc(C(=O)N2CCCCCCC2)cc(OC)c1OC,train
CO[C@H]1C[C@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3C(C)=CC[C@@H]4C[C@@H](C[C@]5(C=C[C@H](C)[C@@H](C6CCCCC6)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OCC(=CC=C[C@@H]3C)[C@@]45O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,train
CN1C(=O)N(C)C(O)C1O,train
CC(C)c1ccc(S(=O)(=O)O)cc1,train
CC(C)(c1ccc(OCC2CO2)cc1)c1ccc(OCC2CO2)cc1,valid
c1ccc(SCCSc2ccccc2)cc1,train
COCCOCCOC,train
CCCCNC(=O)OCC#CI,train
O=C(O)C1=NN(c2ccc(S(=O)(=O)O)cc2)C(=O)C1,train
NC(=O)NNC(N)=O,test
COC(=O)/C=C/C=C(C)/C=C/C=C(C)/C=C/C=C(C)/C=C/C=C(C)\C=C/C(=O)O,train
O=C1C=CC(=O)N1c1ccccc1,train
CCOC(=O)NC(C)(C)Cc1ccc(Cl)cc1,train
C=C(c1ccc(C(=O)O)cc1)c1cc2c(cc1C)C(C)(C)CCC2(C)C,train
CC(=O)C(=O)[O-],valid
COc1cc(S(C)=O)ccc1-c1nc2ncccc2[nH]1,train
COCCCCCCCCOCCC[Si](C)(O[Si](C)(C)C)O[Si](C)(C)C,train
CC(=O)OC1C2(C)CCC(C2)C1(C)C,train
O=S(=O)([O-])[O-].[Li+].[Li+],train
O=Cc1c2ccccc2c(Cl)c2ccccc12,test
CCCCCBr,train
CCCC[n+]1ccc(C)cc1,train
CC[N+]1(C)CCCC1.F[B-](F)(F)F,train
Cc1[nH]nc2c1N=C(c1ccccc1Cl)c1cc([N+](=O)[O-])ccc1N2,train
COC(=O)Nc1nc2cc(S(=O)c3ccccc3)ccc2[nH]1,valid
CC1CCC(=O)C1=O,train
CCN1C(=O)c2ccccc2C1Nc1ccc(OCCN2CCCCC2)cc1,train
O=C1CCCCCCCCCCOCCCCO1,train
CSSc1ccoc1C,train
Cc1occc(=O)c1OC(=O)C(C)C,test
CCCc1ccccc1O,train
CCC(=O)OCc1ccc(OC)cc1,train
CCCC1CCOC(C)S1,train
Cc1cc(Nc2ccccc2C(=O)O)ccc1Cl,train
CC(C)C[C@H]1C(=O)N2CCC[C@H]2[C@]2(O)O[C@](NC(=O)[C@@H]3C[C@@H]4c5cccc6[nH]cc(c56)C[C@H]4N(C)C3)(C(C)C)C(=O)N12,valid
CC(C)C[C@H](NC(=O)[C@H](CCc1ccccc1)NC(=O)CN1CCOCC1)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](CC(C)C)C(=O)[C@@]1(C)CO1,train
COC(=O)N[C@H](C(=O)N[C@@H](Cc1ccccc1)[C@@H](O)CN(Cc1ccc(-c2ccccn2)cc1)NC(=O)[C@@H](NC(=O)OC)C(C)(C)C)C(C)(C)C,train
O=C(Nc1ccc(Cl)cc1)Nc1ccc(Cl)c(C(F)(F)F)c1,train
CC(C)n1nc(-c2cc3cc(O)ccc3[nH]2)c2c(N)ncnc21,train
CC(=O)OCC(=O)[C@@]1(O)CCC2C3CCC4=CC(=O)CC[C@]4(C)C3[C@@H](O)C[C@@]21C,test
C1CN(P2(N3CC3)=NP(N3CC3)(N3CC3)=NP(N3CC3)(N3CC3)=N2)CCO1,train
CC(C)(C)C(=O)OCOC(=O)C1N2C(=O)CC2S(=O)(=O)C1(C)C,train
CC(C)NCC(O)c1ccc([N+](=O)[O-])cc1,train
O=C1CC2(CCCC2)CC(=O)N1CCCCN1CCN(c2ncccn2)CC1,train
CS(=O)(=O)Cl,valid
Cc1cccc(C(=O)O)c1,train
O=C(O)c1cccc(O)c1,train
Cc1ccc(C(=O)O)cc1,train
CCC(C(=O)OCCN(CC)CC)c1ccccc1,train
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)CO,test
COc1cc([C@@H]2c3cc4c(cc3[C@H](O)[C@H]3COC(=O)[C@H]23)OCO4)cc(OC)c1OC,train
NNS(=O)(=O)c1ccc(Oc2ccc(S(=O)(=O)NN)cc2)cc1,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@H]1C(=O)CO,train
CC(=O)OC(C)(C)C1CC=C(C)CC1,train
Cl[Si](Cl)(c1ccccc1)c1ccccc1,valid
COCCOC,train
CCOCCOCC,train
O=[N+]([O-])c1cccc2ccccc12,train
O=C(O)CCC(=O)C(=O)O,train
S=C([S-])NCCNC(=S)[S-],test
O=C1NCCN1,train
C[N+](=O)[O-],train
CCCCC(CC)COC(=O)c1ccccc1C(=O)OCC(CC)CCCC,train
O=[N+]([O-])c1ccc(Oc2ccc(Cl)cc2Cl)cc1,train
CCCCC(CC)CO,valid
C1CC2OC2CC1C1CO1,train
CC(C)N(C(=O)COc1nnc(C(F)(F)F)s1)c1ccc(F)cc1,train
CCN(Cc1c(F)cccc1Cl)c1c([N+](=O)[O-])cc(C(F)(F)F)cc1[N+](=O)[O-],train
Cc1nn(C)c(Oc2ccccc2)c1/C=N/OCc1ccc(C(=O)OC(C)(C)C)cc1,train
O=[N+]([O-])c1cc(C(F)(F)F)c(Cl)c([N+](=O)[O-])c1Nc1ncc(C(F)(F)F)cc1Cl,test
COC(=O)CSc1cc(/N=c2\sc(=O)n3n2CCCC3)c(F)cc1Cl,train
CCCCCOC(=O)COc1cc(N2C(=O)C3=C(CCCC3)C2=O)c(F)cc1Cl,train
C#CCN1C(=O)COc2cc(F)c(N3C(=O)C4=C(CCCC4)C3=O)cc21,train
CO/N=C(/C(=O)OC)c1ccccc1COc1ccccc1C,train
CC1(C)[C@@H](/C=C(\Cl)C(F)(F)F)[C@H]1C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1,valid
CCCCN(CCCC)CCC(O)c1cc2c(Cl)cc(Cl)cc2c2cc(C(F)(F)F)ccc12,train
CN[C@@H]1[C@@H](O[C@H]2O[C@H](CO)[C@@H](N)[C@H](O)[C@H]2O)O[C@H]2C[C@@H](N)[C@@H](O[C@@H]3[C@@H](N)C[C@@H](N)[C@H](O)[C@H]3O)O[C@@H]2[C@@H]1O,train
CCOC(=O)OC(C)OC(=O)[C@@H]1N2C(=O)[C@@H](NC(=O)[C@H](N)c3ccccc3)[C@H]2SC1(C)C,train
CC(C)(C)NC[C@H](O)COc1ccccc1C1CCCC1.CC(C)(C)NC[C@H](O)COc1ccccc1C1CCCC1,train
C[C@@H]1C[C@H]2[C@@H]3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@@]3(Cl)[C@@H](O)C[C@]2(C)[C@H]1C(=O)COC(=O)C(C)(C)C,test
CCCC(=O)Nc1ccc(OCC(O)CNC(C)C)c(C(C)=O)c1,train
Cc1cc(NC(=O)C2=C(O)c3ccccc3S(=O)(=O)N2C)no1,train
CCC(=O)O[C@H]1[C@H](C)O[C@@H](O[C@@H]2[C@@H](C)O[C@@H](O[C@@H]3[C@@H](OC)[C@H](OC(=O)CC)CC(=O)O[C@H](C)CC=CC=C[C@H](O)[C@H](C)C[C@@H]3CC=O)[C@H](O)[C@H]2N(C)C)C[C@@]1(C)O,train
CC(C(=O)O)c1ccc(C(=O)c2cccs2)cc1,train
COc1ccc2c(c1)N(C[C@H](C)CN(C)C)c1ccccc1S2,valid
FC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
C=CC(=O)O[Sn](CCCC)(CCCC)CCCC,train
CNC(=O)Oc1ccccc1OC(C)C,train
C#CC(C)(C)O,train
CCCCCCCCCCO,test
CCCCCCCCCCCCCO,train
CCCCCCCCO,train
CCOCCOCCO,train
CCCCCCCCCC(C)=O,train
CCCCCCCCCCCCCOC(=O)CCSCCC(=O)OCCCCCCCCCCCCC,valid
CC12CCCC=C1C(=O)OC2=O,train
CC(C)(c1ccccc1)c1ccc(Nc2ccc(C(C)(C)c3ccccc3)cc2)cc1,train
O=C1C(CCS(=O)c2ccccc2)C(=O)N(c2ccccc2)N1c1ccccc1,train
COc1cc(NS(=O)(=O)c2ccc(N)cc2)nc(C)n1,train
Cc1nnc(NS(=O)(=O)c2ccc(N)cc2)s1,test
Cc1ccnc(NS(=O)(=O)c2ccc(N)cc2)n1,train
COc1cnc(NS(=O)(=O)c2ccc(N)cc2)nc1,train
COc1ccc(NS(=O)(=O)c2ccc(N)cc2)nn1,train
CC(=O)CC(=O)Nc1ccccc1,train
CC(=O)O,valid
CC(=O)OC(C)=O,train
CC(=O)Nc1ccc(C(=O)O)cc1,train
CC(=O)C(C)O,train
COc1ccc(NC(C)=O)cc1N,train
CCCCC(CC)COC(=O)c1ccc(C(=O)OCC(CC)CCCC)cc1,test
O=P(O)(O)CN(CP(=O)(O)O)CP(=O)(O)O,train
CCC(C)S,train
CC1=C2C[C@H]3[C@@H](CC=C4C[C@@H](O)CC[C@@]43C)[C@@H]2CC[C@]12O[C@@H]1C[C@H](C)CN[C@H]1[C@H]2C,train
O=C(CCN1CCC(c2ccccc2)C1)c1ccc2c(c1)OCCO2,train
O=[N+]([O-])c1ccc(O)c2ncccc12,valid
Nc1ccc(S(=O)(=O)Nc2ccccc2)cc1,train
CCc1nnc(NS(=O)(=O)c2ccc(N)cc2)s1,train
Cc1c(Cn2ccnc2)c2ccccc2n1CCC(=O)O,train
O=C(COc1ccc(Cl)cc1)OCCNC12CC3CC(CC(C3)C1)C2,train
COC(c1ccccc1)(c1ccccc1)[C@H](Oc1nc(C)cc(C)n1)C(=O)O,test
CCOC(=O)c1c(C)n(C)c2ccc(O)cc12,train
CN1CCN(C(=O)C2CCCCC2)CC1,train
NCCCNCCCN,train
O=[N+]([O-])c1cccc(I)c1,train
CCCCCCCCCCCCCCCCCCO,valid
C=CCCCCCCCCCCCCCCCC,train
COc1ccc(NC(=O)CC(C)=O)cc1,train
CCCCCCCCCCCCCCCC(=O)OC,train
COC(=O)CC(=O)OC,train
CN1CCOCC1,test
CCCC(C)C,train
[Ni+2].c1cc[cH-]c1.c1cc[cH-]c1,train
CCCCCCCC[N+](CCCCCCCC)(CCCCCCCC)CCCCCCCC,train
CN1CCC[C@@H]1Cc1c[nH]c2ccc(CCS(=O)(=O)c3ccccc3)cc12,train
O=C(C[C@@H]1NCCC[C@H]1O)Cn1cnc2cc(Br)c(Cl)cc2c1=O,valid
C/C(=C\c1csc(C)n1)C1C[C@@H]2O[C@]2(C)CCC[C@H](C)C(O)[C@@H](C)C(=O)C(C)(C)[C@@H](O)CC(=O)O1,train
Clc1cc(Cl)c(OCC#CI)cc1Cl,train
N=C(N)NCCC[C@H](NC(=O)[C@@H]1CCCN1C(=O)[C@@H]1CSSCCC(=O)N[C@@H](Cc2ccc(O)cc2)C(=O)N[C@@H](Cc2ccccc2)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CC(N)=O)C(=O)N1)C(=O)NCC(N)=O,train
O=[N+]([O-])c1c(Cl)cccc1-c1c[nH]cc1Cl,train
CCCCC[C@@H](O)C1C(=O)OC(C)[C@H](O)C=CC=CC=CC=CC=C(C)C(O)C(O)C(O)C[C@H](O)C[C@H](O)C[C@H](O)C[C@H](O)C[C@@H]1O,test
CNC(C)CCC=C(C)C,train
NC(=S)NS(=O)(=O)c1ccc(N)cc1,train
COC(=O)/C=C/c1ccccc1,train
CN(C)c1ccc(/N=N/c2ccccc2C(=O)O)cc1,train
CON(C)C(=O)Nc1ccc(Br)cc1,valid
CO[C@H]1[C@H]([C@]2(C)O[C@@H]2CC=C(C)C)[C@]2(CC[C@H]1OC(=O)NC(=O)CCl)CO2,train
CCCC(=O)Nc1c(I)cc(I)c(CC(CC)C(=O)[O-])c1I,train
COC(=O)[C@H]1[C@@H](O)CC[C@H]2CN3CCc4c([nH]c5ccccc45)[C@@H]3C[C@@H]21,train
CC[C@]12CCCN3CCc4c(n(c5ccccc45)[C@@](O)(C(=O)OC)C1)[C@@H]32,train
CC(=NC#N)N(C)Cc1ccc(Cl)nc1,test
CCCCCCC(C)OC(=O)COc1nc(F)c(Cl)c(N)c1Cl,train
C=CC(=O)Nc1cc2c(Nc3ccc(F)c(Cl)c3)ncnc2cc1OCCCN1CCOCC1,train
CC(=O)O[C@H]1CC[C@@]2(C)C(=CC[C@H]3[C@@H]4CC[C@H](C(C)=O)[C@@]4(C)CC[C@@H]32)C1,train
c1cnc2cc3c(cc2n1)C1CNCC3C1,train
Clc1ccc([C@@H]2C[C@H]3CC[C@@H]2N3)cn1,valid
CCC(=O)N(c1ccccc1)C1CCN(CCc2cccs2)CC1,train
CCC(=O)N(c1ccccc1)C1CCN(Cc2cccs2)CC1,train
CCC(=O)N(c1ccccc1)C1CCN(C(C)Cc2cccs2)CC1,train
CCC(=O)N(c1ccccc1)C1CCN(CC(O)c2ccccc2)CC1,train
Nc1c(Cl)cc(Cl)cc1Cl,test
O=C(O)C(Cl)(Cl)Cl,train
FC(F)(Cl)C(F)(Cl)Cl,train
CCCCCCCC(=O)OCC(COC(=O)CCCCCCC)OC(=O)CCCCCCC,train
BrC(Br)Br,train
Nc1nc(N)c2nc(-c3ccccc3)c(N)nc2n1,valid
Nc1ccc(Oc2ccc(N)c(N)c2)cc1,train
CC1(C)O[C@@H]2C[C@H]3[C@@H]4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1,train
O=S1OC[C@@H]2[C@H](CO1)[C@]1(Cl)C(Cl)=C(Cl)[C@@]2(Cl)C1(Cl)Cl,train
Nc1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,train
Cc1cc2nc(N)sc2cc1C,test
Nc1cc(Cl)ccc1O,train
Nc1cc(Cl)c([N+](=O)[O-])cc1O,train
Cc1cc(=O)n(-c2ccccc2)n1C,train
CCOc1ccc(NC(C)=O)cc1,train
Nc1cc(Cl)cc(S(=O)(=O)O)c1O,valid
CC/C=C/CCO,train
O=Cc1ccc(Cl)cc1Cl,train
c1ccc(SSc2ccccc2)cc1,train
CCOC(=O)c1cccc(N)c1,train
CC/C=C\CCO,test
O=C(O)CNC(=O)c1ccc(CO)o1,train
COc1ccc(OC)c(NC(=O)CC(C)=O)c1,train
COP(=O)(NC(C)=O)SC,train
C1=Cc2cccc3cccc1c23,train
ON(Cc1ccccc1)Cc1ccccc1,valid
N#Cc1ccc(Cl)cc1,train
CC(C)CCCC(=O)O,train
COC(=O)c1ccccc1Cl,train
NCCN1CCNC1=O,train
CC1=C(Br)C(=O)C(Br)=C/C1=C(\c1ccccc1S(=O)(=O)[O-])c1cc(Br)c(O)c(Br)c1C,test
CSc1nc2cc(Cl)c(Oc3cccc(Cl)c3Cl)cc2[nH]1,train
O=S(=O)([O-])c1cc(O)c(O)c(S(=O)(=O)[O-])c1,train
COC(C)(C)CCCC(C)CC=O,train
NC(=O)c1ccc(N)cc1,train
ClP(Cl)(Cl)(Cl)Cl,valid
CCC(Cl)CC(Cl)C(Cl)C(Cl)C(Cl)C(C)Cl,train
C=C[Si](C)(C)O[Si](C)(C)C=C,train
NNC(=O)c1cccc(C(=O)NN)c1,train
O=[N+]([O-])C=Cc1ccccc1,train
COP(=O)(OC)O/C(=C\Cl)c1cc(Cl)c(Cl)cc1Cl,test
CCOP(=O)(Sc1ccccc1)Sc1ccccc1,train
CN1CCCCC1CCN1c2ccccc2Sc2ccc(S(C)=O)cc21,train
c1ccc2c(c1)Sc1ccccc1N2CC1CN2CCC1CC2,train
CCCC(C)(COC(N)=O)COC(N)=O,train
C[C@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@H]3C(=O)C[C@]2(C)[C@@]1(O)C(=O)CO,valid
Cc1cc(C)cc(OCC2CNC(=O)O2)c1,train
N#CSCc1ccccc1,train
O=C(O[C@@H](C(=O)O)[C@@H](OC(=O)c1ccccc1)C(=O)O)c1ccccc1,train
O=C([O-])[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C([O-])[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.[Ba+2],train
O=C(CO)[C@@H](O)[C@H](O)[C@@H](O)C(=O)[O-].O=C(CO)[C@@H](O)[C@H](O)[C@@H](O)C(=O)[O-],test
O=S(=O)(O)c1cc(Cl)c(Cl)cc1Cl,train
CNC.O=C(O)COc1ccc(Cl)cc1Cl,train
Nc1ccn([C@@H]2O[C@H](CO)[C@@H](O)[C@@H]2O)c(=O)n1,train
Cc1ccc(C(C)(C)O)cc1,train
CCCC(=O)OC(=O)CCC,valid
CC(C)=CCC/C(C)=C\CO,train
COc1cc(-c2ccc(N)c(OC)c2)ccc1N,train
CCCCCCC(O)CCCCCCCCCCC(=O)O,train
CCCCOC(=O)/C=C\C(=O)OCCCC,train
CC(C)=CCC/C(C)=C/CO,test
CC(C)=CCCC(C)CCO,train
CCCCCCCC[N+](C)(C)CCCCCCCC,train
CCCCON=O,train
CN1[C@H]2CC[C@@H]1C[C@H](OC(=O)C(CO)c1ccccc1)C2.CN1[C@H]2CC[C@@H]1C[C@H](OC(=O)C(CO)c1ccccc1)C2,train
C[C@@H](O)[C@H]1C(=O)N2C(C(=O)O)=C(S[C@@H]3CN[C@H](CNS(N)(=O)=O)C3)[C@H](C)[C@H]12,valid
c1cnccn1,train
C1CS1,train
O=c1[nH]cnc2ccccc12,train
O=C(C(=O)c1ccco1)c1ccco1,train
CC(=O)OCC(=O)NCCCOc1cccc(CN2CCCCC2)c1,test
CNC[C@H](O)c1ccc(O)c(O)c1,train
O=c1cccccc1O,train
N=C(N)NCCCC[C@@H]1NC(=O)CCSSC[C@@H](C(N)=O)NC(=O)[C@@H]2CCCN2C(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CC(=O)O)NC(=O)CNC1=O,train
CCCN(CCOc1c(Cl)cc(Cl)cc1Cl)C(=O)n1ccnc1,train
CSc1nc(NC(C)C)nc(NC(C)C)n1,valid
CC(C)N(C(=O)CCl)c1ccccc1,train
N[C@@H](Cc1ccc(N(CCCl)CCCl)cc1)C(=O)O,train
C[C@@]12C=CC[C@H]1[C@@H]1CC[C@H]3CC(=O)CC[C@]3(C)[C@H]1CC2,train
C=CCc1cc(OC)c(O)c(C(=O)NCCO)c1,train
Nc1nc(=O)c2ncn(CCC(CO)CO)c2[nH]1,test
CC(C)C[C@H](NC(=O)[C@@H](Cc1c[nH]c2ccccc12)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@H](CO)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)[C@H](Cc1c[nH]cn1)NC(=O)[C@@H]1CCC(=O)N1)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N1CCC[C@@H]1C(=O)NCC(N)=O.O=C(O)c1cc2ccccc2c(Cc2c(O)c(C(=O)O)cc3ccccc23)c1O,train
Cn1c(NCCN(CCO)CCCc2ccc([N+](=O)[O-])cc2)cc(=O)n(C)c1=O,train
CCCCC(=O)OCC(=O)[C@]1(O)Cc2c(O)c3c(c(O)c2[C@@H](O[C@H]2C[C@H](NC(=O)C(F)(F)F)[C@H](O)[C@H](C)O2)C1)C(=O)c1c(OC)cccc1C3=O,train
O=C(CS(=O)C(c1ccccc1)c1ccccc1)NO,train
CCOC(=O)COc1ccc2c(C)c(CCN(CC)CC)c(=O)oc2c1Cl,valid
CO[C@H]1C=CO[C@@]2(C)Oc3c(C)c(O)c4c(c3C2=O)C2=NC3(CCN(CC(C)C)CC3)NC2=C(NC(=O)C(C)=CC=C[C@H](C)[C@H](O)[C@@H](C)[C@@H](O)[C@@H](C)[C@H](OC(C)=O)[C@@H]1C)C4=O,train
Cc1c(O)cccc1C(=O)N[C@@H](CSc1ccccc1)[C@H](O)CN1C[C@H]2CCCC[C@H]2C[C@H]1C(=O)NC(C)(C)C,train
CCOC(=O)c1ccccc1OC,train
CCCCCCCOC(C)=O,train
CCCCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,test
Brc1ccc(-c2ccccc2)cc1,train
OCC(CBr)(CBr)CBr,train
O=C(O)C(Cl)Br,train
O=C(O)C(Cl)(Cl)Br,train
Nc1c(Br)cc([N+](=O)[O-])cc1[N+](=O)[O-],valid
NC(CCC(=O)O)C(=O)O,train
CC[n+]1ccccc1,train
CCCC[n+]1ccccc1.F[B-](F)(F)F,train
OCCCCCCCl,train
COC(=O)C(C)N(C(=O)Cc1ccccc1)c1c(C)cccc1C,test
c1ccc(-c2ccccn2)nc1,train
Clc1ccc(C2(Cn3cncn3)OCCO2)c(Cl)c1,train
CCCCCCCCCCCCCCCC(=O)OC[C@H](O)[C@H]1OC(=O)C(O)=C1O,train
O=C[C@@H](O)[C@H](O)[C@H](O)CO,train
O=C(Nc1cccc(Cl)c1)OCC#CCCl,valid
CSc1nc(N=[N+]=[N-])nc(NC(C)C)n1,train
CN1[C@H]2CC[C@@H]1CC(OC(=O)c1c[nH]c3ccccc13)C2,train
Nc1ccc(S(=O)(=O)Nc2ccnn2-c2ccccc2)cc1,train
Nc1ccc(S(=O)(=O)Nc2ncccn2)cc1,train
CC1CN(c2cc3c(cc2F)c(=O)c(C(=O)O)cn3-c2ccc(F)cc2F)CCN1,test
c1ccc2c(c1)sc1ccccc12,train
Oc1ccccc1F,train
Fc1ccccc1-c1ccccc1,train
Oc1cccc(Nc2ccccc2)c1,train
C=CCC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,valid
CC(C)=CC(=O)C=C(C)C,train
CC(C)(CCC(=O)O)C(=O)O,train
CC(=O)CCC=C(C)CCC=C(C)C,train
CC(C)(CC(=O)O)C(=O)O,train
CC(=O)C(C)=O,test
CC(=O)C1=C(C)[C@@H]2C[C@]3(C1)[C@@H](CC[C@H]3C)C2(C)C,train
C=COCC(C)C,train
C=C(C)C(=O)OCCOCCOCCOC(=O)C(=C)C,train
CC(C)=CC=O,train
CC(C)(C#N)C(C)(C)C#N,valid
CN(C)c1ccc(N(C)C)cc1,train
Cc1cc(-c2cc(C)c(N)c(C)c2)cc(C)c1N,train
CSSC,train
CCOc1ccccc1N,train
CC1=C(/C=C/C(C)=C/C=C/C(C)=C/C=C\C=C(C)\C=C\C=C(C)\C=C\C2=C(C)C(=O)CCC2(C)C)C(C)(C)CCC1=O,test
CC(C)CC(C)Nc1ccc(Nc2ccccc2)cc1,train
C=C1CC[C@H](O)C/C1=C/C=C1\CCC[C@@]2(C)[C@H]1CC[C@@H]2[C@H](C)CCCC(C)(C)O,train
CCC(C)(C)C,train
C=C1/C(=C\C=C2/CCC[C@@]3(C)[C@H]2CC[C@@H]3[C@H](C)CCCC(C)(C)O)C[C@@H](O)C[C@@H]1O,train
CC(Cl)(Cl)[N+](=O)[O-],valid
CCCCCCCCC=O,train
CC(C)C(=O)O,train
O=[N+]([O-])C(Br)Br,train
CCC(C)C(C)=O,train
CC(C)C=O,test
CC(CCC(=O)O)C(=O)O,train
CCCC(C)C(=O)O,train
Cc1cc(N)no1,train
O=C(O)Cc1nn(Cc2ccc(Br)cc2F)c(=O)c2ccccc12,train
C=CCN1C[C@H](C)N([C@H](c2ccc(C(=O)N(CC)CC)cc2)c2cccc(OC)c2)C[C@H]1C,valid
CNCCCCOc1ccccc1Cc1ccccc1,train
Cc1ccc2c(c1)c1c3n2CCNC3CCC1,train
CCN(CC)CCOc1ccc(Cc2ccccc2)cc1,train
CCN1CCC[C@H]1CNC(=O)c1c(OC)ccc(Br)c1OC,train
CCCCCCCC/C=C\CCCCCCCC(=O)NCc1ccc(O)c(OC)c1,test
CCC1(c2ccc(N)cc2)CCC(=O)NC1=O,train
Nc1nc(N)c2nc(CNc3ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc3)cnc2n1,train
CCOC(=O)Nc1cccc(OC(=O)Nc2ccccc2)c1,train
CC[N+](CC)(CCNC(=O)C(=O)NCC[N+](CC)(CC)Cc1ccccc1Cl)Cc1ccccc1Cl,train
CC1(C)S[C@@H]2[C@H](/N=C/N3CCCCCC3)C(=O)N2[C@H]1C(=O)O,valid
Cc1ccc([N+](=O)[O-])cc1N,train
NCC[C@H](O)C(=O)N[C@@H]1C[C@H](N)[C@@H](O[C@H]2O[C@H](CN)[C@@H](O)[C@H](O)[C@H]2O)[C@H](O)[C@H]1O[C@H]1O[C@H](CO)[C@@H](O)[C@H](N)[C@H]1O,train
O=P(O)(O)c1ccccc1,train
C(=C/C=N/c1ccccc1)\C=C\Nc1ccccc1,train
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@@H]2OC(=O)CCc1ccccc1,test
CC(=O)OCC(=O)[C@@]1(O)[C@H](OC(C)=O)C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
CC(C)c1ccccc1O,train
CNC(=S)NN,train
CC(=N\O)/C(C)=N/O,train
COc1ccc2ccccc2c1,valid
CCCCCCCCCCCCBr,train
CC(=O)N[C@@H](C=O)[C@H](O)[C@H](O)[C@@H](O)CO,train
O=S(=O)(O)C(F)(F)F,train
COCCOCCOCCOCCOC,train
O=C(OCCOCCO)c1ccccc1Nc1cccc(C(F)(F)F)c1,test
c1ccc([Bi](c2ccccc2)c2ccccc2)cc1,train
Nc1c(Br)cc(Br)cc1CN[C@H]1CC[C@H](O)CC1,train
CC(=O)OCC(=O)[C@H]1CC[C@H]2[C@@H]3CC[C@H]4C[C@H](O)CC[C@]4(C)[C@H]3C(=O)C[C@]12C,train
CC(C)(Sc1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1)Sc1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,train
CN1CCOC(c2ccccc2)c2ccccc2C1,valid
CCO[Si](C)(OCC)OCC,train
CCCCC[Si](OCC)(OCC)OCC,train
CCCNC(C)C(=O)Nc1c(C)csc1C(=O)OC,train
CC(=O)OC1CC(C)OC(C)O1,train
COc1ccc(N)c(OC)c1,test
COc1cc(-c2ccc(N=C=O)c(OC)c2)ccc1N=C=O,train
O=C(O)CCCCCCCCCCC(=O)O,train
CCC(C)NC(N)=O,train
CCSCCCl,train
CCCC[Sn](Cl)(Cl)CCCC,valid
C=C(C)C(=O)OCC(CC)CCCC,train
CO[Si](OC)(OC)OC,train
CC(COc1ccccc1)N(CCCl)Cc1ccccc1,train
[C-]#N.[Cu+],train
COc1nc(C)nc(NC(=O)NS(=O)(=O)c2ccccc2Cl)n1,test
O=C1c2ccccc2C(=O)C1c1ccccc1,train
Clc1ccc(Cl)c(Cl)c1,train
OCCN(CCO)c1ccccc1,train
O=C(c1ccccc1)c1ccccc1,train
CCOc1cc(C=O)ccc1O,valid
COc1cc(C=O)ccc1O,train
CC(C)(C)C(=O)CCl,train
COC1[C@@H](O[C@@H]2O[C@H](C)[C@@H](O[C@H]3C[C@@](C)(O)[C@H](OC(=O)CC(C)C)[C@H](C)O3)[C@H](N(C)C)[C@H]2O)[C@@H](CC=O)C[C@@H](C)[C@@H](O)C=CC=CC[C@@H](C)OC(=O)C[C@H]1OC(C)=O,train
CCC(C)n1ncn(-c2ccc(N3CCN(c4ccc(OC[C@H]5CO[C@](Cn6cncn6)(c6ccc(Cl)cc6Cl)O5)cc4)CC3)cc2)c1=O,train
CC[C@H](C)[C@H]1O[C@]2(CC[C@@H]1C)C[C@@H]1C[C@@H](CC=C(C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H](O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)C=CC=C3CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,test
CCCCC(CC)COP(=O)(O)O,train
O=C(c1ccc(F)cc1)C1CCN(CCn2c(=O)[nH]c3ccccc3c2=O)CC1,train
CCC(C)(C#N)/N=N/C(C)(C#N)CC,train
CC(S)C(=O)NCC(=O)O,train
Clc1ccc2nsnc2c1NC1=NCCN1,valid
O=C(O)COc1ccc(C(=O)c2cccs2)c(Cl)c1Cl,train
COC1CC(=C(c2cccs2)c2cccs2)C[N+](C)(C)C1,train
O=C(CCCN1CCC(n2c(=S)[nH]c3ccccc32)CC1)c1ccc(F)cc1,train
CC(C)(C)NC[C@H](O)COc1nsnc1N1CCOCC1,train
CCS(=O)(=O)CCn1c([N+](=O)[O-])cnc1C,test
C=CCCCCCCCCC(=O)O,train
O=C1CCCO1,train
[Cd+2],train
CC(C)(C)c1ccc(O)cc1,train
CC(C)(C)c1cc(O)ccc1O,valid
CC1CC(=O)O1,train
CCCCNC(N)=O,train
CCN(CC)c1ccc(C(=O)c2ccccc2C(=O)O)c(O)c1,train
Nc1cc(S(=O)(=O)[O-])cc2cc(S(=O)(=O)O)cc(O)c12,train
N#CCN(CC#N)CCN(CC#N)CC#N,test
CC(O)C(CO)C(C)O,train
COCCOCCOCCOCCOCCO,train
O=C(O)c1cc(Cl)cc([N+](=O)[O-])c1Cl,train
CCCCCC=CCC1CC(=O)OC1=O,train
CCCCCCCCCCCCCN1CC(C)OC(C)C1,valid
COc1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,train
Clc1cc(Cl)nc(Cl)n1,train
C[n+]1ccn(CCO)c1.F[B-](F)(F)F,train
CC(CC(=O)Cl)CC(C)(C)C,train
CC[n+]1ccccc1.O=S(=O)([O-])C(F)(F)F,test
CCCCCCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
CCCC[N+]1(C)CCCCC1,train
CC[C@H](N)c1ccccc1,train
Clc1ccnc(Cl)n1,train
CCCC[n+]1ccccc1.O=S(=O)([O-])C(F)(F)F,valid
CC/C=C\CCCCCCCCCCOC(C)=O,train
CC(C)(c1ccccc1)c1cc(-n2nc3ccccc3n2)c(O)c(C(C)(C)c2ccccc2)c1,train
CC/C=C/C/C=C/C/C=C/CCCCCCCC(=O)OCC,train
CC(=O)c1ccc2c(c1)Cc1ccccc1-2,train
NC(=O)c1cnccn1,test
CCCCC(CC)COCC1CO1,train
CC(=O)Oc1cccc2c1C(=O)c1c(OC(C)=O)cc(C(=O)O)cc1C2=O,train
CC(C)(C)C(=O)C1C(=O)c2ccccc2C1=O,train
C=C(C)C(=O)OCC(C)O,train
CN1C(CSCC(F)(F)F)Nc2cc(Cl)c(S(N)(=O)=O)cc2S1(=O)=O,valid
CCCCCCCCBr,train
CCCCCCCCCCCCO,train
C=CCCCCCCCCCC,train
CCCCCCCCCCCO,train
COCCOCCOCCO,test
CCCCCCCCCCCC,train
COc1cc(S(=O)(=O)[O-])c(C)cc1/N=N/c1c(O)ccc2cc(S(=O)(=O)[O-])ccc12,train
CC1=C(/C=C/C(C)=C/C=C/C(C)=C/C=C/C=C(C)/C=C/C=C(C)/C=C/C2=C(C)C[C@@H](O)CC2(C)C)C(C)(C)C[C@H](O)C1,train
CC(C)CCC(=O)O[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]12C,train
CC(=O)OCC(=O)[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)C3=CC[C@@]21C,valid
COc1ccc(-c2nc3cc(C4=NNC(=O)CC4C)ccc3[nH]2)cc1,train
Nc1nc(=O)c(Br)c(-c2ccccc2)[nH]1,train
CN(c1nccc(=O)[nH]1)C1CCN(c2nc3ccccc3n2Cc2ccc(F)cc2)CC1,train
Cc1oc(C)c(C(=O)Nc2ccccc2)c1C,train
c1ccc([Hg]c2ccccc2)cc1,test
C[N+](C)(C)CC(O)CCl,train
CC1=C(/C=C/C(C)=C\C=C/C(C)=C/C(=O)O)C(C)(C)CCC1,train
C=C(C)C(=O)NCCCN(C)C,train
OC[C@H]1O[C@@H](Oc2ccc(O)cc2)[C@H](O)[C@@H](O)[C@@H]1O,train
O=C(NCNC(=O)NC1C(=O)NC(=O)N1CO)NC1C(=O)NC(=O)N1CO,valid
C=C(C)C(=O)OCC(CC)(COC(=O)C(=C)C)COC(=O)C(=C)C,train
O=P(Cl)(Oc1ccccc1)Oc1ccccc1,train
CCCCCCCCC(CCCCCC)C(=O)O,train
Oc1ccc(Cc2ccccc2O)cc1,train
CC[C@H](OC(C)=O)C(C[C@@H](C)NC)(c1ccccc1)c1ccccc1,test
CO[Si](CCCn1c(=O)n(CCC[Si](OC)(OC)OC)c(=O)n(CCC[Si](OC)(OC)OC)c1=O)(OC)OC,train
CCC(CO)CO,train
CC(CN)CC(C)(C)CCN,train
CC(C)(C)OC(=O)c1ccc(O)cc1,train
C=Cc1ccc(OC(C)=O)cc1,valid
CC(C)CC(C)(N)C#N,train
CC(C)=CCN1CC[C@]2(C)c3cc(O)ccc3C[C@H]1[C@H]2C.O=C(O)CCC(=O)O,train
CC(=O)OC1(c2ccccc2)CCN(CCc2ccccc2)CC1,train
O=S(=O)([O-])c1ccc2cc(O)ccc2c1,train
CNC(=S)[S-],test
CC(C)COC(=O)CC(C(=O)OCC(C)C)S(=O)(=O)[O-],train
CC1(C)C(=O)N(Br)C(=O)N1Cl,train
CCCCCCCCCCCCCCCCCC(=O)OCC(O)CO,train
[SnH2+2],train
COCCO,valid
C1=CCC([Ti+2]C2=CC=CC2)=C1,train
COC(=O)Cl,train
CC(=O)Oc1c(C)c(C)c2c(c1C)CC[C@@](C)(CCC[C@H](C)CCC[C@H](C)CCCC(C)C)O2,train
CC(=O)Nc1ccc(N)cc1,train
CC(=O)Nc1cccc(N)c1,test
Nc1cccc(N)c1,train
CCCCC1C(=O)N(c2ccccc2)N(c2ccccc2)C1=O,train
c1ccc(Nc2ccc3ccccc3c2)cc1,train
CC1=NN(c2ccccc2)C(=O)C1,train
N=C(N)NC(=O)c1nc(Cl)c(N)nc1N,valid
Nc1ccc(N)cc1,train
CC1=CC[C@H]2C[C@@H]1C2(C)C,train
CCCCCCCCCCCCOCCOS(=O)(=O)[O-],train
CCCCCCCCCCCCCCCCCCOCCO,train
CN1C(=O)c2ccccc2C1=O,test
CCCCCCCCCCCCCCCC,train
COC(=O)C(C)O,train
C1=CCC=C1,train
CCNc1nc(NC(C)C)nc(SC)n1,train
CC1(C)CNC(=NN=C(/C=C/c2ccc(C(F)(F)F)cc2)/C=C/c2ccc(C(F)(F)F)cc2)NC1,valid
COC(=O)c1ccccc1S(=O)(=O)NC(=O)Nc1nc(C)nc(OC)n1,train
CCOC(=O)[C@H](CCc1ccccc1)N[C@@H](C)C(=O)N1CC2(C[C@H]1C(=O)O)SCCS2,train
CNC(=O)O/N=C/C(C)(C)S(C)(=O)=O,train
O=C1OC[C@H](Cc2cccc(O)c2)[C@H]1Cc1cccc(O)c1,train
CC(C)(C)c1ccc(N)cc1,test
CCCc1ccc(N)cc1,train
COc1cc(NS(C)(=O)=O)ccc1Nc1c2ccccc2nc2ccccc12,train
CCCCc1nc2cccnc2n1Cc1ccc(-c2ccccc2-c2nnn[n-]2)cc1,train
CCCCc1oc2ccccc2c1C(=O)c1cc(I)c(OCCN(CC)CC)c(I)c1,train
CN1CCc2cccc3c2[C@H]1Cc1ccc(O)c(O)c1-3.CN1CCc2cccc3c2[C@H]1Cc1ccc(O)c(O)c1-3,valid
C=C1C[C@H]2[C@@H]3C=C(C)C4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]2(C)[C@@]1(OC(C)=O)C(C)=O,train
CC(C)COC(=O)C=Cc1ccccc1,train
O=C(C=Cc1ccccc1)OCC=Cc1ccccc1,train
C=CC(Cl)CCl,train
Oc1ccccc1Cc1ccccc1O,test
CCC(=O)Nc1ccc(Cl)c(Cl)c1,train
NC12CC3CC(CC(C3)C1)C2,train
CCC(C)(c1ccc(O)cc1)c1ccc(O)cc1,train
CCc1cccc(C)c1N(C(=O)CCl)C(C)COC,train
Fc1ccccc1,valid
CC(O)C1CCCCC1,train
CNC1(C)C2CCC(C2)C1(C)C,train
Cc1cccc(Nc2ccccc2C(=O)O)c1C,train
CNC[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,train
COC(=O)N(C(=O)N1CO[C@@]2(C(=O)OC)Cc3cc(Cl)ccc3C2=N1)c1ccc(OC(F)(F)F)cc1,test
C[N+](C)(C)CCCCCC[N+](C)(C)C,train
O=C(Cn1cnnn1)N[C@@H]1C(=O)N2C(C(=O)[O-])=C(CSc3nncs3)CS[C@H]12,train
COC(=O)C1(O)c2ccccc2-c2ccccc21,train
COc1ccc2c3c1O[C@H]1[C@H](O)CC[C@H]4[C@@H](C2)N(C)CC[C@@]341,train
CS(=O)(=O)c1cc(C(F)(F)F)ccc1C(=O)c1cnoc1C1CC1,valid
Cc1c(C(=O)c2cnn(C)c2O)ccc(S(C)(=O)=O)c1C1=NOCC1,train
CC(C)(C)c1ccc(CCC=O)cc1,train
CCCCC(=O)OCC(=O)[C@H]1[C@H](C)C[C@H]2[C@@H]3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
NCCCC[C@H](NC(=O)[C@@H]1CCCN1C(=O)[C@@H]1CSSC[C@H](N)C(=O)N[C@@H](Cc2ccccc2)C(=O)N[C@@H](Cc2ccccc2)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CC(N)=O)C(=O)N1)C(=O)NCC(N)=O,train
C1CC(COCC2CO2)CCC1COCC1CO1,test
COC(=O)CCCCCCCCC(=O)OC,train
O=[Se]([O-])[O-],train
CCC(=O)O[C@](Cc1ccccc1)(c1ccccc1)[C@H](C)CN(C)C,train
COC(=O)CCCCCCCC(=O)OC,train
CCOC(=S)[S-],valid
CCCCN(CCCC)C(=S)[S-],train
O=C(CCCN1CCC(C(=O)c2ccc(F)cc2)CC1)c1ccc(F)cc1,train
CC(C)(CO)[C@@H](O)C(=O)NCCCC(=O)[O-].CC(C)(CO)[C@@H](O)C(=O)NCCCC(=O)[O-],train
O=C(O)CCC/C=C\C[C@H]1[C@@H](O)C[C@@H](O)[C@@H]1/C=C/[C@@H](O)COc1cccc(C(F)(F)F)c1,train
COc1ccc(-c2cc(=S)ss2)cc1,test
O=C1CCC(=O)N1CO,train
NC(=O)[C@@H]1C[C@]2(NC(=O)NC2=O)c2cc(F)ccc2O1,train
COc1ccc(C(=O)c2c(C)n(CCN3CCOCC3)c3ccccc23)cc1,train
CCCCCCCCCCCCCCCCBr,train
CC1=C(C=O)C(C)(C)CC=C1,valid
CC[C@@]1(O)C[C@H](O[C@H]2C[C@H](N(C)C)[C@H](O[C@H]3C[C@H](O)[C@H](O[C@H]4CCC(=O)[C@H](C)O4)[C@H](C)O3)[C@H](C)O2)c2c(cc3c(c2O)C(=O)c2c(O)cccc2C3=O)[C@H]1C(=O)OC,train
CC(C)(C)C(=O)CC(=O)C(C)(C)C,train
CCCCC/C(=C/c1ccccc1)CO,train
CCCCCCCCCCCCCC[P+](CCCC)(CCCC)CCCC.CCCCCCCCCCCCc1ccccc1S(=O)(=O)[O-],train
O=[N+]([O-])c1ccc(CBr)cc1,test
CC(C)C[C@H](NC(=O)[C@H](Cc1ccccc1)NC(=O)c1cnccn1)B(O)O,train
CCN(CC)C(=O)C(C)Oc1cccc2ccccc12,train
Clc1cccc(C(Cl)(Cl)Cl)n1,train
CC(C)Cc1ccc([C@H](C)C(=O)O)cc1,train
CC(C)COC(=O)COc1ccc(Cl)cc1Cl,valid
CCOC(=O)COc1ccc(Cl)cc1Cl,train
CCOC(=O)C=CC(=O)O,train
CCN1C(=CC=CC=Cc2sc3ccccc3[n+]2CC)Sc2ccccc21,train
Oc1ccc(C2(c3ccc(O)cc3)c3ccccc3-c3ccccc32)cc1,train
N#CCNCC#N,test
Oc1ccc(C2(c3ccc(O)cc3)CCCCC2)cc1,train
Nc1cc2c3c(c1)C(c1ccccc1)=N[C@@H](NC(=O)c1cccnc1)C(=O)N3CC2,train
CSc1ccc(Oc2ccc(S(N)(=O)=O)cc2CN(C)C)cc1C,train
NCC1(Cc2noc(=O)[nH]2)CCCCC1,train
CN[C@H]1CC[C@@H](c2ccc(Cl)c(Cl)c2)c2ccc(S(N)(=O)=O)cc21,valid
CC(C)c1nnc2ccc(-c3ocnc3-c3cc(F)ccc3F)cn12,train
O=C(O)c1cccc(C[C@@H]2COc3ccc(OCc4nc5cc(F)ccc5s4)cc3[C@@H]2O)c1,train
O=C(O)Cn1c(=O)n(Cc2ccc(Br)cc2F)c(=O)c2ccc(Cl)cc21,train
CCOc1cccc([C@]2(N3CCN(c4ccccc4)CC3)CC[C@@H](C)CC2)c1,train
CC(=O)NCCCS(=O)(=O)[O-].CC(=O)NCCCS(=O)(=O)[O-],test
Cc1ccc(Br)cc1,train
OCCCCO,train
C(CCOCC1CO1)COCC1CO1,train
CCCC=NO,train
CCCCCCCCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,valid
Cl[Au](Cl)Cl,train
CC(=O)OC(C)c1ccccc1,train
Cc1ccc(S(=O)(=O)[O-])c(C)c1,train
OCCCc1ccccc1,train
CCCCCCCCOC(=O)c1ccc(O)cc1,test
CN1CC[C@H](c2c(O)cc(O)c3c(=O)cc(-c4ccccc4Cl)oc23)[C@H](O)C1,train
Cn1cnc2ncnc(N)c21,train
NC(=O)n1c(O)c(C(=O)c2cccs2)c2cc(Cl)ccc21,train
NCCNCCNCCNCCN,train
COc1ccc(Br)c(C)c1,valid
Oc1c(Cl)c(Cl)cc(Cl)c1Cl,train
CCCCCC1=CCCC1=O,train
O=C1OC(=O)c2c(Cl)c(Cl)c(Cl)c(Cl)c21,train
C=CC(=O)OCCOCCOCCOCCOC(=O)C=C,train
O=C[O-],test
CCCCCCCCCCCCCCCCCC[N+](C)(C)Cc1ccccc1,train
CN(C)c1ccc(N=O)cc1,train
CC(C)(C)OC(=O)c1cccc(N)c1,train
Cc1ccc2ccccc2c1C,train
Cc1ccc([N+](=O)[O-])cc1C,valid
Cc1cccc([N+](=O)[O-])c1C,train
Cc1ccc([N+](=O)[O-])c(C)c1,train
Cc1cccc(C)c1[N+](=O)[O-],train
Cc1ccc(C)c([N+](=O)[O-])c1,train
Cc1cc(C)cc([N+](=O)[O-])c1,test
CCO[Si](CC)(OCC)OCC,train
CCOc1cc(OP(=S)(OC)OC)nc(CC)n1,train
COP(=S)(OC)Oc1ccc(S(=O)(=O)N(C)C)cc1,train
CCC=O,train
O=C(O)CC(=O)O,valid
COC(=O)[C@@H]1[C@@H](O)CC[C@@H]2CN3CCc4c([nH]c5ccccc45)[C@@H]3C[C@@H]21,train
CC(=O)C(=O)O,train
CC(C)(O)[C@H]1[C@@H]2C(=O)O[C@H]1[C@H]1OC(=O)[C@@]34O[C@@H]3C[C@]2(O)[C@@]14C,train
CCCCCCCCCCCCCCC(=O)O,train
CO[C@]12C[C@@H](COC(=O)c3cncc(Br)c3)CN(C)[C@@H]1Cc1cn(C)c3cccc2c13,test
NCCCC[C@H](N[C@@H](CCc1ccccc1)C(=O)O)C(=O)N1CCC[C@H]1C(=O)O,train
CCCCC(=O)O,train
O=C(O)Cc1ccccc1,train
CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)O)=C(C[N+]3(C)CCCC3)CS[C@H]12)c1csc(N)n1,train
O=C(O)/C=C/c1c[nH]cn1,valid
C[Si](Cn1cncn1)(c1ccc(F)cc1)c1ccc(F)cc1,train
NNC(=O)c1cccnc1,train
O=C(O)c1cccnc1,train
O=[N+]([O-])[O-].[Na+],train
CCOC(=O)NC(=S)Nc1ccccc1NC(=S)NC(=O)OCC,test
O=C(O)CN(CC(=O)O)CC(=O)O,train
N#CCCC(Br)(C#N)CBr,train
CC(CCC(=O)O)(c1ccc(O)cc1)c1ccc(O)cc1,train
CC[C@H]1OC(=O)C[C@@H](O)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)[C@@H](O[C@H]3C[C@@](C)(O)[C@@H](O)[C@H](C)O3)[C@H](N(C)C)[C@H]2O)[C@@H](CC=O)C[C@@H](C)C(=O)C=CC(C)=C[C@@H]1CO[C@@H]1O[C@H](C)[C@@H](O)[C@@H](OC)[C@H]1OC,train
O=C(O)c1cn(-c2ccc(F)cc2)c2cc(N3CCNCC3)c(F)cc2c1=O,valid
O=C(NC(Cc1cc(=O)[nH]c2ccccc12)C(=O)O)c1ccc(Cl)cc1,train
Cc1ncc([N+](=O)[O-])n1CC(C)O,train
CCn1cc(C(=O)O)c(=O)c2cc(F)c(N3CCN(C)CC3)cc21,train
C[C@]12CC[C@H]3[C@@H](C=CC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@@]21CCC(=O)O1,train
CC(C)NCC(O)COc1cccc2[nH]ccc12,test
O=c1[nH]c2ccccc2n1C1CCN(CCCC(c2ccc(F)cc2)c2ccc(F)cc2)CC1,train
O=C(O)c1ccccc1C(=O)Nc1ccc(S(=O)(=O)Nc2nccs2)cc1,train
CNC(=O)Oc1ccc2c(c1)[C@]1(C)CCN(C)[C@@H]1N2C,train
CC(=O)O[C@H]1C[C@@H]2CC[C@@H]3[C@H](CC[C@@]4(C)[C@H]3C[C@H](N3CC[N+](C)(C)CC3)[C@@H]4OC(C)=O)[C@@]2(C)C[C@@H]1N1CC[N+](C)(C)CC1,train
CCn1cc(C(=O)O)c(=O)c2cnc(N3CCNCC3)nc21,valid
C=Cc1cccc(CCl)c1,train
CCOP(=O)(OCC)SCCSCC,train
C=Cc1ccc(C(C)(C)C)cc1,train
CCCCN,train
CC(C)[C@H](C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1)c1ccc(OC(F)F)cc1,test
N#CCC#N,train
CCNCC,train
O=c1c(-c2ccc(O)cc2)coc2cc(O)cc(O)c12,train
CCCCCCCCCC(=O)N(C)C,train
C=CC(=O)[O-].C=CC(=O)[O-].[Zn+2],valid
Nc1nc2nc[nH]c2c(=S)[nH]1,train
CS(=O)(=O)c1ccc([C@@H](O)[C@@H](COC(=O)CN)NC(=O)C(Cl)Cl)cc1,train
CC1(C)C2=CCCC(C)(C)[C@]23CC[C@H]1C3,train
CC(CCO)CCCC(C)(C)O,train
Nc1nc2c(ncn2COC(CO)CO)c(=O)[nH]1,test
CN1CCN(CCC=C2c3ccccc3Sc3ccc(S(=O)(=O)N(C)C)cc32)CC1,train
Cc1cc(N)n(-c2ccccc2)n1,train
C=C(C)C(=O)OCCOCCOCCOCCOC(=O)C(=C)C,train
COc1cc2ncnc(Nc3ccc(F)c(Cl)c3)c2cc1OCCCN1CCOCC1,train
Nc1ccc(S(=O)(=O)Nc2ccccn2)cc1,valid
Cc1cc(NS(=O)(=O)c2ccc(N)cc2)no1,train
COc1ccc2c(c1)c1c3n2CCN(C)C3=NCC1,train
CCN(CC)c1ccc(C(=C2C=CC(=[N+](CC)CC)C=C2)c2ccc(S(=O)(=O)[O-])cc2S(=O)(=O)[O-])cc1,train
CCCCOC(=O)c1ccc(O)cc1,train
CCC(C)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,test
CCCCCl,train
CCCCOC(=O)c1ccccc1C(=O)OCc1ccccc1,train
CCC[C@@H]1O[C@@H]2C[C@H]3[C@@H]4CCC5=CC(=O)C=C[C@]5(C)[C@H]4[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1,train
OCCBr,train
O=C([O-])CCCOc1ccc(Cl)cc1Cl,valid
CCCCC/C=C\C=C\C(=O)OCC,train
CCCCCC[n+]1ccccc1.F[B-](F)(F)F,train
Clc1nnc(Cl)c2ccccc12,train
Cc1cc(O)c(Cl)c(C)c1Cl,train
CC(C)CCCO,test
CC/C=C/CCCCCCCCCCOC(C)=O,train
C/C=C/C/C=C\CCCCCCCCOC(C)=O,train
C/C=C/C=C/CCCCCCCO,train
C=CC(=O)OCCCCOC(=O)C=C,train
O=[N+]([O-])/C=C/c1ccccc1,valid
COC(=O)c1c(C(F)F)nc(C(F)(F)F)c(C2=NCCS2)c1CC(C)C,train
CCCCn1cc[n+](C)c1,train
CCCCCCI,train
Cc1cccc2c1cc1ccc3cccc4ccc2c1c34,train
CCCCCCCCCCCCCCCBr,test
CCOP(=S)(Oc1cnc(C(C)(C)C)nc1)OC(C)C,train
CCOC(=O)CS,train
COP(=S)(OC)Oc1ccc(Sc2ccc(OP(=S)(OC)OC)cc2)cc1,train
CCCCCC[n+]1ccccc1.O=S(=O)([O-])C(F)(F)F,train
CCCOC(=O)C(C)O,valid
CC(O)CN(CCN(CC(C)O)CC(C)O)CC(C)O,train
CCCCCCC(C=O)=Cc1ccccc1,train
c1ccc(-c2ccc(-c3ccccc3)cc2)cc1,train
OC(CCCN1CCCCC1)(c1ccccc1)c1ccccc1,train
CC(C)(C#N)/N=N/C(C)(C)C#N,test
CCCCC(CC)COC(=O)c1c(Br)c(Br)c(Br)c(Br)c1C(=O)OCC(CC)CCCC,train
CCC(C)(C)c1cc(-n2nc3ccccc3n2)c(O)c(C(C)(C)CC)c1,train
CC/C=C\C#N,train
CC(C)S,train
CC(C)OCC1CO1,valid
CC(C)OC(=O)Cc1ccccc1,train
COP(=S)(OC)Oc1cc(Cl)c(Cl)cc1Cl,train
CC(C)Nc1ccc(Nc2ccccc2)cc1,train
Fc1ccc(Oc2ccnc3cc(Cl)cc(Cl)c23)cc1,train
COP(OC)OC,test
CCC(C)(C)c1ccc(O)c(C(C)(C)CC)c1,train
Cc1ccc([N+](=O)[O-])cc1S(=O)(=O)O,train
CC(C)(C)CC(C)(C)c1ccc(OCCO)cc1,train
OCCO[C@H]1[C@@H](O)O[C@H](CO)[C@@H](O)[C@@H]1O,train
CCCCCCCCC1CO1,valid
CNC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)[C@@H](CC(=O)NO)CC(C)C,train
C[Se]CC[C@H](N)C(=O)O,train
O=C(O[C@@H]1C[C@@H]2C[C@@H]3C[C@H](C1)N2CC3=O)c1c[nH]c2ccccc12,train
CCCCCCCCOc1ccccc1C(=O)Nc1ccc(C(=O)OCC[N+](C)(CC)CC)cc1,train
O[C@@H]1[C@H](O)CN2CCC[C@@H](O)[C@H]12,test
C=CC[C@@H]1C=C(C)C[C@H](C)C[C@H](OC)[C@H]2O[C@@](O)(C(=O)C(=O)N3CCCC[C@H]3C(=O)O[C@H](/C(C)=C/[C@@H]3CC[C@@H](O)[C@H](OC)C3)[C@H](C)[C@@H](O)CC1=O)[C@H](C)C[C@@H]2OC,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](COP(=O)(O)O)[C@@H](O)[C@H]1O,train
CCOC(=O)N(C)C(=O)CSP(=S)(OCC)OCC,train
CC/C=C\CC/C=C/CO,train
C=C(C)CCC[C@H](C)CCO,valid
CCCCCCCCC1CCC(=O)O1,train
C=CC(C)(CCC=C(C)C)OC(=O)c1ccccc1,train
CCCCCCCCCCCCCCC1CO1,train
CC1CCC(C(C)(C)S)C(=O)C1,train
CCCC(C)C(=O)OCC,test
CCCc1ccc(O)c(OC)c1,train
CN(C)C(=S)SC(=S)N(C)C,train
CN(C)C(=S)SSC(=S)N(C)C,train
OC[P+](CO)(CO)CO.OC[P+](CO)(CO)CO,train
OC[P+](CO)(CO)CO,valid
c1ccc2[nH]c(-c3cscn3)nc2c1,train
Cn1c(=O)c2[nH]cnc2n(C)c1=O,train
O=[N+]([O-])C([N+](=O)[O-])([N+](=O)[O-])[N+](=O)[O-],train
S=P(N1CC1)(N1CC1)N1CC1,train
CS(=O)(=O)c1ccc([C@@H](O)[C@@H](CO)NC(=O)C(Cl)Cl)cc1,test
Cc1ccc(S(N)(=O)=O)cc1,train
CO/N=C1/C[C@]2(C[C@@H]3C[C@@H](CC=C(C)C[C@@H](C)C=CC=C4CO[C@@H]5[C@H](O)C(C)=C[C@@H](C(=O)O3)[C@]45O)O2)O[C@H](/C(C)=C/C(C)C)[C@H]1C,train
CON(C)C(=O)Nc1ccc(Cl)cc1,train
O=C(O)c1cc(C(=O)O)cc(C(=O)O)c1,train
COP(=O)(OC)Oc1ccc([N+](=O)[O-])cc1,valid
CNC(=O)N(C)c1nc2ccccc2s1,train
O=C(O)c1cccc(C(=O)O)c1,train
O=C(O)c1ccccc1C(=O)O,train
O=C(O)c1ccc(C(=O)O)c(C(=O)O)c1,train
O=C(O)c1cccc(C(=O)O)c1C(=O)O,test
CC(C)OC(=O)c1cc(C(=O)OC(C)C)cc([N+](=O)[O-])c1,train
CC1CCCO1,train
O=C(O)c1ccc(S(=O)(=O)O)cc1C(=O)O,train
Nn1cnnc1,train
O=C1O[C@H]([C@H](O)CO)C(O)=C1O,valid
C[N+]1(C)[C@H]2CC(OC(=O)C(O)(c3cccs3)c3cccs3)C[C@@H]1[C@H]1O[C@@H]21,train
COc1ccnc(CS(=O)c2nc3cc(OC(F)F)ccc3[n-]2)c1OC,train
CCCCc1ncc(/C=C(\Cc2cccs2)C(=O)O)n1Cc1ccc(C(=O)O)cc1,train
CC(C)[C@H](N)C(=O)OCCOCn1cnc2c(=O)[nH]c(N)nc21,train
CNC(=O)Oc1cc(C)cc(C(C)C)c1,test
Cc1ccsc1C(=CCCN1CCC[C@@H](C(=O)O)C1)c1sccc1C,train
COc1cc(C)nc(-n2nc(C)cc2OC)n1,train
CCN(CC)CC#CCOC(=O)C(O)(c1ccccc1)C1CCCCC1,train
OC(CCN1CCCC1)(c1ccccc1)C1CCCCC1,train
CC(=O)Oc1ccc(C(c2ccc(OC(C)=O)cc2)c2ccccn2)cc1,valid
Cc1cc2c(OCC(CNC(C)(C)C)OC(=O)c3ccccc3)cccc2[nH]1,train
CC[N+](C)(C)Cc1ccccc1Br,train
CSC(=O)c1cccc2nnsc12,train
CCCCC1CCC(O)CC1,train
CCC(=O)c1ccc(Cl)cc1,test
Cc1cc(C)c(C(=O)O)c(C)c1,train
CCCCOc1ccc(N)cc1,train
CC(C)C(=O)c1ccccc1,train
O=C(c1ccccc1)C1CCC1,train
CC(C)(C)c1ccc(S)cc1,valid
CCCCCCOc1ccc(O)cc1,train
CCCC1CCC(=O)CC1,train
CCCC(CC)CCCCCCCN,train
CC(=O)OC[C@H]1O[C@@H](O)[C@H](OC(=O)c2ccccc2C(=O)O)[C@@H](O)[C@@H]1O[C@@H]1O[C@H](COC(C)=O)[C@@H](O)[C@H](OC(=O)c2ccccc2C(=O)O)[C@H]1O,train
C=C(C)C(=O)OCC,test
CCOS(C)(=O)=O,train
CC=C1CC2C=CC1C2,train
COP(=O)(O)OP(=O)(O)OC,train
CCCCC(CC)COCCCN,train
CCCCC(CC)COC(=O)C=Cc1ccc(OC)cc1,valid
CCCCC(CC)COCCC#N,train
CCCCC(CC)COP(=O)(Oc1ccccc1)Oc1ccccc1,train
Oc1ccc(CCNCCCCCCNCCc2ccccc2)cc1O,train
C#C[C@]1(OC(=O)CCCCCC)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@@]21C,train
CCCCc1nc(Cl)c(CO)n1Cc1ccc(-c2ccccc2-c2nnn[nH]2)cc1,test
O=C1Nc2ccc(Cl)cc2C(c2ccccc2Cl)=NC1O,train
CCOC(=O)N1CCC(=C2c3ccc(Cl)cc3CCc3cccnc32)CC1,train
COc1cc(OC)nc(Sc2cccc(Cl)c2C(=O)[O-])n1,train
CCCSP(=S)(OCC)Oc1ccc(SC)cc1,train
Cc1cnc(C2=NC(=O)C(C)(C(C)C)N2)c(C(=O)O)c1,valid
Cc1cc(Cl)ccc1OCC(=O)[O-],train
O=C(CS)Nc1ccccc1,train
COc1ccc(CCN(C)CCCC(C#N)(c2ccc(OC)c(OC)c2)C(C)C)cc1OC,train
C[Si](C)(C)O[Si](C)(C)C,train
C=C(C)CC(C)(C)C,test
CC(C)=CC(C)(C)C,train
CC(=O)OCC1=C(C(=O)O)N2C(=O)[C@@H](NC(=O)Cc3ccccc3)[C@H]2SC1,train
COc1cc(C(=S)N2CCOCC2)ccc1O,train
Cn1c(=N)[nH]c2[nH]cnc2c1=O,train
O=C(O)c1ccc(NC2OC[C@@H](O)[C@H](O)[C@H]2O)cc1,valid
O=C(O)COc1ccc(CCNS(=O)(=O)c2ccccc2)s1,train
COc1cc2[nH]c(=O)[nH]c(=O)c2cc1OC,train
C[C@H]1[C@@H](c2ccccc2)N=C(O)N1C,train
CCOC(=O)CC(=O)c1cc(F)c(Cl)nc1Cl,train
CCC(C=O)CC,test
CC(=O)NC(=O)c1ccccc1O,train
CCCCN1CCCC1C(=O)Nc1c(C)cc(C)cc1C,train
BrCC(Br)C1CCC(Br)C(Br)C1,train
CNc1cnn(-c2cccc(C(F)(F)F)c2)c(=O)c1Cl,train
Brc1cc(Oc2cc(Br)c(Br)c(Br)c2Br)c(Br)c(Br)c1Br,valid
OCC(Br)=C(Br)CO,train
BrC/C=C/CBr,train
N#CC(Br)Br,train
CC(C)Oc1cc(-n2nc(C(C)(C)C)oc2=O)c(Cl)cc1Cl,train
CCCN(CCC)c1c([N+](=O)[O-])cc(S(N)(=O)=O)cc1[N+](=O)[O-],test
O=C1C(I)=CC(=C(c2cc(I)c(O)c(I)c2)c2ccccc2C(=O)O)C=C1I,train
CCCCCCCCCCCCCCCCCC[N+](C)(C)CCC[Si](OC)(OC)OC,train
C=C(C#N)CCC#N,train
BrCc1cccc(Oc2ccccc2)c1,train
CC1(C)Cc2cccc(O)c2O1,valid
SC1CCCCC1,train
CCCCOCCOCCOCCOCCO,train
O=S(=O)([O-])CCO,train
Cc1ccc(OC(=O)c2ccccc2O)cc1,train
C=CCOCC(O)CS(=O)(=O)[O-],test
N[C@@H](Cc1cc(I)c(Oc2ccc(O)c(I)c2)c(I)c1)C(=O)[O-],train
COc1ccc(C)cc1N,train
CC[Hg]Sc1ccccc1C(=O)[O-],train
C=C(C)C(=O)O,train
CC(C/N=C/c1ccccc1O)/N=C/c1ccccc1O,valid
CN1CCC(CN2c3ccccc3Sc3ccccc32)C1,train
CC1(C)[C@H](C=C(Cl)Cl)[C@@H]1C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,train
O=C(O)c1cc(Br)c(O)c(Br)c1,train
CCCCC(CC)COC(=O)c1ccccc1,train
COC(=O)c1c(C)cc(O)c(C)c1O,test
CCCCCC1CCCC(=O)O1,train
N#Cc1ccc(C(=O)O)cc1,train
NCCc1cc[nH]n1,train
O=C(O)c1cn(C2CC2)c2cc(N3CCNCC3)c(F)cc2c1=O,train
CN1CCC[C@@H]1CCO[C@](C)(c1ccccc1)c1ccc(Cl)cc1,valid
Cc1c(-c2ccccc2)oc2c(C(=O)OCCN3CCCCC3)cccc2c1=O,train
COc1cc(N)c(Cl)cc1C(=O)NC1CCN(Cc2ccccc2)CC1,train
CCOC(=O)Nc1ccc2c(c1)N(C(=O)CCN1CCOCC1)c1ccccc1S2,train
N[C@@H]1C[C@H]1c1ccccc1,train
CNC(C)(C)Cc1ccccc1.CNC(C)(C)Cc1ccccc1,test
CC(=O)c1ccc2c(c1)N(CCCN(C)C)c1ccccc1S2,train
c1ccc(CN(CC2=NCCN2)c2ccccc2)cc1,train
NS(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
CCC(C)c1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,train
C=CC(C)CCCC(C)(C)O,valid
Cc1cccc(C#N)c1,train
CC(C)(C)CC(C)(C)c1ccc(Nc2ccc(C(C)(C)CC(C)(C)C)cc2)cc1,train
Cc1ccccc1N,train
NCc1cccc(CN)c1,train
COC(=O)CCC(=O)OC,test
COP(=O)(O)OC,train
CNC(=O)NC,train
Cl[Dy](Cl)Cl,train
CC1CCc2cc(F)cc3c(=O)c(C(=O)O)cn1c23,train
O=C(O)CCNC(=O)c1ccccc1,valid
OCC(O)CN1CCN(c2ccccc2)CC1,train
CCCOc1ccc2[nH]c(NC(=O)OC)nc2c1,train
Cc1cc(C)c(C=C2C(=O)Nc3ccccc32)[nH]1,train
CCC[C@@H]1C[C@@H](C(=O)N[C@H]([C@H](C)Cl)[C@H]2O[C@H](SC)[C@H](O)[C@@H](O)[C@H]2O)N(C)C1,train
O=c1cc(CO)occ1O,test
Nc1nc(N)nc(-c2cc(Cl)ccc2Cl)n1,train
NC(=S)Nc1cccc2ccccc12,train
c1ccc2cc(Nc3ccc(Nc4ccc5ccccc5c4)cc3)ccc2c1,train
c1ccc2ccccc2c1,train
CCn1cc(C(=O)O)c(=O)c2ccc(C)nc21,valid
CC(C)(Oc1ccc(C2CCCc3ccccc32)cc1)C(=O)O,train
CS(=O)(=O)OCCCCOS(C)(=O)=O,train
NCCNc1cccc2ccccc12,train
Nc1cccc2c(N)cccc12,train
O=C(O)Cc1cccc2ccccc12,test
NC(=O)Cc1cccc2ccccc12,train
CCOC(=O)C1=C[C@@H](OC(CC)CC)[C@H](NC(C)=O)[C@@H](N)C1,train
CCCSSCCC,train
C=Cc1c(C)c2cc3nc(c(CC(=O)[O-])c4[n-]c(cc5nc(cc1[n-]2)c(C)c5CC)c(C)c4C(=O)[O-])C(CCC(=O)[O-])C3C.[Cu+2],train
CCC1(c2cccc(O)c2)CCCCN(C)C1,valid
CC(C)C[C@H](CN)CC(=O)O,train
CCC(=O)NCCC1CCc2ccc3c(c21)CCO3,train
NC[C@H]1O[C@H](O[C@@H]2[C@@H](N)C[C@@H](N)C(O)[C@H]2O[C@@H]2O[C@H](CO)[C@@H](O)[C@H]2O)[C@H](N)[C@@H](O)[C@@H]1O,train
CCc1c2c(nc3ccc(OC(=O)N4CCC(N5CCCCC5)CC4)cc13)-c1cc3c(c(=O)n1C2)COC(=O)[C@]3(O)CC,train
O=C(CCCCCCCCCCCCC1CCCC1)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)CCCCCCCCCCCCC3CCCC3)cc2)cc1,test
Nc1cc(C(Cl)=C(Cl)Cl)c(S(N)(=O)=O)cc1S(N)(=O)=O,train
CC(=O)Oc1ccc(C2(c3ccc(OC(C)=O)cc3)C(=O)Nc3ccccc32)cc1,train
CC(=O)O[C@H]1C(=O)[C@]2(C)[C@@H](O)C[C@H]3OC[C@@]3(OC(C)=O)[C@H]2[C@H](OC(=O)c2ccccc2)[C@]2(O)C[C@H](OC(=O)[C@H](O)[C@@H](NC(=O)c3ccccc3)c3ccccc3)C(C)=C1C2(C)C,train
CC(=O)O[C@H]1C[C@@H]2CC[C@@H]3[C@H](CC[C@@]4(C)[C@H]3C[C@H]([N+]3(C)CCCCC3)[C@@H]4OC(C)=O)[C@@]2(C)C[C@@H]1[N+]1(C)CCCCC1,train
COc1ccnc(CS(=O)c2nc3cc(OC(F)F)ccc3[nH]2)c1OC,valid
CC1(C)NC(=O)NC1=O,train
CC1OC(C)OC(C)O1,train
CCN(CC)CCCC(C)Nc1ccnc2cc(Cl)ccc12,train
NCCCN(CCO)CCO,train
Nc1ccccn1,test
CCOc1ccc(OCC)c(Cl)c1,train
O=C([O-])C1=NN(c2ccc(S(=O)(=O)O)cc2)C(=O)C1,train
N#CC(Cl)(Cl)Cl,train
NC(=O)C(Cl)(Cl)Cl,train
Nc1nc(-c2ccccc2)cs1,valid
CC(=O)C(Cl)(Cl)Cl,train
C(=C(c1ccccc1)c1ccccc1)c1ccccc1,train
CCOCCOC(C)=O,train
CCCCCCBr,train
Oc1ccc(C(c2ccc(O)cc2)C(Cl)(Cl)Cl)cc1,test
C[C@@H]1O[C@@H](OC[C@H]2O[C@@H](Oc3c(-c4ccc(O)c(O)c4)oc4cc(O)cc(O)c4c3=O)[C@H](O)[C@@H](O)[C@@H]2O)[C@H](O)[C@H](O)[C@H]1O,train
CCNCCO,train
C1CCCCC1,train
Nc1cc(S(=O)(=O)O)ccc1O,train
c1ccncc1,valid
C1OCOCO1,train
O=S(=O)([O-])c1ccc(Nc2nc(Nc3ccc(/C=C/c4ccc(Nc5nc(Nc6ccc(S(=O)(=O)[O-])cc6)nc(N(CCO)CCO)n5)cc4S(=O)(=O)[O-])c(S(=O)(=O)[O-])c3)nc(N(CCO)CCO)n2)cc1,train
FB(F)F,train
Clc1ccccc1-c1ccccc1Cl,train
CCCCOCCCC,test
O=C1c2ccccc2C(=O)c2cc(S(=O)(=O)[O-])ccc21,train
CC(=O)Oc1ccc(C(C)=O)cc1,train
CC(C)c1ccc2c(C(C)C)cc(C(C)C)c(S(=O)(=O)[O-])c2c1,train
CC(=O)c1cc(C(C)(C)C)cc2c1CCC2(C)C,train
CCN(CCC#N)c1ccc(N=Nc2c(Cl)cc([N+](=O)[O-])cc2Cl)cc1,valid
CCCCn1cc[n+](C)c1C,train
CCCCCCCC[N+](C)(CCCCCCCC)CCCCCCCC.O=S(=O)([O-])C(F)(F)F,train
CN(C)N=O,train
CCN(CC)N=O,train
CCCCCCCCCCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,test
CCC(Cl)CCC(Cl)C(Cl)CC(Cl)C(Cl)C(Cl)CCl,train
O=C(O)C1C(C(=O)O)C2(Cl)C(Cl)=C(Cl)C1(Cl)C2(Cl)Cl,train
OC(O)C(Cl)(Cl)Cl,train
C[C@H](CCC(=O)O)[C@H]1CC[C@H]2[C@H]3[C@H](CC[C@@]21C)[C@@]1(C)CC[C@@H](O)C[C@H]1C[C@H]3O,train
O=C(O)CCCc1ccc(N(CCCl)CCCl)cc1,valid
Nc1cc(Cl)cc(C(=O)O)c1Cl,train
O=C(N[C@H](CO)[C@H](O)c1ccc([N+](=O)[O-])cc1)C(Cl)Cl,train
ClC1=C(Cl)[C@@]2(Cl)[C@H]3[C@H](CC(Cl)[C@@H]3Cl)C1(Cl)C2(Cl)Cl,train
O=C1C(Cl)=C(Cl)C(=O)C(Cl)=C1Cl,train
CCCCCCCCCCCCn1cc[n+](C)c1.F[B-](F)(F)F,test
CCCCCCn1cc[n+](C)c1.F[B-](F)(F)F,train
CCCn1cc[n+](C)c1.F[B-](F)(F)F,train
CCCn1cc[n+](C)c1C,train
c1ccc2cc3c4ccccc4c4ccccc4c3cc2c1,train
CCCC[P+](CC)(CCCC)CCCC.CCOP(=O)([O-])OCC,valid
CCC(C)(CCCC(C)C)OC(C)=O,train
CC/C=C\CCOC(C)=O,train
CC/C=C1/OC(=O)c2ccccc21,train
CN1CCN(c2cc3c(cc2F)c(=O)c(C(=O)O)cn3-c2ccc(F)cc2)CC1,train
N=C(N)NCCC[C@H](NC(=O)[C@@H]1CCCN1C(=O)[C@@H]1CSSC[C@H](N)C(=O)N[C@@H](Cc2ccc(O)cc2)C(=O)N[C@@H](Cc2ccccc2)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CC(N)=O)C(=O)N1)C(=O)NCC(N)=O,test
CCN1C(=CC=Cc2sc3ccccc3[n+]2CC)Sc2ccccc21,train
CC(C)=CCC/C(C)=C/C=C\C(C)=C\C=C\C(C)=C\C=C/C=C(C)/C=C/C=C(C)/C=C\C=C(/C)CCC=C(C)C,train
O=C1c2ccccc2C(=O)N1CO,train
O=c1[nH]c2ccccc2c(=O)o1,train
O=C(O)c1ccc2cc(O)ccc2c1,valid
C[C@@H]1CC[C@H]2[C@@H](C)[C@H](OC(=O)CCC(=O)O)O[C@@H]3O[C@]4(C)CC[C@@H]1C23OO4,train
CCn1cc(C(=O)O)c(=O)c2cnc(N3CCCC3)nc21,train
Cc1ccc(C#N)cc1,train
Oc1ccc(Cc2ccccc2)cc1,train
CCC(O)C(C)(C)C,test
Cc1cc(O)c2c3c4c(cc(O)c13)C(C)(C)C(=O)c1c(O)cc(O)c(c1-4)C2=O,train
CC1CN(c2ccccc2)NC1=O,train
CCN1CCCC1=O,train
O=S(=O)([O-])c1cc(O)ccc1O,train
ClC(Cl)=C(Cl)Cl,valid
ClC(Cl)C(Cl)Cl,train
Clc1cc2c(cc1Cl)Oc1cc(Cl)c(Cl)cc1O2,train
Nc1cc(Cl)c(-c2cc(Cl)c(N)cc2Cl)cc1Cl,train
ClCC(Cl)(Cl)Cl,train
O=S(=O)(c1ccc(Cl)cc1)c1cc(Cl)c(Cl)cc1Cl,test
O=C=NCCCCCCN=C=O,train
Nc1ccc(-c2ccc(N)c(N)c2)cc1N,train
OCC1CCCO1,train
O=C(OC(=O)c1ccccc1)c1ccccc1,train
Oc1ccc(-c2ccc(O)cc2)cc1,valid
CCOC(=O)C(C)O,train
Cc1cc(C)c(C)cc1C,train
CC(C)[C@@H](Nc1ccc(C(F)(F)F)cc1Cl)C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1,train
CN(C)C(=O)Nc1ccccc1,train
Nc1nc(N)nc(Cl)n1,test
O=[N+]([O-])c1ccc(Oc2ccc(C(F)(F)F)cc2[N+](=O)[O-])cc1,train
Nc1ccc(O)cc1,train
Nc1ccccc1O,train
CC(C)(c1cc(Br)c(OCCO)c(Br)c1)c1cc(Br)c(OCCO)c(Br)c1,train
Nc1nc2ccc([N+](=O)[O-])cc2s1,valid
CC(C)(C)C1=CC(=O)C=C(C(C)(C)C)C1=O,train
COC(=O)c1cc(Cl)ccc1[N+](=O)[O-],train
CCC1(CO)COCOC1,train
Clc1ccc(Nc2nnc(Cc3ccncc3)c3ccccc23)cc1,train
COc1ccc(C[C@@H](C)NC[C@H](O)c2ccc(O)c(NC=O)c2)cc1.COc1ccc(C[C@@H](C)NC[C@H](O)c2ccc(O)c(NC=O)c2)cc1,test
C=CC(=O)OCC1CC2CC1C1CCC(COC(=O)C=C)C21,train
CC[C@@H]([C@H](C)O)n1ncn(-c2ccc(N3CCN(c4ccc(OCC5CO[C@@](Cn6cncn6)(c6ccc(F)cc6F)C5)cc4)CC3)cc2)c1=O,train
O[C@@H](CNC[C@H](O)[C@H]1CCc2cc(F)ccc2O1)[C@@H]1CCc2cc(F)ccc2O1,train
CNS(=O)(=O)CCc1ccc2[nH]cc(C3CCN(C)CC3)c2c1,train
Cc1cc(CCC(=O)OCCOCCOCCOC(=O)CCc2cc(C)c(O)c(C(C)(C)C)c2)cc(C(C)(C)C)c1O,valid
OCCSCSCCO,train
C=CC(=O)N1CCOCC1,train
C[C@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)CO,train
CC(C)COCC(CN(Cc1ccccc1)c1ccccc1)N1CCCC1,train
Cl[Zr](Cl)(C1=CC=CC1)C1C=CC=C1,test
CC(=O)N1CCN(c2ccc(OC[C@H]3CO[C@](Cn4ccnc4)(c4ccc(Cl)cc4Cl)O3)cc2)CC1,train
Clc1ccccc1C(c1ccccc1)(c1ccccc1)n1ccnc1,train
[O-][n+]1ccccc1S[Zn]Sc1cccc[n+]1[O-],train
O=S1(=O)OC(c2ccc(O)cc2)(c2ccc(O)cc2)c2ccccc21,train
O=S(=O)(c1ccc(O)cc1)c1ccc(O)cc1,valid
C=C(C)C(=O)OCC1CO1,train
COc1ccc(C(=O)c2ccccc2O)c(O)c1,train
O=C(c1ccccc1O)c1ccccc1O,train
COc1ccc(C(=O)c2ccccc2)c(O)c1,train
O=C(c1ccccc1)c1ccc(O)cc1O,test
Nc1nnc(N)[nH]1,train
O=C(Nc1ccccc1)Nc1cnns1,train
CC1(C)CCC(=Cc2ccc(Cl)cc2)C1(O)Cn1cncn1,train
C=C(C)CN(CC)c1c([N+](=O)[O-])cc(C(F)(F)F)cc1[N+](=O)[O-],train
CC(=O)[C@H]1CC[C@H]2[C@@H]3CC=C4C[C@@H](O)CC[C@]4(C)[C@H]3CC[C@]12C,valid
C[N+]1(CCCCC[N+]2(C)CCCC2)CCCC1,train
Cc1cccc(C)c1NC(=O)C(C)N,train
CCCc1ncc(C[n+]2ccccc2C)c(N)n1,train
O=C1OC2(c3cc(Br)c([O-])cc3Oc3c2cc(Br)c([O-])c3[Hg]O)c2ccccc21,train
C[C@@H](Cc1ccccc1)NCCC(c1ccccc1)c1ccccc1.C[C@H](O)C(=O)O,test
COC1=CC(=O)OC(CCc2ccccc2)C1,train
COc1cc(OC)nc(NC(=O)NS(=O)(=O)c2ncccc2C(=O)N(C)C)n1,train
C[C@H](CCC(=O)[O-])[C@H]1CC[C@H]2[C@@H]3CC[C@@H]4C[C@H](O)CC[C@]4(C)[C@H]3C[C@H](O)[C@@]21C,train
CCCCCCCCC(=O)NCc1ccc(O)c(OC)c1,train
O=P1(N(CCCl)CCCl)NC(OO)CCO1,valid
CC(=O)/C(C)=C/C1C(C)=CCCC1(C)C,train
CC(=O)[O-].[Na+],train
CC(=O)[O-].[K+],train
CC(C)CC(C)(O)C#CC(C)(O)CC(C)C,train
C=C1CCC2CC1C2(C)C,test
O=[N+]([O-])c1cccc(S(=O)(=O)[O-])c1,train
C=CCN1CC[C@]23CCCC[C@H]2[C@H]1Cc1ccc(O)cc13,train
CN(C)CCOC1=Cc2ccccc2Sc2ccc(Cl)cc21,train
O=C1Cc2cc(CCN3CCN(c4nsc5ccccc45)CC3)c(Cl)cc2N1,train
CC(c1cc2ccccc2s1)N(O)C(N)=O,valid
N#Cc1ccc(C(c2ccc(C#N)cc2)n2cncn2)cc1,train
CCCCCCCCCCCC(=O)O[Sn](CCCC)(CCCC)OC(=O)CCCCCCCCCCC,train
O=C(Cl)C(Cl)Cl,train
Nc1ccc(Cl)cc1Cl,train
CC12CC1(C)C(=O)N(c1cc(Cl)cc(Cl)c1)C2=O,test
CCCCCCCCCCCC[N+](C)(C)CCCCCCCCCCCC,train
CC(C(=O)O)c1ccc(-c2ccccc2)c(F)c1,train
CC(=O)OC/C=C(\C)CC/C=C(\C)CCC=C(C)C,train
N[C@H]1C(O)O[C@H](CO)[C@@H](O)[C@@H]1O,train
COc1ccc(Cl)cc1C(=O)NCCc1ccc(S(=O)(=O)NC(=O)NC2CCCCC2)cc1,valid
C=C[C@](C)(O)CC[C@@H]1[C@@]2(C)CCCC(C)(C)[C@@H]2CC[C@@]1(C)O,train
C=C(C)[C@@H]1CC[C@@H](C)C[C@@H]1O,train
O=[PH](O)c1ccccc1,train
CN(C)CCC#N,train
CO[Si](CCCNCCN)(OC)OC,test
CC(C)=CCCC(C)=CC(=O)O,train
CCCCO[PH](=O)OCCCC,train
Cc1c([N+](=O)[O-])cc2c(c1[N+](=O)[O-])C(C)(C)CC2(C)C,train
CCCCCCCCCCCCn1cc[n+](C)c1,train
c1csc(C2(N3CCCCC3)CCCCC2)c1,valid
CCCCCCCCCCCCCCCC(=O)OCC(COC(=O)CCCCCCCCCCCCCCC)OC(=O)CCCCCCCCCCCCCCC,train
Cc1cccc(C)c1NC(=O)NC1=CCCN1C,train
[O-][n+]1cc[n+]([O-])c2ccccc21,train
N=C(N)NCC1COc2ccccc2O1,train
CCCCCCCCCCn1cc[n+](CCCCCCCCCC)c1C,test
CCOC(=O)Cc1ccc(-c2ccccc2)cc1,train
O=C(O)CNC(=O)c1ccccc1I,train
CC(C)(C)C(=O)NCCCC(=O)O,train
CCNC(=O)NCc1cccc(Cl)c1,train
Oc1ccc2c(c1)Cc1ccccc1-2,valid
Cc1nnc(-c2ccccc2)c(=O)n1N,train
CCC(C)C(=O)OCCc1ccccc1,train
FC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)CCI,train
C/C=C(\C)C(=O)OCC/C=C\CC,train
CC/C=C\CCOC(=O)CCCCC,test
Cc1cc(C(C)(C)C)c(O)c(C)c1Cn1c(=O)n(Cc2c(C)cc(C(C)(C)C)c(O)c2C)c(=O)n(Cc2c(C)cc(C(C)(C)C)c(O)c2C)c1=O,train
Cc1ccccc1C(N)=O,train
CC(=O)O[C@@H]1CC2CCC1(C)C2(C)C,train
COC(=O)c1ccccc1N,train
CNc1ccc(O)cc1.CNc1ccc(O)cc1,valid
CC1=CC[C@@H]2C[C@H]1C2(C)C,train
COc1c2ccoc2cc2oc(=O)ccc12,train
CC(C)(c1ccc(Oc2ccc3c(c2)C(=O)OC3=O)cc1)c1ccc(Oc2ccc3c(c2)C(=O)OC3=O)cc1,train
CC1(C)S[C@@H]2[C@H](NC(=O)Cc3ccccc3)C(=O)N2[C@H]1C(=O)O.CC1(C)S[C@@H]2[C@H](NC(=O)Cc3ccccc3)C(=O)N2[C@H]1C(=O)O.c1ccc(CNCCNCc2ccccc2)cc1,train
CCC[C@@H]1C[C@@H](C(=O)N[C@H]([C@@H](C)O)[C@H]2O[C@H](SC)[C@H](O)[C@@H](O)[C@H]2O)N(C)C1,test
CN[C@]1(c2ccccc2Cl)CCCCC1=O,train
CC(CN(C)C)C(C)(O)Cc1ccc(Cl)cc1,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=C/C(=N/O)CC[C@@H]4[C@H]3CC[C@@]21CC,train
CC1(C)NC(=O)N(c2ccc([N+](=O)[O-])c(C(F)(F)F)c2)C1=O,train
NC(=O)C(c1ccccc1)(c1ccccc1)[C@@H]1CCN(CCc2ccc3c(c2)CCO3)C1,valid
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3C(=C)C[C@@]21CC,train
O=C(O[C@H]1CN2CCC1CC2)N1CCc2ccccc2[C@@H]1c1ccccc1,train
CC(C)[C@H](N)C(=O)OCC(CO)OCn1cnc2c(=O)nc(N)[nH]c21,train
CCN(CC)C(=O)[C@]1(c2ccccc2)C[C@@H]1CN,train
CN1CC(=O)N2[C@H](Cc3c([nH]c4ccccc34)[C@H]2c2ccc3c(c2)OCO3)C1=O,test
N#CC(c1ccc(Cl)cc1)c1c(Cl)cc(-n2ncc(=O)[nH]c2=O)cc1Cl,train
CCCCCCCCCCCCN(CCO)CCO,train
CC(C)=CCC[C@](C)(O)[C@@H]1CC=C(C)CC1,train
OC[C@H](O)[C@H](O)[C@@H](O)[C@H](O)CO,train
O=C(O)C=Cc1ccc(C=CC(=O)O)cc1,valid
CN1C(C(=O)Nc2ccccn2)=C(O)c2sccc2S1(=O)=O,train
CC(C)[C@]12CC(=O)[C@H](C)[C@H]1C2,train
CC(CO)Oc1ccccc1,train
COc1ccc(CN(CCN(C)C)c2ncccn2)cc1,train
CC(CCO)CC(C)(C)C,test
CCCCCCCC[Sn](Cl)(Cl)CCCCCCCC,train
N=C(Nc1ccccc1)Nc1ccccc1,train
Cc1ccc(C)o1,train
C=CC(C)(O)CC/C=C(\C)CCC=C(C)C,train
CC(=O)c1cc2c(cc1C)C(C)(C)C(C)CC2(C)C,valid
CC[C@@]1(O)C(=O)OCc2c1cc1n(c2=O)Cc2cc3c(CN(C)C)c(O)ccc3nc2-1,train
OCCN1CCOCC1,train
CCSC(=O)N(CC)CC,train
CCCCc1c(C)nc(NCC)nc1O,train
CCCCC(CC)COCCO,test
COCCCOCCCOC,train
CC(=O)CC(c1ccccc1)c1c([O-])c2ccccc2oc1=O,train
CCCc1ccc(OC)cc1,train
CCCCCC[C@@H](O)C/C=C\CCCCCCCC(=O)[O-],train
O=N[O-],valid
O=C(CBr)c1ccc(O)cc1,train
O=[N+]([O-])c1ccc(Oc2ccc(N=C=S)cc2)cc1,train
CCO[C@H](Cc1ccc(OCCc2ccc(OS(C)(=O)=O)cc2)cc1)C(=O)O,train
C1CCNCC1.O=c1[nH]cnc2[nH]cc(CN3CCCCC3)c12,train
CCOc1ccc(/N=C(\C)Nc2ccc(OCC)cc2)cc1,test
c1ccc2c(c1)Sc1ccccc1N2C[C@@H]1CN2CCC1CC2,train
COC(=O)[C@]12CCC(C)(C)C[C@H]1[C@H]1C(=O)C=C3[C@@](C)(CC[C@H]4C(C)(C)C(=O)C(C#N)=C[C@]34C)[C@]1(C)CC2,train
CCCCCCCCCC(=O)N[O-],train
CN(C)CCOc1ccccc1Cc1ccccc1.Nc1ccc(C(=O)O)c(O)c1,train
FC(F)(F)c1ccccc1Cl,valid
ClCCBr,train
O=C(O)c1cccc(Cl)c1,train
O=C(O)c1ccccc1Cl,train
O=C(O)c1ccc(Cl)cc1,train
FC(F)(F)c1cccc(Cl)c1,test
C1COCCO1,train
Cc1cc([N+](=O)[O-])c(O)c([N+](=O)[O-])c1,train
COC(=O)C(C)(C)C,train
Cc1cccc([N+](=O)[O-])c1[N+](=O)[O-],train
CC(Br)C(=O)O,valid
Brc1ccc(Oc2ccccc2)cc1,train
CC(=O)CCCCn1c(=O)c2c(ncn2C)n(C)c1=O,train
O=C(O)CN(CCN(CC(=O)O)CC(=O)O)CCN(CC(=O)O)CC(=O)O,train
CCCC(C)C1(CC)C(=O)N=C(O)NC1=O,train
CCCCCCCCCCC(=O)O,test
O=C(O)c1ccccc1Nc1cccc(C(F)(F)F)c1,train
COc1nc(NC(C)C)nc(NC(C)C)n1,train
CC(C)(Oc1ccc(CCNC(=O)c2ccc(Cl)cc2)cc1)C(=O)O,train
Clc1ccc(C(c2ccccc2Cl)C(Cl)(Cl)Cl)cc1,train
CC(=O)CCc1ccccc1,valid
Cc1ccc(N)cc1C,train
CCOC(=O)[C@H](CCc1ccccc1)N[C@@H](C)C(=O)N1[C@H](C(=O)O)C[C@H]2CCCC[C@@H]21,train
COc1ccc(/C=C/C(=O)Nc2ccccc2C(=O)O)cc1OC,train
Cc1cccc(Nc2ccncc2S(=O)(=O)NC(=O)NC(C)C)c1,train
COc1cccc([C@@]2(O)CCCC[C@@H]2CN(C)C)c1,test
CCOC(=O)CC(SP(=S)(OC)OC)C(=O)OCC,train
CCOC(=O)CC(SP(=O)(OC)OC)C(=O)OCC,train
O=c1ccc(=O)[nH][nH]1,train
S=C([S-])NCCNC(=S)[S-].[Mn+2],train
NS(=O)(=O)c1cc2c(cc1Cl)NC(C(Cl)Cl)NS2(=O)=O,valid
CN(C)C(=O)Oc1cccc([N+](C)(C)C)c1,train
O=C([O-])c1cc(=O)c2c(OCC(O)COc3cccc4oc(C(=O)[O-])cc(=O)c34)cccc2o1,train
Cc1ccccc1S(=O)(=O)[O-],train
CC(=O)O[AlH3](O)OC(C)=O,train
COc1c(C)c2c(c(O)c1C/C=C(\C)CCC(=O)O)C(=O)OC2,test
CCCN(CC1CC1)c1c([N+](=O)[O-])cc(C(F)(F)F)cc1[N+](=O)[O-],train
C[C@H]1CC[C@]2(NC1)O[C@H]1C[C@H]3[C@@H]4CC[C@H]5C[C@@H](O)CC[C@]5(C)[C@H]4CC[C@]3(C)[C@H]1[C@@H]2C,train
OC[C@@H](O)C(O)[C@H](O)CO,train
C=C(C)C(=O)NCCC[N+](C)(C)C,train
Oc1ccc(Cl)c2cccnc12,valid
CC(=O)Oc1cccc(C(=O)O)c1OC(C)=O,train
COc1ccc(-c2cc(C(F)F)nn2-c2ccc(S(N)(=O)=O)cc2)cc1F,train
O=C1C2CC=CCC2C(=O)N1SC(Cl)(Cl)Cl,train
O=C1C2CC=CCC2C(=O)N1SC(Cl)(Cl)C(Cl)Cl,train
COc1cc(CNC(=O)CCCC/C=C/C(C)C)ccc1O,test
O=C1CCCCCN1,train
CNC(=O)Oc1cccc2ccccc12,train
NC(=O)Nc1ccc([As](=O)(O)O)cc1,train
NC(=O)NNc1ccccc1,train
NNC(N)=O,valid
CNC(=O)Oc1cccc2c1OC(C)(C)C2,train
c1ccc2c(c1)[nH]c1ccccc12,train
N#CC(c1ccccc1)c1ccccc1,train
O=C(Nc1cccc2c(=O)cc(-c3nnn[nH]3)oc12)c1ccc(OCCCCc2ccccc2)cc1,train
COc1cc(S(=O)(=O)Nc2ccccc2)c(OC)cc1N,test
O=C(O)c1cc(I)cc(I)c1I,train
O=C(CC(=O)C(F)(F)F)c1ccccc1,train
O=C1CCCc2c1cc1ccc3cccc4ccc2c1c34,train
O=Cc1cc(O)ccc1Br,train
CCn1cc[n+](C)c1C.F[B-](F)(F)F,valid
OCCCCCCCCCCCCBr,train
COS(=O)(=O)C(F)(F)F,train
CO[C@@H]1[C@@H](O[C@@H]2O[C@H](C)[C@@H](O[C@H]3C[C@@](C)(O)[C@@H](OC(=O)CC(C)C)[C@H](C)O3)[C@H](N(C)C)[C@H]2O)[C@@H](CC=O)C[C@@H](C)[C@@H](O)C=CC=CC[C@@H](C)OC(=O)C[C@H]1O,train
CC(C)(C)c1cc(Cc2cc(C(C)(C)C)c(O)c(C(C)(C)C)c2)cc(C(C)(C)C)c1O,train
CCCCNc1c(C(=O)OCC)c(C)nc2c1cnn2CC,test
Cc1scc2c1N(C(=O)CN1CCN(C)CC1)c1ccccc1NC2=O,train
Cc1c(C(N)=O)cc([N+](=O)[O-])cc1[N+](=O)[O-],train
CN/C(=N\c1ccc(C2=NNC(=O)CC2C)cc1)NC#N,train
CC(C)COC(=O)OCN1C(=O)CN(CCN2CC(=O)N(COC(=O)OCC(C)C)C(=O)C2)CC1=O,train
C[n+]1cc2c3c(ccc2c2ccc4cc5c(cc4c21)OCO5)OCO3,valid
O=C(CCCN1CCC2(CC1)C(=O)NCN2c1ccccc1)c1ccc(F)cc1,train
O=c1[nH]oc2c1CCNC2,train
CC(COCCO)OCCO,train
CCCCCCCCc1ccc(S(=O)(=O)[O-])cc1,train
CC1=C(/C=C/C(C)=C/C=C/C(C)=C/C=O)C(C)(C)CCC1,test
CC1=C(/C=C/C(C)=C/C=C/C(C)=C\C=O)C(C)(C)CCC1,train
Oc1cc2ccccc2c2ccccc12,train
O=Nc1ccc(O)cc1,train
O=[N+]([O-])c1cc(I)c(O)c(I)c1,train
O=C(Cn1ccnc1[N+](=O)[O-])NCc1ccccc1,valid
CN(C)c1ccc(C(=C2C=CC(=[N+](C)C)C=C2)c2c(O)c(S(=O)(=O)[O-])cc3cc(S(=O)(=O)[O-])ccc23)cc1,train
O=C(OCCc1ccccc1)c1ccccc1,train
OC[C@@H](O)[C@H]1O[C@@H]2O[C@@H](C(Cl)(Cl)Cl)O[C@@H]2[C@H]1O,train
Cc1cn([C@H]2C[C@H](F)[C@@H](CO)O2)c(=O)[nH]c1=O,train
O=S(=O)([O-])c1cc(O)c2c(N=Nc3ccc(Nc4ccccc4)c4c(S(=O)(=O)[O-])cccc34)cc(S(=O)(=O)[O-])cc2c1,test
CC(C)CCCCCCc1ccc(OCCOCCO)cc1,train
OCC#CCO,train
CCCN1CCC[C@@H]2Cc3nc(N)ncc3C[C@H]21,train
O=C1CC[C@@]2(O)[C@H]3Cc4ccc(O)c5c4[C@@]2(CCN3CC2CC2)[C@H]1O5,train
O=C(O)C(Br)CBr,valid
CCN1C[C@@]2(COC)[C@H]3[C@@H](OC)[C@H]4C1[C@]3([C@@H](OC)C[C@H]2O)[C@@H]1C[C@]2(O)[C@@H](OC)[C@H](O)[C@@]4(OC(C)=O)[C@H]1[C@H]2OC(=O)c1ccccc1,train
COC(=O)C1=C(C)NC(C)=C(C(=O)OCCCN2CCC(c3ccccc3)(c3ccccc3)CC2)C1c1cccc([N+](=O)[O-])c1,train
CCNS(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
N#CSCSc1nc2ccccc2s1,train
C=CC(=O)NC(C)(C)CS(=O)(=O)[O-],test
Clc1ccc(C(Cl)(Cl)Cl)cc1,train
CCCCCCCCS(=O)(=O)[O-],train
Cc1ccc(/N=N/c2c(O)c(C(=O)[O-])cc3ccccc23)c(S(=O)(=O)[O-])c1,train
CCc1cccc(C)c1N,train
O=C([O-])CS,valid
CC(C)OB(OC(C)C)OC(C)C,train
C[C@]12C(=O)OC(=O)[C@H]1[C@H]1C=C[C@@H]2C1,train
CC(C)CCCCCOC(=O)CS,train
CC(COc1ccc(Oc2ccccc2)cc1)Oc1ccccn1,train
O=C(O)c1c(Cl)ccc2cc(Cl)cnc12,test
CCC(O)OCC(C)OC(O)CC,train
COc1ccc(/C=N/NC(=O)c2ccncc2)cc1OC,train
C=CC(=O)OCCC(=O)O,train
CCOCC(O)COc1ccc(NC(=O)CC[S+](C)C)cc1,train
CCOC(=O)C(C)OC(=O)c1cc(Oc2ccc(C(F)(F)F)cc2Cl)ccc1[N+](=O)[O-],valid
CC[C@@]1(c2ccccc2)NC(=O)N(C)C1=O,train
COC(=O)c1ccccc1CS(=O)(=O)NC(=O)Nc1nc(OC)cc(OC)n1,train
O=C1C=CC(=O)O1,train
CC(C)=CC1C(C(=O)OCN2C(=O)C3=C(CCCC3)C2=O)C1(C)C,train
C[C@H](CSC(=O)c1ccccc1)C(=O)N1C[C@@H](Sc2ccccc2)C[C@H]1C(=O)[O-].C[C@H](CSC(=O)c1ccccc1)C(=O)N1C[C@@H](Sc2ccccc2)C[C@H]1C(=O)[O-],test
CC(C)NCC(O)c1ccc2ccccc2c1,train
CC(CN1c2ccccc2Sc2ccccc21)N(C)C,train
Nc1ccc2cc3ccc(N)cc3nc2c1,train
CNNCc1ccc(C(=O)NC(C)C)cc1,train
CC(C)Nc1nc(Cl)nc(NC(C)C)n1,valid
O=S1(=O)CCCO1,train
CCC(C)(CCC(C)C)C(=O)[O-].CCC(C)(CCC(C)C)C(=O)[O-].[Zn+2],train
C=CCON/C(CCC)=C1\C(=O)CC(C)(C)C(C(=O)OC)=C1[O-],train
CC(=O)/C=C/C1C(C)=CCC(C)C1(C)C,train
O=S(=O)([O-])SSS(=O)(=O)[O-],test
CCCCCCCCCCCC(=O)OCC,train
C=C1[C@H]2CC[C@@H]3[C@H]2C(C)(C)CCC[C@]13C,train
CCCCOC(=O)C(C)CC,train
CCCCCCCCCCCCOCCOCCOCCOCCOCCOCCOCCOCCOCCO,train
Nc1c(S(=O)(=O)O)cc(Br)c2c1C(=O)c1ccccc1C2=O,valid
Cc1cc(Cl)c(C)cc1Cl,train
Clc1nc(C(Cl)(Cl)Cl)c(Cl)c(Cl)c1Cl,train
O=S(=O)(O)c1cc(O)c2ccc(Nc3ccccc3)cc2c1,train
C=C(C)C(=O)OCCC(C)OC(=O)C(=C)C,train
CC1=NN(c2cccc(S(=O)(=O)O)c2)C(=O)C1,test
CC(C)CC(C)N(c1ccccc1)c1ccc(N)cc1,train
OCC1(CO)COCOC1,train
O=S(=O)(O)c1ccc2ccccc2c1,train
Nc1cccc(S(=O)(=O)c2cccc(N)c2)c1,train
CC1=NN(c2ccc(C(=O)O)cc2)C(=O)C1,valid
Nc1ccc(C(=O)OCCCOC(=O)c2ccc(N)cc2)cc1,train
CC(C)C1OCCN1CCOC(=O)NCCCCCCNC(=O)OCCN1CCOC1C(C)C,train
Clc1ccc(Cl)c(Cl)c1Cl,train
NS(=O)(=O)c1cc2c(cc1C(F)(F)F)NC(Cc1ccccc1)NS2(=O)=O,train
CCN(CC)CCOC(=O)C(O)(c1ccccc1)c1ccccc1,test
CCC1(CC)C(=O)NC(=O)NC1=O,train
C[C@H]1[C@H](NC(=O)C(=NOC(C)(C)C(=O)O)c2csc(N)n2)C(=O)N1S(=O)(=O)O,train
Nc1nc(=O)c2c([nH]1)NCC(CNc1ccc(C(=O)N[C@@H](CCC(=O)[O-])C(=O)[O-])cc1)N2C=O,train
O=C(O)c1ccc(C(=O)O)cc1,train
CC(C)(c1cc(Br)c(O)c(Br)c1)c1cc(Br)c(O)c(Br)c1,valid
BrCC(Br)(Br)Br,train
BrC(Br)C(Br)Br,train
O=C1OC(=O)c2c(Br)c(Br)c(Br)c(Br)c21,train
CC(=O)Nc1ccc(OC(=O)c2ccccc2OC(C)=O)cc1,train
CC(=O)c1c(C)c([N+](=O)[O-])c(C(C)(C)C)c([N+](=O)[O-])c1C,test
C=CC(=C)CCC=C(C)C,train
C=CCc1cc(OC)c2c(c1)OCO2,train
Nc1ccc(N)c2ccccc12,train
CS(C)=O,train
CO,valid
CCCO,train
CNC(=O)OCC[N+](C)(C)C,train
c1ccc2cc(CC3=NCCN3)ccc2c1,train
C[C@]12N[C@H](Cc3ccccc31)c1ccccc12,train
CC1(C)CCC(NC(=O)[C@@H](N)CCC(=O)O)CC1,test
Cc1c(C)c2c(c(C)c1O)CC[C@@](C)(CCC[C@H](C)CCC[C@H](C)CCCC(C)C)O2,train
CC[N+](CC)(CC)CCOc1ccc(/C=C/c2ccccc2)cc1,train
Cc1cccc(C)c1OCC(C)N,train
COc1ccc(OC)c(C(O)C(C)N)c1,train
Nc1ccc(Cc2ccc(N)cc2)cc1,valid
CN1CCN(CCCN2c3ccccc3Sc3ccccc32)CC1,train
COc1ccc2[nH]cc(CCNC(C)=O)c2c1,train
CC(=O)OCc1ccco1,train
OCc1ccco1,train
O=C(c1ccc(O)cc1)c1ccc(O)cc1,test
CN1CCCC(OC(=O)C(O)(c2ccccc2)c2ccccc2)C1,train
COC(=O)N(OC)c1ccccc1COc1ccn(-c2ccc(Cl)cc2)n1,train
CC1=NNC(=O)N(/N=C/c2cccnc2)C1,train
CC[C@@H](C(=O)[C@@H](C)[C@@H](O)[C@H](C)[C@@H]1O[C@@H]([C@@H](CC)C(=O)O)[C@@H](C)C[C@@H]1C)[C@H]1O[C@]2(C=C[C@@H](O)[C@]3(CC[C@@](C)([C@H]4CC[C@](O)(CC)[C@H](C)O4)O3)O2)[C@H](C)C[C@@H]1C,train
COCC(=O)N(c1c(C)cccc1C)N1CCOC1=O,valid
c1ccc(CNc2[nH]cnc3ncnc2-3)cc1,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](C(=O)[O-])c3ccsc3)C(=O)N2[C@H]1C(=O)[O-],train
C[C@@H](CN(C)C)CN1c2ccccc2Sc2ccccc21.C[C@@H](CN(C)C)CN1c2ccccc2Sc2ccccc21,train
CCC(=O)O[C@@](Cc1ccccc1)(c1ccccc1)[C@@H](C)CN(C)C.O=S(=O)(O)c1ccc2ccccc2c1,train
CCN(CCCc1ccccc1)CCCc1ccccc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,test
Cc1ccc(Cl)c(Nc2ccccc2C(=O)[O-])c1Cl,train
CN1CCc2c(c3ccccc3n2Cc2ccccc2)C1.CN1CCc2c(c3ccccc3n2Cc2ccccc2)C1.O=S(=O)(O)c1cccc2c(S(=O)(=O)O)cccc12,train
COc1ccc2c(c1)[C@@]13CCCCC1[C@@H](C2)N(C)CC3,train
CCN(CC)Cc1cc(Nc2ccnc3cc(Cl)ccc23)ccc1O,train
CCN(CC)c1ccc(N=Nc2ccc(S(=O)(=O)N(C)C)cc2)c(S(=O)(=O)NCC[n+]2ccccc2)c1,valid
CC[C@H]1O[C@]2(CC[C@@H]1C)C[C@@H]1C[C@@H](CC=C(C)C[C@@H](C)C=CC=C3CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,train
CO[C@@H]1[C@@H](O[C@@H]2O[C@H](C)[C@@H](O[C@H]3C[C@@](C)(O)[C@@H](O)[C@H](C)O3)[C@H](N(C)C)[C@H]2O)[C@@H](CC=O)C[C@@H](C)[C@@H](O[C@@H]2CC[C@H](N(C)C)[C@@H](C)O2)C=CC=CC[C@@H](C)OC(=O)C[C@H]1O,train
C1CCN2C[C@@H]3C[C@@H](CN4CCCC[C@H]34)[C@@H]2C1,train
C[C@H]1CN(c2c(F)c(N)c3c(=O)c(C(=O)O)cn(C4CC4)c3c2F)C[C@@H](C)N1,train
O=C1CSC2(CCN(CCCN3c4ccccc4Sc4ccc(Cl)cc43)CC2)N1,test
CC1(C)OC[C@@H](COC(=O)CCc2ccc(OC[C@@H](O)CNCCNC(=O)N3CCOCC3)cc2)O1,train
CCCCOC(=O)CCCCCCCCC(=O)OCCCC,train
N#C/C(=C1/SC[C@@H](c2ccc(Cl)cc2Cl)S1)n1ccnc1,train
O=C([O-])c1ccccc1Nc1cc(Cl)ccc1C(=O)[O-],train
CCCCCCCCN(C)C,valid
Oc1ccc2ccccc2c1,train
CCCCCCCCCCCC(=O)N(C)CC(=O)[O-],train
CCC(C)CO,train
Nc1ncnc2c1ncn2[C@H]1CC[C@@H](CO)O1,train
O=C1[N-]C(=O)C(c2ccccc2)(c2ccccc2)N1,test
O=C(OCC1(COC(=O)c2cccnc2)CCCC(COC(=O)c2cccnc2)(COC(=O)c2cccnc2)C1O)c1cccnc1,train
CN1CSC(=S)N(C)C1,train
CCOC(=O)OC1=C(c2cc(C)ccc2C)C(=O)NC12CCC(OC)CC2,train
[O-]c1cc(Cl)c(Cl)cc1Cl,train
CCCCCC/C=C\CCCCCCCC(=O)O,valid
C=C(C)[C@@H]1CC=C(C=O)CC1,train
COc1ccc(C(C)=O)cc1,train
COc1cc(Cl)ccc1Cl,train
CC(=O)CC(=O)N(C)C,train
Cc1cccc(C)c1NC(=O)CN1CCCCCC1,test
Cc1nc2ccccc2n1Cc1ccc(Cl)cc1,train
C[C@@H](Cc1ccccc1)N(C)C,train
O=C(CN1CCNCC1)NC1CCCCC1,train
CCCCNCC(O)c1ccc(O)cc1,train
CC(Cc1ccccc1)NCc1ccccc1Cl,valid
COC(=O)C1=C(C)NC(C)=C(C(=O)OC)C1c1ccccc1OC(F)F,train
CCCCCCCCCCCSCC(=O)O,train
COC(CC(C)CCCC(C)(C)O)OC,train
Oc1ccc2c(c1)-c1ccccc1C2,train
COC(=O)C(C)N(C(=O)c1ccco1)c1c(C)cccc1C,test
CO/C=C(/C(=O)OC)c1ccccc1COc1cccc(C(F)(F)F)n1,train
C=CC(C)(O)CC/C=C(/C)CCC=C(C)C,train
CCOC(=O)CC1(C)OCC(C)O1,train
O=C(O)CCCCC1CCSS1,train
COC(=O)CC(C(C)=O)C(=O)OC,valid
CC(C)N(Cc1ccccc1)C(=O)C(C)(C)C,train
CC1CCC(C(C)CS)CC1S,train
C=CC(C)(O)CCC=C(C)C,train
C=CC(C)(CCC=C(C)C)OC(=O)c1ccccc1N,train
CCCCC/C=C\C/C=C\CCCCCCCC(=O)O,test
CC/C=C\C/C=C\C/C=C\CCCCCCCC(=O)O,train
COCCC#N,train
O=C(O)CCSSCCC(=O)O,train
CCCC[n+]1ccccc1,train
CCOP(=S)(OCC)Oc1ncn(-c2ccccc2)n1,valid
CSc1ccc(C=O)cc1,train
Cc1c([N+](=O)[O-])c(C)c([N+](=O)[O-])c(C(C)(C)C)c1[N+](=O)[O-],train
CNC(=S)N(C)C,train
CCCOCCC,train
CCCSP(=S)(OCC)Oc1ccc(Cl)cc1Cl,test
COP(=S)(Oc1cc(Cl)c(Br)cc1Cl)c1ccccc1,train
CSC(C)(C)/C=N\O,train
N#Cc1sc2c(=O)c3ccccc3c(=O)c=2sc1C#N,train
Cc1ccc2ccccc2n1,train
CCOP(=S)(OCC)Oc1ccc(Cl)cc1Cl,valid
N#CCCCl,train
ClCBr,train
O=C([O-])C(=O)[O-].[Ca+2],train
C=CC(C)(C)C,train
CCc1cccc(CC)c1N,test
O=C([O-])c1ccccc1,train
c1ccc2c(c1)-c1cccc3c1c-2cc1ccccc13,train
c1ccc2cc3c(ccc4ccccc43)cc2c1,train
CCCCNC(=O)n1c(NC(=O)OC)nc2ccccc21,train
CC(C)N1C(=O)c2ccccc2NS1(=O)=O,valid
c1cc2ccc3ccc4ccc5cccc6c(c1)c2c3c4c56,train
c1ccc2cc3c(cc2c1)-c1cccc2cccc-3c12,train
CCCCCCCCCCCCCCCC[N+](C)(C)C,train
Cc1ccc(C)c(S(=O)(=O)[O-])c1,train
O=C(C(Cl)Cl)N1CCOC12CCCCC2,test
CC1=CC(C)C(C=O)CC1,train
Br[Ca]Br,train
CCOC(=O)CC(CC(=O)OCC)(OC(C)=O)C(=O)OCC,train
CC(C)c1cccc(O)c1,train
NCCN,valid
CCC(C)(C)C1CCCCC1OC(C)=O,train
OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
COc1ccc(CCN(C)CCCC(C#N)(c2cc(OC)c(OC)c(OC)c2)C(C)C)cc1OC,train
COc1cc2c(cc1OC)-c1cc3ccc(OC)c(OC)c3c[n+]1CC2,train
CCCC[P+](C)(CCCC)CCCC.COS(=O)(=O)[O-],test
CCCCCCn1cc[n+](C)c1.O=S(=O)([O-])C(F)(F)F,train
C[n+]1ccn(CCO)c1,train
CCCCCCCCn1cc[n+](C)c1,train
C[C@@H]([C@H](O)c1ccccc1)N(C)C,train
OC[C@H]1O[C@@H](n2cnc3c(O)ncnc32)[C@H](O)[C@@H]1O,valid
CO[C@H]1C=CO[C@@]2(C)Oc3c(C)c(O)c4c(O)c(c5c(nc6cc(C)ccn65)c4c3C2=O)NC(=O)C(C)=CC=C[C@H](C)[C@H](O)[C@@H](C)[C@@H](O)[C@@H](C)[C@H](OC(C)=O)[C@@H]1C,train
CN(C)N,train
CN(C)C=O,train
CN(C)C(=O)Cl,train
Cc1c2ccccc2c(C)c2c1ccc1ccccc12,test
O=C1NCN(c2ccccc2)C12CCN(CC1COc3ccccc3O1)CC2,train
CN/C(=C\[N+](=O)[O-])NCCSCc1ccc(CN(C)C)o1,train
Nc1nc2ccc(OC(F)(F)F)cc2s1,train
Cc1nc2n(c(=O)c1CCN1CCC(c3noc4cc(F)ccc34)CC1)CCCC2,train
CCCN(CCC)CCc1cccc2c1CC(=O)N2,valid
COc1ccccc1OCC(O)CN1CCN(CC(=O)Nc2c(C)cccc2C)CC1,train
C[C@]12CC[C@H]3[C@@H](CC[C@H]4CC(=O)CC[C@@]43C)[C@@H]1CC[C@@H]2O,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CCC2=O,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1C[C@@H](O)[C@@H]2O,train
C=CC1(C)OC(=O)N(c2cc(Cl)cc(Cl)c2)C1=O,test
CC(C)(C)CC(C)(C)c1ccc(O)cc1,train
C[C@]12CC[C@@H]3c4ccc(O)cc4C[C@@H](CCCCCCCCCS(=O)CCCC(F)(F)C(F)(F)F)[C@H]3[C@@H]1CC[C@@H]2O,train
CC(=O)NC(Cc1ccccc1)C(=O)O,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
CCN(CC)S(=O)(=O)c1ccc(C(=O)O)cc1,valid
CCC1(C)CC(=O)NC1=O,train
BrCCCCCCBr,train
O=c1[nH]c(=O)c2[nH]c(=O)[nH]c2[nH]1,train
CC(C)CC(=O)C1C(=O)c2ccccc2C1=O,train
O=C([O-])c1cc2cc(Cc3cccnc3)ccc2o1,test
NC(=O)Nc1ccccc1,train
CCCOc1ccccc1-c1nc2[nH]nnc2c(=O)[nH]1,train
O=P([O-])(O)C(Cl)(Cl)P(=O)([O-])O,train
O=C(NCCNCC(O)COc1ccc(O)cc1)N1CCOCC1,train
CCCCOC(CC)OC(C)COC(O)CC,valid
CCN(Cc1ccncc1)C(=O)C(CO)c1ccccc1,train
CC(C)(C)C1CCC(O)CC1,train
O=c1[nH]cnc2c1ncn2[C@H]1CC[C@@H](CO)O1,train
CCc1nc(C)c[nH]1,train
CC1(C)C(=O)C(C)(C)C1=O,test
NC(=O)C1c2ccccc2CCc2ccccc21,train
NCCOCCOCCN,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(OC5CCCC5)ccc4[C@H]3CC[C@@]21C,train
CC[C@H](C)[C@@H]1NC(=O)[C@H](Cc2ccc(O)cc2)NC(=O)[C@@H](N)CSSC[C@@H](C(=O)N2CCC[C@H]2C(=O)N[C@@H](CC(C)C)C(=O)NCC(N)=O)NC(=O)[C@H](CC(N)=O)NC(=O)[C@H](CCC(N)=O)NC1=O,train
CC(C)(C)C(=O)C(Oc1ccc(Cl)cc1)n1ccnc1,valid
O=C(O)Cn1c2ccccc2c(=O)c2ccccc21,train
O=C(NC(=O)c1c(F)cccc1F)Nc1ccc(Cl)c(Oc2ncc(C(F)(F)F)cc2Cl)c1,train
CN1CCN(C(=O)O[C@H]2c3nccnc3C(=O)N2c2ccc(Cl)cn2)CC1,train
CCCCCCCCCCCCc1ccccc1,train
C=CC1=C(C(=O)O)N2C(=O)[C@@H](NC(=O)/C(=N/O)c3csc(N)n3)[C@H]2SC1,test
O=C1CCc2ccc(OCCCCN3CCN(c4cccc(Cl)c4Cl)CC3)cc2N1,train
CCOC(=O)C1=C(C)NC(C)=C(C(=O)OC)C1c1cccc([N+](=O)[O-])c1,train
Cc1cc(OI)c(C(C)C)cc1-c1cc(C(C)C)c(OI)cc1C,train
CCOP(OCC)OCC,train
CC(C)CC(=O)CC(C)CC(C)C,valid
CCc1nn(CCCN2CCN(c3cccc(Cl)c3)CC2)c(=O)n1CCOc1ccccc1,train
O=[N+]([O-])c1ccc2ncccc2c1,train
O=[N+]([O-])c1cccc2cccnc12,train
CC(C)n1c(/C=C/[C@@H](O)C[C@@H](O)CC(=O)O)c(-c2ccc(F)cc2)c2ccccc21,train
C=O,test
O=c1[nH]cc(F)c(=O)[nH]1,train
C[C@]12Cc3nonc3C[C@@H]1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@]2(C)O,train
Cn1c(CCCC(=O)O)nc2cc(N(CCCl)CCCl)ccc21,train
O=C(NNCS(=O)(=O)[O-])c1ccncc1,train
CCCCCCCCCC(=O)NO,valid
CC(C)c1cc2c(cc1S(=O)(=O)O)[C@@]1(C)CCC[C@@](C)(C(=O)O)[C@@H]1CC2,train
C[N+](C)(C)CC[N+]1(C)Cc2c(Cl)c(Cl)c(Cl)c(Cl)c2C1,train
CCCCCCC(C)(C)S,train
Cc1nnc2n1-c1ccc(Cl)cc1C(c1ccccc1)=NC2,train
CCc1ccccc1CC,test
CCOP(=O)(NC(C)C)Oc1ccc(SC)c(C)c1,train
COC(=O)c1ccccc1S(=O)(=O)NC(=O)N(C)c1nc(C)nc(OC)n1,train
CCOC(=O)COC(=O)c1ccccc1C(=O)OCC,train
Cn1cc(-c2ccccc2)c(=O)c(-c2cccc(C(F)(F)F)c2)c1,train
c1ccc2c(c1)Cc1ccccc1-2,valid
c1ccc2c(c1)-c1cccc3cccc-2c13,train
CC(=O)CC(C)(C)C1CCCCC1,train
CC1(O)CSC(C)(O)CS1,train
CC(C)Oc1cccc(NC(=O)c2ccccc2C(F)(F)F)c1,train
CC(C)C(O)(c1ccc(OC(F)(F)F)cc1)c1cncnc1,test
CC1(C)CCC[C@@]2(C)[C@H]1CC[C@@]1(C)OC(=O)C[C@H]21,train
O=C1O[C@H]([C@@H](O)CO)C([O-])=C1O.O=C1O[C@H]([C@@H](O)CO)C([O-])=C1O,train
C/C=C(\C)C(=O)OCCc1ccccc1,train
CN1[C@H]2CC[C@@H]1C[C@H](OC(=O)C(O)c1ccccc1)C2,train
CCNc1nc(NC(C)CC)nc(OC)n1,valid
CCNc1nc(NCC)nc(SC)n1,train
C#CC(C)N(C)C(=O)Nc1ccc(Cl)cc1,train
CC(=O)OC/C=C(/C)CCC=C(C)C,train
CCCC(=O)C(C)=O,train
Cc1ccnc2ccccc12,test
O=C(O)CCc1ccccc1,train
COc1ccccc1OC,train
CCC/C=C/CO,train
COc1cccc(OC)c1,train
CCCCCCCCCCCCCCCCC,valid
C=C[C@@]1(C)CC(=O)[C@@]2(O)[C@](C)(O1)[C@@H](OC(C)=O)[C@@H](O)[C@H]1C(C)(C)CC[C@H](O)[C@@]12C,train
O=S(=O)(O)c1ccc2[nH]c(-c3ccccc3)nc2c1,train
CCC(C)c1nccnc1OC,train
Cc1cc(C(=C2C=CC(=Nc3ccc(S(=O)(=O)[O-])cc3S(=O)(=O)O)C=C2)c2ccc(Nc3ccc(S(=O)(=O)[O-])cc3)cc2)ccc1N,train
CC1(C)CC(=O)CC(C)(C)N1[O],test
OCC1CC2CC1C1CCC(CO)C21,train
C=CCOC(=O)c1ccc(C(=O)OCC=C)c(C(=O)OCC=C)c1,train
CCC(C)(CCC(C)C)C(=O)[O-].CCC(C)(CCC(C)C)C(=O)[O-],train
CCOC(=O)/C=C(\C)O[Ti](O/C(C)=C/C(=O)OCC)(OC(C)C)OC(C)C,train
CC1(C)C(O)C(C)(C)C1O,valid
CCC1(c2ccccc2)C(=O)N(COC)C(=O)N(COC)C1=O,train
O=C1OC(=O)c2cccc3cccc1c23,train
CN1CCC(=C2c3ccccc3Sc3ccccc32)CC1,train
CN(C)[C@@H]1C(O)=C(C(=O)NCN[C@@H](CCCCN)C(=O)O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)C3C[C@@H]12,train
CCOC(=O)CCC(=O)O[C@H]1[C@H](O[C@@H]2[C@@H](C)[C@H](O[C@H]3C[C@@](C)(OC)[C@@H](O)[C@H](C)O3)[C@@H](C)C(=O)O[C@H](CC)[C@@](C)(O)[C@H](O)[C@@H](C)C(=O)[C@H](C)C[C@@]2(C)O)O[C@H](C)C[C@@H]1N(C)C,test
O=C(O)CC(C(=O)O)S(=O)(=O)O,train
CO[C@H]1[C@@H](O)[C@H](N)[C@@H](O[C@H]2O[C@H]([C@H](C)N)CC[C@H]2N)[C@H](O)[C@@H]1N(C)C(=O)CN,train
CC(C)NCC(O)COc1ccc(CC(N)=O)cc1,train
CCCNC(C)(C)COC(=O)c1ccccc1,train
C[C@]12CC[C@@H]3c4ccc(OC(=O)c5ccccc5)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,valid
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2OC(=O)CCC1CCCC1,train
CC(=O)OC[C@H]1O[C@H](O[C@]2(COC(C)=O)O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@@H]2OC(C)=O)[C@H](OC(C)=O)[C@@H](OC(C)=O)[C@@H]1OC(C)=O,train
OCC(O)c1ccccc1,train
CC1=CC(=O)c2ccccc2C1=O,train
Cc1cc(O)ccc1Cl,test
Oc1c(Cl)cc(Cl)c(Cl)c1Cl,train
c1ccc(CC2=NCCN2)cc1,train
COC(=O)[C@@H](N)Cc1ccc(O)c(O)c1,train
CCc1cccc(N(C)C(=N)Nc2cccc3ccccc23)c1,train
CCOC(=O)c1cnc2c(cnn2CC)c1NN=C(C)C,valid
CCCCCCCCCCCCCCCCCCOC[C@H](COP(=O)([O-])OCC[N+](C)(C)C)OC,train
NCCCC(N)(C(=O)O)C(F)F,train
C[C@]12CC[C@H]3[C@@H](CC=C4C[C@@H](OS(=O)(=O)[O-])CC[C@@]43C)[C@@H]1CCC2=O,train
CCc1cc(Cl)c(OC)c(C(=O)NC[C@@H]2CCCN2CC)c1O,train
CCCCCCCCCCCCCCC[C@H](O)[C@@H](N)CO,test
CCCCCCC(O)CO,train
C=C(C)C(=O)OCc1ccccc1,train
C=C(C)C(=O)OCC1CCCO1,train
C=CC(=O)OCCCCCC,train
Cc1ccn2nc(S(=O)(=O)Nc3c(F)cccc3F)nc2n1,valid
CCCN(CCCl)c1c([N+](=O)[O-])cc(C(F)(F)F)cc1[N+](=O)[O-],train
CCCSP(=O)(OCC)SCCC,train
CCOc1ccc(C(C)(C)COCc2cccc(Oc3ccccc3)c2)cc1,train
COP(=S)(OC)Oc1ccc([N+](=O)[O-])c(C)c1,train
CCOP(=S)(OCC)OC(Cl)C(Cl)(Cl)Cl,test
O=C1c2ccccc2C(=O)C1C(=O)C(c1ccccc1)c1ccc(Cl)cc1,train
CC(C)(C)N(NC(=O)c1ccc(Cl)cc1)C(=O)c1ccccc1,train
O=C(O)/C=C/c1ccc(Cn2ccnc2)cc1,train
CCOC(=O)O[C@]1(C(=O)COC(=O)CC)CC[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@H]3[C@@H](O)C[C@@]21C,train
CS(=O)(=O)c1ccc([C@@H](O)[C@@H](CF)NC(=O)C(Cl)Cl)cc1,valid
CC(=O)[C@@]1(O)CC[C@H]2[C@@H]3C[C@H](C)C4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
C[C@@H]1CN([C@H]2CC[C@](C#N)(c3ccc(F)cc3)CC2)CC[C@]1(C(=O)O)c1ccccc1,train
O=C(O)c1cc(O)c2c(c1)[C@H]([C@H]1c3cc(C(=O)O)cc(O)c3C(=O)c3c(O[C@@H]4O[C@H](CO)[C@@H](O)[C@H](O)[C@H]4O)cccc31)c1cccc(O[C@@H]3O[C@H](CO)[C@@H](O)[C@H](O)[C@H]3O)c1C2=O,train
O=C(Oc1ccccc1C(=O)O)c1ccccc1O,train
OCc1cc(C(O)CNCCCCCCOCCCCc2ccccc2)ccc1O,test
CCC(=O)OC(=O)CC,train
Cc1c(C)c2c(c(C)c1O)CCC(C)(COc1ccc(CC3SC(=O)NC3=O)cc1)O2,train
Cc1ccc(/C(=C\CN2CCCC2)c2ccccn2)cc1,train
COc1cc(C(=O)N2CCOCC2)cc(OC)c1OC,train
COc1cc(Cc2cnc(N)nc2N)cc(OC)c1OC,valid
Cc1cc2cc3c(C)cc(=O)oc3c(C)c2o1,train
CCOP(=S)(OCC)Oc1cnc2ccccc2n1,train
NC1CCC(CC2CCC(N)CC2)CC1,train
N#C/C(N)=C(/N)C#N,train
NCCCCCCN,test
C[C@H]1[C@H](c2ccc(Cl)cc2)SC(=O)N1C(=O)NC1CCCCC1,train
CCCC[Sn](CCCC)(CCCC)O[Sn](CCCC)(CCCC)CCCC,train
CC(CCl)OC(C)CCl,train
OCC(CO)(CBr)CBr,train
Cc1c(COC(=O)C2C(/C=C(\Cl)C(F)(F)F)C2(C)C)cccc1-c1ccccc1,valid
CC1(C)C(=O)N(Br)C(=O)N1Br,train
ClCCOCCCl,train
O=C(O)CN(CCN(CC(=O)O)CC(=O)O)CC(=O)O,train
Nc1c2c(nc3ccccc13)CCCC2,train
CNC(=O)c1ccccc1,test
CN1C(=O)c2ccc([N+](=O)[O-])cc2C1=O,train
Cc1ccc(O)c(-n2nc3ccccc3n2)c1,train
CCCCCCCCCCCCCCCl,train
Cc1cc(O)c(C(C)(C)C)cc1C(C)(C)C,train
COc1ccc(Cl)cc1C,valid
Oc1ccc(Cl)cc1C1CCCC1,train
Cc1cc(Cl)ccc1N,train
Cc1occc(=O)c1O,train
CCC(CC)Nc1ccc(C)c(C)c1,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C[C@H](C)C4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,test
O=C(CCCN1CC=C(n2c(=O)[nH]c3ccccc32)CC1)c1ccc(F)cc1,train
CCCCN=C=O,train
C1CCCNCC1,train
OCCSCCO,train
P#[In],valid
CCN(CC)c1ccc2c(-c3ccccc3C(=O)O)c3ccc(N(CC)CC)cc3[o+]c2c1,train
O=C(NCc1cccnc1)Nc1ccc([N+](=O)[O-])cc1,train
Cc1cnc2c(C(=O)O)c(Cl)ccc2c1,train
CCOc1ccc2c(c1)C(C)CC(C)(C)N2,train
CC(=O)NCc1c(I)c(NC(C)=O)c(I)c(C(=O)O)c1I,test
CON(C)C(=O)Nc1ccc(Br)c(Cl)c1,train
O=C(CCCCC(=O)Nc1c(I)cc(I)c(C(=O)O)c1I)Nc1c(I)cc(I)c(C(=O)O)c1I,train
O=C(O)c1c(Cl)ccc(Cl)c1Cl,train
O=C(O)CC(Cl)C(=O)O,train
N#CCCl,valid
Cc1cccc2sc3nncn3c12,train
CC(=O)c1cc(C)oc1C,train
C=C(C)C1CC=C(C)C(OC(C)=O)C1,train
C=C1CCC=C(C)CC[C@@H]2[C@@H]1CC2(C)C,train
CCCC(C)(COC(N)=O)COC(=O)NC(C)C,test
C=C(C)C1CC=C(C)C(O)C1,train
CCCCCCCCCCCCCCCO,train
[Mn+2],train
CCC(C)Cl,train
CC1=CC(=O)CC(C)(C)C1,valid
Nc1cccc(C(F)(F)F)c1,train
NNC(=O)c1ccncc1,train
O=C(O)c1ccncc1,train
NC(=O)c1ccncc1,train
CC(C)CON=O,test
OC[C@H]1O[C@H](OC[C@@H](O)[C@@H](O)[C@H](O)[C@H](O)CO)[C@H](O)[C@@H](O)[C@@H]1O,train
FC(F)OC(Cl)C(F)(F)F,train
c1ccc(C[n+]2cccc3ccccc32)cc1,train
CCCCN1C(=O)c2ccccc2C1=O,train
O=C1OC2(c3ccccc31)c1cc(Br)c(O)c(Br)c1Oc1c2cc(Br)c(O)c1Br,valid
Cc1cc(C)c(C(=O)P(=O)(C(=O)c2c(C)cc(C)cc2C)c2ccccc2)c(C)c1,train
CC(C)(C)C(=O)/C=C/c1ccc(Cl)cc1,train
CCCCCCCCOCCC#N,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](N)c3ccc(O)cc3)C(=O)N2[C@H]1C(=O)O,train
Cc1cc(C(C)(C)c2ccc(O)c(C)c2)ccc1O,test
CC(c1ccc(O)cc1)c1ccc(O)cc1,train
CCCCC#N,train
COc1ccc(C(=O)c2cccc(O)c2)c(OC)c1,train
CCCCCOC(=O)c1ccc(O)cc1,train
CCn1cc[n+](C)c1.N#C[S-],valid
CCCCCCCCCOC(=O)c1ccc(O)cc1,train
O=C1/C(=C2\Nc3ccccc3C2=O)Nc2ccccc21,train
Oc1ccc(C2(c3ccc(O)cc3)CC3CC2C2CCCC32)cc1,train
C[C@H]1[C@H]2[C@@H](O[C@]13CC[C@@H](C)CO3)[C@@H](O)[C@H]1[C@@H]3CC[C@H]4C[C@@H](O[C@@H]5O[C@H](CO)[C@H](O[C@@H]6O[C@H](CO)[C@@H](O)[C@H](O[C@@H]7OC[C@@H](O)[C@H](O)[C@H]7O)[C@H]6O[C@@H]6O[C@H](CO)[C@@H](O)[C@H](O[C@@H]7O[C@H](CO)[C@@H](O)[C@H](O)[C@H]7O)[C@H]6O)[C@H](O)[C@H]5O)[C@H](O)C[C@]4(C)[C@H]3CC[C@@]12C,train
Nc1ccc(F)cc1F,test
CCCCC(=O)N(C)C,train
CN(C)c1nc(=O)n(C2CCCCC2)c(=O)n1C,train
CCCCCCOC(=O)c1ccccc1C(=O)OCCCCCC,train
COc1ccc2c(c1OC)C(=O)O[C@@H]2[C@H]1c2c(cc3c(c2OC)OCO3)CCN1C,train
CO[C@@H]1[C@@H](OC(N)=O)[C@@H](O)[C@H](Oc2ccc3c([O-])c(NC(=O)c4ccc(O)c(CC=C(C)C)c4)c(=O)oc3c2C)OC1(C)C,valid
CC(CCc1ccccc1)NC(C)C(O)c1ccc(O)cc1,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@@]21C,train
O=C1O[C@H]([C@H](O)CO)C([O-])=C1O,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,train
Clc1ccc2c(c1)C(c1ccccc1)=NCc1nncn1-2,test
Cc1cc2c(s1)Nc1ccccc1N=C2N1CCN(C)CC1,train
CCc1cc(C(N)=S)ccn1,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@@]21C,train
CC/C(=C(\c1ccc(O)cc1)c1ccc(OCCN(C)C)cc1)c1ccccc1,train
CN/C(=N\[N+](=O)[O-])NCc1cnc(Cl)s1,valid
CC[C@H](c1ccc(O)cc1)[C@@H](CC)c1ccc(O)cc1,train
CC(=O)OCC(CCn1cnc2cnc(N)nc21)COC(C)=O,train
NC(N)=Nc1nc(CSCC/C(N)=N\S(N)(=O)=O)cs1,train
C=C1C[C@@H]2[C@H](CC[C@]3(C)C(=O)CC[C@@H]23)[C@@]2(C)C=CC(=O)C=C12,train
COc1cc([C@@H]2c3cc4c(cc3C(O[C@@H]3O[C@@H]5CO[C@@H](C)O[C@H]5[C@H](O)[C@H]3O)C3COC(=O)[C@@H]32)OCO4)cc(OC)c1O,test
Cn1c(=O)c2c(ncn2CCOC(=O)C(C)(C)Oc2ccc(Cl)cc2)n(C)c1=O,train
CCOC(=O)c1cncn1C(C)c1ccccc1,train
CCc1cc2c(s1)-n1c(C)nnc1CN=C2c1ccccc1Cl,train
Cn1c(=O)c2c(ncn2CCO)n(C)c1=O,train
Cc1ccccc1NC(=N)Nc1ccccc1C,valid
CO[C@H]1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,train
CC(C)(C)c1ccc(O)c(C(C)(C)C)c1,train
CNC(=O)Oc1cccc(N=CN(C)C)c1,train
O=C1OCCO1,train
C=C(CC(=O)O)C(=O)O,test
CC(C)C(=O)OC(=O)C(C)C,train
O=C1NCCN1c1ncc([N+](=O)[O-])s1,train
Nc1nc2cc(Cl)ccc2o1,train
CN(CCCCCCCCCCN(C)C(=O)Oc1ccccc1[N+](C)(C)C)C(=O)Oc1ccccc1[N+](C)(C)C,train
COc1cc(NC(C)CCCN)c2ncccc2c1,valid
Cc1ccc(Cl)cc1N,train
Cc1ccc(N)cc1Cl,train
CC(O)CCl,train
Nc1ccc(N)c(Cl)c1,train
Nc1ccc(Cl)cc1N,test
Nc1ccc(Cl)c(N)c1,train
CCCCN(CCC(=O)OCC)C(C)=O,train
COc1cnc(OC)n2nc(NS(=O)(=O)c3c(OCC(F)F)cccc3C(F)(F)F)nc12,train
CC(=O)CCC(=O)[O-].CC(=O)CCC(=O)[O-],train
O=C([O-])[O-].[Sr+2],valid
CC(=O)N[C@@H](CCC(=O)O)C(=O)O,train
C=CC(=O)OC(C)(C)C,train
Cn1ccc2cc3c(cc21)CCN3C(=O)Nc1cccnc1,train
CC1(C)CC(O)CC(C)(C)N1,train
Cc1ccccn1,test
Cc1cccnc1,train
OC1CCCCC1,train
Cc1ccncc1,train
CC(C)OC(C)C,train
O=C(O)COc1ccc2ccccc2c1,valid
CCCCOC(=O)C(C)O,train
CNC(=S)NC,train
CCCCCCCCCC[N+](C)(C)[O-],train
O=C1CCC(=O)N1Cl,train
CCCCNS(=O)(=O)c1ccc(C)cc1,test
CCN(CCN)c1cccc(C)c1,train
CC(COC(=O)c1ccccc1)OC(=O)c1ccccc1,train
O=C([O-])CNc1ccccc1,train
Cc1ccc2c(c1)C(=O)OC2=O,train
CC(C)(N)C#N,valid
CNC(C)Cc1ccccc1OC,train
NCCN1CCOCC1,train
COP(=S)(OC)Oc1ccc(S(N)(=O)=O)cc1,train
CC1CC(C)C(=O)C1=O,train
CC(=O)SCc1ccco1,test
Cc1ccc(C(C)NC(=O)[C@@H](NC(=O)OC(C)C)C(C)C)cc1,train
c1coc(Cn2cccc2)c1,train
Brc1c(Br)c(Br)c(Br)c(Br)c1Br,train
CCCCCCCCCCC=CC#N,train
COC(=O)c1sccc1S(=O)(=O)NC(=O)Nc1nc(C)nc(OC)n1,valid
CCCCCCC,train
ClC1=C(Cl)C2(Cl)C3C4OC4C(Cl)C3C1(Cl)C2(Cl)Cl,train
COC(=O)C(C)Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1,train
CC1=NN(c2cc(Cl)c(S(=O)(=O)[O-])cc2Cl)C(=O)C1/N=N/c1ccc(S(=O)(=O)[O-])cc1,train
COc1ccccc1/N=N/c1c(S(=O)(=O)[O-])cc2cc(S(=O)(=O)[O-])cc(NC(C)=O)c2c1O,test
CC1(C)[C@@H](O[C@H]2O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]2O[C@@H]2O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]2O)CC[C@@]2(C)[C@H]1CC[C@]1(C)[C@@H]2C(=O)C=C2[C@@H]3C[C@@](C)(C(=O)O)CC[C@]3(C)CC[C@]21C,train
ClCCOCCOCCCl,train
COC(Cc1ccccc1)OC,train
Cc1c(C(=O)O)c(O)cc2c1C(=O)c1c(O)c([C@@H]3O[C@H](CO)[C@@H](O)[C@H](O)[C@H]3O)c(O)c(O)c1C2=O,train
Nc1c(/N=N/c2ccc(-c3ccc(/N=N/c4cc(S(=O)(=O)[O-])c5ccccc5c4N)cc3)cc2)cc(S(=O)(=O)[O-])c2ccccc12,valid
Cc1cc(-c2ccc(/N=N/c3ccc4c(S(=O)(=O)[O-])cc(S(=O)(=O)[O-])c(N)c4c3O)c(C)c2)ccc1/N=N/c1ccc2c(S(=O)(=O)[O-])cc(S(=O)(=O)[O-])c(N)c2c1O,train
O=C([O-])c1c(Cl)c(Cl)c(Cl)c(Cl)c1-c1c2cc(I)c(=O)c(I)c-2oc2c(I)c([O-])c(I)cc12,train
O=c1[nH]c2cc(Cl)ccc2o1,train
NS(=O)(=O)c1cc(C2(O)NC(=O)c3ccccc32)ccc1Cl,train
CN(C)CC/C=C1\c2ccccc2Sc2ccc(Cl)cc21,test
CCC(=O)O[C@]1(C(=O)CCl)[C@@H](C)C[C@H]2[C@@H]3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
O=C([O-])c1cc(O)c2c3nc4ccccc4oc-3cc(=O)c2n1,train
CCC(C)(Oc1ccc(C2(c3ccc(OC(C)(CC)C(=O)O)cc3)CCCCC2)cc1)C(=O)O,train
C(=C\c1ccccc1)\c1ccccc1,train
Cc1cccc(C)c1NC(=O)CC12CCCN1CCC2,valid
CC(C)=CCC/C(C)=C/CC/C(C)=C/CC/C=C(\C)CC/C=C(\C)CCC=C(C)C,train
C[C@]12CC[C@H]3[C@@H](CC=C4C[C@@H](O)CC[C@@]43C)[C@@H]1CC[C@@H]2O,train
CC[N+](C)(C)c1cccc(O)c1,train
COC(=O)c1ccccc1O,train
Cc1ccc2ccc3cccc4ccc1c2c34,test
Nc1cc(-c2ccncc2)c[nH]c1=O,train
Cc1ccc2cccnc2c1,train
CC(C)(C#N)c1cc(Cn2cncn2)cc(C(C)(C)C#N)c1,train
CC(C)Cc1nccs1,train
Nc1cc(Cl)c(NC2=NCCN2)c(Cl)c1,valid
CCN(CC)CCNC(=O)c1ccc(NC(C)=O)cc1,train
O=C(Oc1ccccc1)c1ccccc1C(=O)Oc1ccccc1,train
COP(=O)(OC)OC(c1ccc(Cl)cc1)P(=O)(OC)OC,train
CC1(N)CCC(C(C)(C)N)CC1,train
Cn1nc(N)c2cn([C@@H]3O[C@H](CO)[C@@H](O)[C@H]3O)c3ncnc1c23,test
CCC(CC)(CC(=O)Nc1cccc(/C=C/c2nc(C3CCC3)cs2)c1)C(=O)O,train
Cc1cc2nc3c(=O)[nH]c(=O)nc-3n(C[C@H](O)[C@H](O)[C@H](O)CO)c2cc1C,train
OC(CN1CCC(Cc2ccc(F)cc2)CC1)c1ccc(Cl)cc1,train
Cc1c[nH]c2ccccc12,train
c1cc2c3c(cccc3c1)CC2,valid
CC/C(=N\OC/C=C/Cl)C1=C(O)CC(C2CCOCC2)CC1=O,train
CCOP(=O)(OCC)OC(=CCl)c1ccc(Cl)cc1Cl,train
NC(=O)CF,train
CC(C)(C)Cl,train
CC(C)O[Ti](OCCN(CCO)CCO)(OCCN(CCO)CCO)OC(C)C,test
CCCCCCCCCCCCSC,train
O=C1CCCN1CCO,train
CN(C)C=Nc1c(I)cc(I)c(CCC(=O)[O-])c1I,train
CC(=O)Nc1c(I)c(C(=O)[O-])c(I)c(N(C)C(C)=O)c1I,train
CCCCC(CC)COC(=O)OOC(C)(C)C,valid
CCCCNC1CC(C)(C)NC(C)(C)C1,train
CCCCC(CC)CO[PH](=O)OCC(CC)CCCC,train
O=C(C(=O)c1ccc(Br)cc1)c1ccc(Br)cc1,train
Clc1ccc(C(Cn2ccnc2)OCc2csc3c(Cl)cccc23)c(Cl)c1,train
CCn1cc(C(=O)O)c(=O)c2cc(F)c(N3CCNC(C)C3)c(F)c21,test
C[C@@H](O)[C@H]1C(=O)N2C(C(=O)O)=C(S[C@@H]3CN[C@H](C(=O)N(C)C)C3)[C@H](C)[C@H]12,train
O=C(NCC(O)CO)c1c(I)c(C(=O)NCC(O)CO)c(I)c(N(CCO)C(=O)CO)c1I,train
CC(=O)N(CC(O)CN(C(C)=O)c1c(I)c(C(=O)NCC(O)CO)c(I)c(C(=O)NCC(O)CO)c1I)c1c(I)c(C(=O)NCC(O)CO)c(I)c(C(=O)NCC(O)CO)c1I,train
O=C(O)COC(=O)Cc1ccccc1Nc1c(Cl)cccc1Cl,train
O=S(=O)(O)c1ccccc1O,valid
CCCCCCCC/C=C\CCCCCCCC(=O)OC[C@@H](O)[C@H]1OC[C@H](O)[C@H]1O,train
CCC1CCC(CCC(=O)O)C1,train
CC(=O)Oc1cc2c(s1)CCN(C(C(=O)C1CC1)c1ccccc1F)C2,train
Nc1ccccc1S(=O)(=O)c1ccccc1,train
CCOC(=O)[C@H](CCc1ccccc1)N[C@@H](C)C(=O)N1[C@H](C(=O)O)C[C@@H]2CCC[C@@H]21,test
O=C(c1ccc(OCCN2CCCCC2)cc1)c1c(-c2ccc(O)cc2)sc2cc(O)ccc12,train
CCOc1cc(CC(=O)N[C@@H](CC(C)C)c2ccccc2N2CCCCC2)ccc1C(=O)O,train
COC(=O)[C@H]1[C@H]2C[C@@H]3c4[nH]c5cc(OC)ccc5c4CCN3C[C@H]2C[C@@H](OC(=O)C=Cc2cc(OC)c(OC)c(OC)c2)[C@@H]1OC,train
NC(=O)c1ncn([C@@H]2O[C@H](CO)[C@@H](O)[C@H]2O)n1,train
CC1=C(/C=C/C(C)=C/C=C/C(C)=C/CO)C(C)(C)CCC1,valid
CCCCCCCC(=O)CC[C@H]1[C@H](O)C[C@H](O)[C@@H]1C/C=C\CCCC(=O)OC(C)C,train
CCN1CCCC1CNC(=O)c1cc(S(=O)(=O)CC)ccc1OC,train
CCCCOCCOCCOC(C)=O,train
O=C1CCCCO1,train
CCCCOC(=O)CC#N,test
COc1ccc(C(CN(C)C)C2(O)CCCCC2)cc1,train
CC(=O)O[C@H]1C[C@@H]2CC[C@@H]3[C@H](CC[C@@]4(C)[C@H]3C[C@H]([N+]3(C)CCCCC3)[C@@H]4OC(C)=O)[C@@]2(C)C[C@@H]1N1CCCCC1,train
CCCC(CCC)C(=O)O,train
O=C(Cc1ccccc1)c1ccccc1,train
CC(C)(C)NCCO,valid
C=CC(N)CCC(=O)O,train
CCN(CC)CCOC(=O)c1ccc(N)cc1,train
CC(=O)C1CCOC1=O,train
Nc1cc(N)cc(C(=O)O)c1,train
N#CC(Br)(Br)C(N)=O,test
N#Cc1c(Cl)cccc1Cl,train
c1ccc2occc2c1,train
Nc1nc(N)nc(-c2ccccc2)n1,train
O=C(O)c1ccccc1,train
O=C(c1ccccc1)C(O)c1ccccc1,valid
O=C1C=CC(=O)C=C1,train
c1ccc2sc(SSc3nc4ccccc4s3)nc2c1,train
c1ccc2[nH]nnc2c1,train
ClC(Cl)(Cl)c1ccccc1,train
NNC(=O)c1ccccc1,test
CS(=O)(=O)Nc1ccc([N+](=O)[O-])cc1Oc1ccccc1,train
CCCC/C=C/C=C\C=O,train
CC(C)=CCCC(C)CC(=O)O,train
CCc1nccnc1CC,train
COc1ccc(O)cc1,valid
c1ccc(Nc2cccc3ccccc23)cc1,train
N#Cc1ccncc1,train
C[C@@H](N)[C@@H](O)c1ccccc1,train
NCCCN1CCOCC1,train
CC(C)c1ccc2c(c1)CC[C@@H]1[C@]2(C)CCC[C@@]1(C)CN,test
O=[N+]([O-])c1cc[n+]([O-])cc1,train
CC1(C)CC(=O)CC(C)(C)N1,train
O=C(O)C(O)C(O)C(=O)O,train
Sc1ccccc1,train
O=c1c2ccccc2c(=O)c2c1cc(Cl)c1[nH]c3c([nH]c12)c(Cl)cc1c(=O)c2ccccc2c(=O)c13,valid
COc1ccc2nc(NC(=O)Nc3ccccc3)sc2c1,train
CCC(C(=O)OCCOCCN(CC)CC)c1ccccc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
O=C(N/N=C/c1ccccc1O)c1ccncc1,train
Cc1ccc(S(=O)(=O)NC(=O)NC2CCCCC2)cc1,train
CC(O)C(=O)O.CCOc1ccc2nc3cc(N)ccc3c(N)c2c1,test
NS(=O)(=O)c1ccc(NC(=O)CCC(=O)O)cc1,train
COc1ccc(S(=O)(=O)Nc2nnc(CC(C)C)s2)cc1,train
O=C(Nc1ncc([N+](=O)[O-])s1)c1cccs1,train
CC(C)(O)C(=O)O,train
CC[C@@H]1C[C@H](N(Cc2cc(C(F)(F)F)cc(C(F)(F)F)c2)C(C)=O)c2cc(C(F)(F)F)ccc2N1C(=O)OC(C)C,valid
CC(C)(O)c1ccc(CNC(=O)c2cccnc2Oc2ccc(F)cc2)cc1,train
CNCc1ccc(NC(=O)c2c[nH]c3c2C(=O)CCC3)cc1,train
CC(Cc1ccc(OCC(=O)O)cc1)NCC(O)c1csc(C(F)(F)F)n1,train
Cc1nccn1-c1ccc(Sc2cccc(C3(C(N)=O)CCOCC3)c2)cc1,train
Cc1cc(CCN2CCN(c3nsc4ccccc34)CC2)cc2c1NC(=O)CC2(C)C,test
O=c1[nH]c2ccc(S(=O)CCN3CCC(Cc4ccc(F)cc4)CC3)cc2o1,train
Nc1cc(Cl)c(O)c(Cl)c1,train
O=C1CCC(C2CCCCC2)CC1,train
OC1CCC(C2CCCCC2)CC1,train
O=C(Nc1ccccc1)c1cc([N+](=O)[O-])ccc1Cl,valid
O=C(O)c1ccc([Hg]Cl)cc1,train
Cc1c(C(=O)NCCO)[n+]([O-])c2ccccc2[n+]1[O-],train
OC(Cn1cncn1)(c1ccc(F)cc1)c1ccccc1F,train
CCCCOP(=O)(O)OCCCC,train
NC(=O)c1ccc([N+](=O)[O-])cc1,test
CCCO[Si](OCCC)(OCCC)OCCC,train
CCCCC[C@H](O)/C=C/[C@H]1[C@H](O)CC(=O)[C@@H]1CCCCCCC(=O)O,train
CCCCCCCCCCCC[n+]1ccc2ccccc2c1,train
CCCC(=O)Cl,train
CCCCCCCCCl,valid
Oc1ccccc1Cl,train
CC(Cl)C(=O)O,train
O=C(O)CCCl,train
O=[Cd],train
COc1ccc(OC)c(C(O)CNC(=O)CN)c1,test
CSc1cnccn1,train
C=CC(C)(CCC=C(C)C)OC(=O)C(C)C,train
CCCCCCCCCCOC(=O)c1ccccc1C(=O)OCCCCCCCC,train
CC(C)OC=O,train
N#Cc1cccc(C#N)c1,valid
CCOC(=O)/C=C/C(=O)OCC,train
CCC(C)COC(C)=O,train
c1ccc(Cc2ccccc2)cc1,train
C[C@H]1CNCCc2ccc(Cl)cc21,train
Cc1nnc(C(=O)NC(C)(C)c2nc(C(=O)NCc3ccc(F)cc3)c(O)c(=O)n2C)o1,test
O=S(=O)(c1ccccc1)c1ccccc1,train
CCC(C)OC(=O)c1ccc(O)cc1,train
Cc1ccc(NC(=O)N(C)C)cc1NC(=O)N(C)C,train
[O-][n+]1ccccc1SSc1cccc[n+]1[O-],train
C=COCC1CCC(COC=C)CC1,valid
N#Cc1c(F)cccc1F,train
S=C=S,train
Nc1c(NC/C=C\COc2cc(CN3CCCCC3)ccn2)c(=O)c1=O,train
CN1[C@H]2CC[C@@H]1C[C@H](NC(=O)n1c(=O)[nH]c3ccccc31)C2,train
C1CCc2nnnn2CC1,test
CC(C(=O)O)c1ccc(/C=C2\CCCCC2=O)cc1,train
CCCCCOc1ccc(C2SC(=O)NC2=O)cc1OCC,train
CC[N+](CC)(CC)CCOC(=O)C(C1CCCC1)C1CCCC1,train
CCCCCCCCCCCCCCCCCCNC(=O)OC1CCN(C(=O)OC[C@H](COC(=O)N(Cc2cccc[n+]2CC)C(=O)c2ccccc2OC)OC)CC1,train
CCOC(=O)c1ccc(OCCN2C[C@H](O)[C@@H](O)[C@H](O)[C@H]2CO)cc1,valid
CCC(C)N(C)C(=O)c1cc2ccccc2c(-c2ccccc2Cl)n1,train
Cc1ccc(/C=N/n2c(-c3ccccc3)csc2=S)cc1,train
CN(CCCN1c2ccccc2CCc2ccccc21)CC(=O)c1ccc(Cl)cc1,train
CN(C)CCOC(c1ccc(Cl)cc1)c1ccccn1,train
NC[C@@H]1O[C@H](O[C@@H]2[C@@H](CO)O[C@@H](O[C@@H]3[C@@H](O)[C@H](N)C[C@H](N)[C@H]3O[C@H]3O[C@H](CO)[C@@H](O)[C@H](O)[C@H]3N)[C@@H]2O)[C@H](N)[C@@H](O)[C@@H]1O,test
Clc1ccc(C(Cl)(Cl)Cl)cn1,train
c1ccc(P(c2ccccc2)c2ccccc2)cc1,train
c1ccc(OP(Oc2ccccc2)Oc2ccccc2)cc1,train
CC(CCl)OP(=O)(OC(C)CCl)OC(C)CCl,train
ClCCOP(OCCCl)OCCCl,valid
CCOC(=O)OCC,train
CCN(C(=O)N(CC)c1ccccc1)c1ccccc1,train
C=CC(=O)OCCOCCOC(=O)C=C,train
CCOCCOCCOCC,train
CCOP(=S)(OCC)SCCSCC,test
COCCOCCO,train
C1CN2CCN1CC2,train
COc1ccc(OC)cc1,train
CCCCCCCC/C=C\CCCCCCCCO,train
CC(C)OC(=O)C(C)(C)Oc1ccc(C(=O)c2ccc(Cl)cc2)cc1,valid
ClC(Cl)(Cl)Br,train
CC(C)(C)NC[C@H](O)COc1cccc2c1CCCC2=O,train
CC(C(=O)O)c1cccc(C(=O)c2ccccc2)c1,train
C[C@H](CCC(=O)O)[C@H]1CC[C@H]2[C@@H]3CC[C@@H]4C[C@H](O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
C=C(C)[C@H]1CC=C(C)CC1,test
Nc1nc(=O)[nH]cc1F,train
O=C(O)Cc1sc(-c2ccccc2)nc1-c1ccc(Cl)cc1,train
CC(C)=CCC1C(=O)N(c2ccccc2)N(c2ccccc2)C1=O,train
O=CC1CC=CCC1,train
CS(=O)(=O)c1ccc(C(=O)C2C(=O)CCCC2=O)c([N+](=O)[O-])c1,valid
CC1=C(/C=C/C(C)=C/C=C\C(C)=C\C(=O)O)C(C)(C)CCC1,train
SCc1ccccc1,train
N#Cc1cccnc1,train
O=S(=O)([O-])C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
CCC1(CC)C(=O)C=CNC1=O,test
O=C(OCC(COC(=O)c1ccccc1)(COC(=O)c1ccccc1)COC(=O)c1ccccc1)c1ccccc1,train
COc1c2occc2c(OC)c2c(=O)cc(C)oc12,train
Nc1ccc(S(=O)(=O)Nc2ccc(Cl)nn2)cc1,train
O=C(O)CO,train
O=CC=O,valid
CC1CCC(C(C)C)C(O)C1,train
CN(C)CCOC(C)(c1ccccc1)c1ccccn1.O=C(O)CCC(=O)O,train
Sc1nc2ccccc2s1,train
NCCc1ccc(O)c(O)c1,train
O=c1oc2c(O)c(O)cc3c(=O)oc4c(O)c(O)cc1c4c23,test
Cc1cnc(NC(=O)C2=C(O)c3ccccc3S(=O)(=O)N2C)s1,train
Nc1nc(N)nc(N)n1,train
CC[C@H]1CN2CCc3cc(OC)c(OC)cc3[C@@H]2C[C@@H]1C[C@H]1NCCc2cc(OC)c(OC)cc21,train
O=S(=O)([O-])CCS,train
CCn1cc[n+](C)c1C.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,valid
CCCCCCCCCCCCSCCOCCO,train
N=C(N)n1cccn1,train
FC(F)(F)COCC(F)(F)F,train
Cc1c(I)c(=O)n(-c2ccccc2)n1C,train
CCC(C(N)=O)N1CCCC1=O,test
CC(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)O,train
CC(=O)c1ccc2ccc3ccccc3c2c1,train
Nc1ccc(NCCO)c([N+](=O)[O-])c1,train
Cc1cc(C)c(S(=O)(=O)[O-])cc1N=Nc1cc(S(=O)(=O)[O-])c2ccccc2c1O,train
COc1ccccc1N1CCN(CCCCNC(=O)c2ccc(-c3ccc(C(C)=O)cc3)cc2)CC1,valid
[Be+2],train
COCCOCCOCCOCCO,train
Cc1cccc(Oc2ccccc2)c1,train
CCCCCCCCCCCCCCCCCCCl,train
O=C(CC(C(=O)OC1CCCCC1)S(=O)(=O)[O-])OC1CCCCC1,test
CC/C(C)=N/O[Si](C)(O/N=C(\C)CC)O/N=C(\C)CC,train
CC(C)(C)C(=O)C(Cl)Cl,train
CCCOc1nn(C(=O)[N-]S(=O)(=O)c2ccccc2C(=O)OC)c(=O)n1C,train
CCCCCCC(CCCCCCCCCCC(=O)OCC(COC(C)=O)OC(C)=O)OC(C)=O,train
CC(C)CCCCCCOC(=O)C1CCCCC1C(=O)OCCCCCCC(C)C,valid
c1ccc2cnccc2c1,train
C=CC(C)(CCC=C(C)C)OC=O,train
Cc1nc(-c2ccccn2)ncc1C(=O)Nn1cc(C)c2cc(F)ccc21,train
CCCCCCCCCCCCOC(C)=O,train
CC1C(C)(C)C2=C(C(=O)CCC2)C1(C)C,test
CC(=O)OCCCc1ccccc1,train
COc1ccc(COC=O)cc1,train
Oc1ccc(Cl)c2c1CCC2,train
CC(C)=CCC/C(C)=C/CC/C(C)=C/CO,train
O=C(NC(=O)c1c(F)cccc1F)Nc1cc(Cl)c(Oc2ncc(C(F)(F)F)cc2Cl)c(Cl)c1,valid
CC(C)OC(=O)C(O)(c1ccc(Cl)cc1)c1ccc(Cl)cc1,train
OC[C@H](O)[C@@H](O)[C@H](O[C@@H]1O[C@H](CO)[C@H](O)[C@H](O)[C@H]1O)[C@H](O)CO,train
FC1(F)C(F)(F)C(F)(F)C2(F)C(F)(C1(F)F)C(F)(F)C(F)(F)C1(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C12F,train
CC1(C)O[C@@H]2CO[C@@]3(COS(N)(=O)=O)OC(C)(C)O[C@H]3[C@@H]2O1,train
CC(C)CC(C)CC(C)CC(C)COC(=O)c1ccccc1C(=O)OCC(C)CC(C)CC(C)CC(C)C,test
F[B-](F)(F)F,train
CC(C)(C)NCC(O)c1cc(Cl)c(N)c(Cl)c1,train
C=C(Nc1ccc(OCC)cc1)Nc1ccc(OCC)cc1,train
C[N+]12CCC(CC1)C(OC(=O)C(O)(c1ccccc1)c1ccccc1)C2,train
Oc1c(I)cc(Cl)c2cccnc12,valid
CC(C)/N=c1/cc2n(-c3ccc(Cl)cc3)c3ccccc3nc-2cc1Nc1ccc(Cl)cc1,train
C=CCOC(=O)CCCCCC,train
O=C(Nc1cccc(C(F)(F)F)c1)c1cc(Br)cc(Br)c1O,train
COc1cc(C(=O)NCCCCCC(=O)O)cc(OC)c1OC,train
O=S(=O)(Cl)Cl,test
C=C(Cl)CSC(=S)N(CC)CC,train
OC[C@H]1O[C@H](O[C@]2(CO)O[C@H](CO)[C@@H](O)[C@@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,train
O=S(=O)([O-])[S-],train
CC(=O)NS(=O)(=O)c1ccc(N)cc1,train
C=C1C(C)(C)[C@@]2(Cl)C(Cl)[C@]1(Cl)C(Cl)(Cl)C2(Cl)Cl,valid
CN(N=O)C(=O)N[C@H]1[C@@H](O)O[C@H](CO)[C@@H](O)[C@@H]1O,train
O=C1CCC(=O)O1,train
c1ccc(C2CO2)cc1,train
Nc1ccc(S(=O)(=O)Nc2nccs2)cc1,train
C=Cc1ccccc1,test
Cc1cc(=O)oc2cc(O)ccc12,train
C=CC(C)=O,train
CC(CCc1ccc(O)cc1)NCCc1ccc(O)c(O)c1,train
CCCCC(CC)COC(=O)CC(C(=O)OCC(CC)CCCC)S(=O)(=O)[O-],train
CCCCCCOc1ccccc1C(N)=O,valid
CC(=O)Nc1ccc(S(=O)(=O)Nc2ccc([N+](=O)[O-])cc2)cc1,train
CC[C@@H](C(=O)[C@@H](C)[C@@H](O)[C@H](C)CCc1ccc(C)c(O)c1C(=O)[O-])[C@H]1O[C@](CC)([C@H]2CC[C@](O)(CC)[C@H](C)O2)C[C@@H]1C,train
O=C1C(O)=C(O)C(=O)C(O)=C1O,train
CC(N)CCCC(C)(C)O,train
CCc1cn([C@H]2C[C@H](O)[C@@H](CO)O2)c(=O)[nH]c1=O,test
COc1ccc(-c2cc(=O)c3c(O)cc(O[C@@H]4O[C@H](CO[C@@H]5O[C@@H](C)[C@H](O)[C@@H](O)[C@H]5O)[C@@H](O)[C@H](O)[C@H]4O)cc3o2)cc1O,train
CCc1cccc(O)c1,train
O=C(c1ccccc1)c1ccc(O)cc1,train
Oc1ccc(C(=C(Cl)Cl)c2ccc(O)cc2)cc1,train
O=C(O)/C=C/c1ccccc1,valid
O=C(NCCN1CCOCC1)c1ccc(Cl)cc1,train
CCCCCCCCCCCCOCCCN,train
CCc1cccc(CC)c1NC(=O)CN(CC(=O)O)CC(=O)O,train
COc1ccc(C(=O)CCC(=O)O)c2ccccc12,train
O=C(OCCO)c1ccccc1O,test
O=C([O-])CC(S[Au])C(=O)[O-],train
CCC(=O)O[C@H]1CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@]12C,train
O=C(O)c1cc2ccccc2c(Cc2c(O)c(C(=O)O)cc3ccccc23)c1O,train
CCOc1ccc(C[C@H]2NC(=O)CCSSC[C@@H](C(=O)N3CCC[C@H]3C(=O)N[C@@H](CCCN)C(=O)NCC(N)=O)NC(=O)[C@H](CC(N)=O)NC(=O)[C@H]([C@@H](C)O)NC(=O)[C@H]([C@@H](C)CC)NC2=O)cc1,train
CCCC1([C@H](O)C/C=C/[C@H]2[C@H](O)CC(=O)[C@@H]2CCCCCCC(=O)OC)CCC1,valid
CSCC[C@H](NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)CCNC(=O)OC(C)(C)C)C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](Cc1ccccc1)C(N)=O,train
C1CNCCN1.C[C@H](CCC(=O)O)[C@H]1CC[C@H]2[C@H]3[C@H](C[C@H](O)[C@@]21C)[C@@]1(C)CC[C@@H](O)C[C@H]1C[C@H]3O,train
CN1[C@H]2CC[C@@H]1C[C@H](OC(c1ccccc1)c1ccccc1)C2,train
CC(C)(C)c1ccc(CN2CCN(C(c3ccccc3)c3ccc(Cl)cc3)CC2)cc1,train
OC(CCN1CCCCC1)(c1ccccc1)c1ccccc1,test
O=C(O)CCc1nc2ccccc2[nH]1,train
CN1CCN(c2c(F)cc3c(=O)c(C(=O)O)cn(CCF)c3c2F)CC1,train
CC(C)=O,train
NC(=O)[C@@H]1CCCN1C(=O)[C@H](Cc1cnc[nH]1)NC(=O)[C@@H]1CCC(=O)N1,train
CC(O)Cn1cnc2c1c(=O)n(C)c(=O)n2C,valid
O=NN(CCO)CCO,train
CCCCN(CCCC)N=O,train
C#CC1(O)CCCCC1,train
Nc1cccc2c(O)nnc(O)c12,train
OCC(F)(F)F,test
CC(=O)CN(CC(C)=O)N=O,train
CC(=O)C(C)(C)C,train
CN(C)S(=O)(=O)N(SC(F)(Cl)Cl)c1ccccc1,train
CC(C)CO,train
CCCCOCCOP(=O)(OCCOCCCC)OCCOCCCC,valid
Nc1nc(CC(=O)O)cs1,train
Nc1nccs1,train
CN(C)CCCCl,train
Oc1cc(Cl)c(Cl)c(Cl)c1Cl,train
COc1cc(C[C@H](C)N)c(OC)cc1C,test
Cc1cc(OCCOCC[N+](C)(C)Cc2ccccc2)ccc1C(C)(C)CC(C)(C)C,train
[N-]=[N+]=C1C=Cc2c(cccc2S(=O)(=O)[O-])C1=O,train
Nc1cc(C(=O)O)ccc1Cl,train
COc1ccccc1OCC(O)COC(N)=O,train
CC(=O)N=c1sc(S(N)(=O)=O)nn1C,valid
CCC1(CC)C(=O)NC(=O)N(C)C1=O,train
CC[C@@H](CO)NC(=O)[C@@H]1C=C2c3cccc4[nH]cc(c34)C[C@H]2N(C)C1,train
CN1C(C)(C)CC(O)CC1(C)C,train
CCCCOCCOC(=O)COc1ccc(Cl)cc1Cl,train
CCCC/C=C\CCCCCCCCCC=O,test
CCCC/C=C/CCCCO,train
CCC1(CC)C(=O)N=C([O-])NC1=O,train
O=C1CC(=O)NC(=O)N1,train
COP(=S)(OC)SCn1nnc2ccccc2c1=O,train
c1ccc(N=Nc2ccccc2)cc1,valid
CC1=C(C(=O)OC(C)C)C(c2cccc([N+](=O)[O-])c2)C(C(=O)OC2CN(C(c3ccccc3)c3ccccc3)C2)=C(N)N1,train
[N-]=[N+]=[N-],train
Cc1cn([C@H]2C[C@H](N=[N+]=[N-])[C@@H](CO)O2)c(=O)[nH]c1=O,train
C/C(=C\c1ccccc1)[N+](=O)[O-],train
CCCCN(CCO)CCO,test
N#CCc1ccc(Cl)cc1Cl,train
C=C1CC[C@H]2C[C@@H]1C2(C)C,train
CCOC(=O)Cc1ccccc1,train
Cc1cc(O)c(C(C)C)cc1Cl,train
O=c1[nH]c2ccc(Cl)cc2o1,valid
CC1CC(=O)NN=C1c1ccc(NCC(C)(C)NCC(O)COc2cc(Cl)ccc2C#N)cc1,train
CC(C)CCC(=O)CCC(C)C,train
O=C(O)COc1ccccc1,train
O=C1CCN(c2ccccc2)N1,train
CC(C)=CCC1=C(O)C(=O)c2ccccc2C1=O,test
C#CCOc1ccc2c(C)cc(=O)oc2c1,train
Cc1cc(C)c(NC(=O)CN(CC(=O)O)CC(=O)O)c(C)c1Br,train
CC/C=C\CCOC(=O)c1ccccc1O,train
CC(C)(O)CCC(C)(C)O,train
CCCCCCCCCCCCCC(=O)OC(C)C,valid
CC(=O)OC1CCCCC1C(C)(C)C,train
Cc1oncc1C(=O)Nc1ccc(C(F)(F)F)cc1,train
Cc1c(OCC(F)(F)F)ccnc1CS(=O)c1nc2ccccc2[nH]1,train
CC(C)CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CC[C@@H]4C[C@H](O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
C=C[C@]1(C)C[C@@H](OC(=O)CSCCN(CC)CC)[C@]2(C)C(C)CC[C@]3(CCC(=O)[C@H]32)[C@@H](C)[C@@H]1O,test
CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)O)=C(CSC(=O)c3ccco3)CS[C@H]12)c1csc(N)n1,train
NC(=O)N[C@@H](CCC(=O)O)C(=O)O,train
O=C1CC[C@@H](C(=O)N[C@H]2C[C@@H]2c2ccccc2)N1,train
CC(=O)Nc1ccc(OC(C)=O)cc1,train
CC1CCCCN1CCCOC(=O)c1ccccc1,valid
O=C1C(=Cc2cccs2)CCCC1=Cc1cccs1,train
C(=Cc1ccncc1)c1ccccc1,train
c1ccc2c(c1)Sc1ccccc1N2CCN1CCCC1,train
CNC(=O)Oc1ccccc1C(=O)Nc1ccccc1,train
CCOC(=O)c1cc(-c2ccccc2)nc2ccc(C)cc12,test
OCC1(CO)COC(C(Cl)(Cl)Cl)OC1,train
CCCCCCCCCCCC(=O)N(C)CC(=O)O,train
ON=CC=NO,train
CC(Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1)C(=O)O,train
CC(C)c1cccc(C(C)C)c1NC(=O)[N-]S(=O)(=O)c1cc(C(C)(C)O)co1,valid
CCOc1ccc2[nH]cc(C(=O)NCc3ccccc3)c(=O)c2n1,train
c1ccc(C(O[C@@H]2CCCN(CCc3ccc4c(c3)OCO4)C2)c2ccccc2)cc1,train
COc1ccc(OC(F)(F)F)cc1CN[C@H]1CCCN[C@H]1c1ccccc1,train
CCc1nn(C2CCCCC2)c2cc([C@]3(C#N)CC[C@@H](C(=O)O)CC3)ccc12,train
O=C(N[C@@H](Cc1ccccc1)[C@@H](O)C(=O)N1C[C@H](O)[C@H](O)C1)c1cc2cc(Cl)ccc2[nH]1,test
Clc1cc(Cl)c2c(c1)N=C1CCCCCN1C2,train
CN(C(=O)Cc1cccc2occc12)[C@H]1CC[C@@]2(CCCO2)C[C@@H]1N1CCCC1,train
CCCCCCCCCCCCCCCCn1cc[n+](C)c1,train
CCCCCc1ccc(N)cc1,train
O=C(O)c1cc(-c2ccccc2)nc2ccccc12,valid
Cc1ccc(Cl)cc1Cl,train
CC(C)(C)c1ccc(C(=O)O)cc1,train
CO[Si](OC)(OC)c1ccccc1,train
CCOC(=O)CC(O)(CC(=O)OCC)C(=O)OCC,train
CCCCCCc1ccc(N)cc1,test
O=C(O)C1C2CCC(O2)C1C(=O)O,train
CCOP(=S)(OCC)SCSP(=S)(OCC)OCC,train
CCOCCO,train
O=P(O)(O)CCCl,train
O=P(OCCCl)(OCCCl)OCCCl,valid
CCCCC(CC)COP(=O)(OCC(CC)CCCC)OCC(CC)CCCC,train
O=Cc1ccc(CO)o1,train
Oc1cc(O)c(Cl)cc1Cl,train
NC(=O)C(Cl)Cl,train
CC(Cl)C(C)Cl,test
C[Si](C)(CCCN)O[Si](C)(C)CCCN,train
COC(=O)c1ccc(C(=O)OC)c(C(=O)OC)c1,train
C=CC(=O)OCCCCO,train
CCOC(=O)/C=C(/C)O[AlH3](OC(C)CC)OC(C)CC,train
O=CN(CCO)CCO,valid
C=CC(=O)OCCCCCCCC,train
On1nnc2ccccc21,train
O=c1n(CCCN2CCN(c3cccc(Cl)c3)CC2)nc2ccccn12,train
O=C(COCCOCCOCC(=O)Nc1c(I)cc(I)c(C(=O)O)c1I)Nc1c(I)cc(I)c(C(=O)O)c1I,train
O=C(O)Cc1cccc(-c2cc(CCCCCCc3ccc(O[C@H]4O[C@H](CO)[C@@H](O)[C@H](O)[C@@H]4O)c(-c4cccc(CC(=O)O)c4)c3)ccc2O[C@H]2O[C@H](CO)[C@@H](O)[C@H](O)[C@@H]2O)c1,test
Cc1ccccc1C(=O)Nc1ccc(C(=O)N2CCCC(O)c3cc(Cl)ccc32)c(C)c1,train
C[N+]1(CC2COC(c3ccccc3)(c3ccccc3)O2)CCCCC1,train
O=C(CCCCCCCCC#CI)Oc1ccccc1,train
O=P(O)(O)C(O)(Cc1cnc2ccccn12)P(=O)(O)O,train
O=C(CCC1CCN(Cc2ccccc2)CC1)c1ccc2c(c1)NCCCC2,valid
CCCCNc1ccc(C(=O)OCC[NH+](CC)CC)cc1,train
CCOC(=O)c1ncn2c1CN(C)C(=O)c1c(I)cccc1-2,train
Cc1cccc(C)c1N(CCCc1cccnc1)C(=O)C(C)N,train
CCc1ccc(C(=O)C(C)CN2CCCCC2)cc1,train
[2H]C([2H])([2H])[C@H](N)Cc1ccccc1.[2H]C([2H])([2H])[C@H](N)Cc1ccccc1,test
NC1CCN(c2nc3c(cc2F)c(=O)c(C(=O)O)cn3-c2ccc(F)cc2F)C1,train
CCOC(=O)C1(c2ccccc2)CCN(CCC(C#N)(c2ccccc2)c2ccccc2)CC1,train
C#CCN[C@@H]1CCc2ccccc21,train
Nc1ccn([C@@H]2O[C@H](CO)[C@@H](O)C2(F)F)c(=O)n1,train
Cc1ncc2n1-c1ccc(Cl)cc1C(c1ccccc1F)=NC2,valid
C=CCN1CC[C@]23c4c5ccc(O)c4O[C@H]2[C@@H](O)C=C[C@H]3[C@H]1C5,train
O=C(OCCN1CCCCC1)C(O)(c1ccccc1)c1ccccc1,train
CC(=O)c1ccc2c(c1)N(CCCN1CCN(CCO)CC1)c1ccccc1S2,train
CN(C)C,train
CCCC(=O)c1cc(O)c(O)cc1O,test
Oc1cccc2cc3cccc(O)c3c(O)c12,train
CC=C(Cl)Cl,train
O=C(O)c1ccc2c(c1)C(=O)OC2=O,train
C1=CC2C3C=CC(C3)C2C1,train
O=C(OC1CCCCC1)c1ccccc1C(=O)OC1CCCCC1,valid
CC1(C)[C@H]2CC[C@]1(C)[C@H](O)C2,train
C=CC(N)=O,train
O=C(O)c1cc(Oc2ccc(C(F)(F)F)cc2Cl)ccc1[N+](=O)[O-],train
C=CC=O,train
CC(=O)Nc1ccc(CC(=O)O)cc1,test
CC(=O)N[C@@H](CS)C(=O)O,train
C=CC#N,train
c1ccc(NNc2ccccc2)cc1,train
NS(=O)(=O)c1cc2c(cc1Cl)NCNS2(=O)=O,train
C[C@]12C[C@H](O)[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)CO,valid
Oc1ccc(OCc2ccccc2)cc1,train
Oc1ccc(O)cc1,train
[Zn+2],train
CC[C@@H]1C=C(C)C[C@H](C)C[C@H](OC)[C@H]2O[C@@](O)(C(=O)C(=O)N3CCCC[C@H]3C(=O)O[C@H](/C(C)=C/[C@@H]3CC[C@@H](Cl)[C@H](OC)C3)[C@H](C)[C@@H](O)CC1=O)[C@H](C)C[C@@H]2OC,train
CC(C)(C)C(O)(CCc1ccc(Cl)cc1)Cn1cncn1,test
O=C(O)c1cccnc1Nc1cccc(C(F)(F)F)c1,train
O=[N+]([O-])/N=C1\NCCN1Cc1ccc(Cl)nc1,train
OC(c1ccccc1)(c1ccccc1)C1CCNCC1,train
O=C(O)CCC(=O)Nc1ccc(S(=O)(=O)Nc2nccs2)cc1,train
C=CCOC(=O)/C=C\C(=O)OCC=C,valid
Nc1ccc(S(=O)(=O)NC(=O)c2ccccc2)cc1,train
CC(C)c1cccc(C(C)C)c1,train
O=C(Cl)c1cccc(C(=O)Cl)c1,train
O=C(O)c1ccc(O)cc1,train
Cc1ccc(C(C)C)cc1,test
O=C(O)c1cc(-c2ccc(F)cc2F)ccc1O,train
C[SiH](C)O[Si](C)(C)O[Si](C)(C)O[SiH](C)C,train
COC(C(=O)O)c1ccccc1,train
O=C(O)C(Br)Br,train
COc1ccc(C(c2ccc(OC)cc2)C(Cl)(Cl)Cl)cc1,valid
COc1cccc(O)c1O,train
COc1cc2c(cc1N)oc1ccccc12,train
CN(Cc1cnc2nc(N)nc(N)c2n1)c1ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc1,train
Cn1cc[nH]c1=S,train
CC(=O)Oc1ccccc1C(=O)Nc1ncc([N+](=O)[O-])s1,test
CC(=O)Nc1c(I)cc(I)c(C(=O)O)c1I,train
CCn1cc(C(=O)O)c(=O)c2cc(F)c(N3CCNCC3)cc21,train
CC=CC=O,train
CC(C)[C@@H](C)/C=C/[C@@H](C)[C@H]1CC[C@H]2/C(=C/C=C3\C[C@@H](O)CC[C@@H]3C)CCC[C@@]21C,train
CN(C)c1ccc(C2(c3ccc(N(C)C)cc3)OC(=O)c3cc(N(C)C)ccc32)cc1,valid
Cc1ccc(OP(=O)(Oc2ccccc2)Oc2ccccc2)cc1,train
Cc1ccccc1OCC1CO1,train
CC(C)(OO)c1ccccc1,train
CCN(CC)CCN1C(=O)CN=C(c2ccccc2F)c2cc(Cl)ccc21,train
CC(CN1c2ccccc2Sc2ccc(S(=O)(=O)N(C)C)cc21)N(C)C,test
O=C([O-])P(=O)([O-])[O-],train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](N)c3ccccc3)C(=O)N2[C@H]1C(=O)O,train
CCCCO[AlH3](OCCCC)OCCCC,train
C=CS(=O)(=O)[O-],train
CN(C)CCOCCN(C)C,valid
C1=CCC2CC=CC2C1,train
O=C(Oc1cccc2cccnc12)c1ccccc1,train
CC1(C)CC(O)CC(C)(C)N1[O],train
CC(C)(C#N)Nc1nc(Cl)nc(NC2CC2)n1,train
CC(C)CN(C[C@@H](O)[C@H](Cc1ccccc1)NC(=O)O[C@@H]1CCOC1)S(=O)(=O)c1ccc(N)cc1,test
CCOc1ccc(C(=O)OCCN(CC)CC)cc1,train
COC(=O)[C@H]1[C@H](OC(=O)c2ccccc2)C[C@@H]2CC[C@H]1N2C,train
CCCOC(=O)NCCCN(C)C,train
C[C@]12C[C@H](O)[C@H]3[C@@H](C[C@H](F)C4=CC(=O)C=C[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)CO,train
Nc1ccc(C(=O)[O-])c(O)c1,valid
C1CCC(SSC2CCCCC2)CC1,train
CCCCCCCCCC(C)C=O,train
CC(=O)c1nccs1,train
C=CCCCCCCCCC(=O)OCCCC,train
O=C1CCCCCCCCCCCCCCCO1,test
CCCCCCOC(=O)CCCCCCCC(=O)OCCCCCC,train
CCCCCCCCCCCCCCCC[N+](C)(C)CC,train
C[C@]12C(=O)OC(=O)[C@@]1(C)C1CCC2O1,train
CCNC(=O)C(C)OC(=O)Nc1ccccc1,train
C[C@@H]1OC(=O)C[C@H](O)C[C@H](O)CC[C@@H](O)[C@H](O)C[C@H](O)C[C@]2(O)C[C@H](O)[C@@H](C(=O)O)[C@H](C[C@@H](O[C@@H]3O[C@H](C)[C@@H](O)[C@H](N)[C@@H]3O)C=CC=CC=CC=CC=CC=CC=C[C@H](C)[C@@H](O)[C@H]1C)O2,valid
Cc1ncc(C[n+]2csc(CCOP(=O)(O)O)c2C)c(N)n1,train
Cc1c(C)c2c(c(C)c1OC(=O)c1cccnc1)CC[C@@](C)(CCC[C@H](C)CCC[C@H](C)CCCC(C)C)O2,train
Cc1ccc(S(=O)(=O)NC(=O)NC2CCCC2)cc1,train
Cc1ccc(S(=O)(=O)NC(=O)NC2CCCCCCC2)cc1,train
NC(=S)N/N=C/c1cccnc1,test
CC(=O)Nc1ncc([N+](=O)[O-])s1,train
Cc1cc(NS(=O)(=O)c2ccc(N)cc2)nc(C)n1,train
OC1CCCC1,train
CC(=O)OCC(=O)[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3C(=O)C[C@@]21C,train
CN1CCN(C2=Nc3ccccc3Sc3ccc(Cl)cc32)CC1,valid
C[C@]12CC(=O)[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)CO,train
CN1CCN(C2=Nc3cc(Cl)ccc3Nc3ccccc32)CC1,train
O=C(O)c1cc(O)c2c(c1)C(=O)c1cccc(O)c1C2=O,train
O=c1nc([O-])n(Cl)c(=O)n1Cl,train
CCCCN(CCCC)c1ccc(C(=O)c2ccccc2C(=O)O)c(O)c1,test
CC(C(=O)O)c1ccc2c(c1)[nH]c1ccc(Cl)cc12,train
COC(=O)c1ccc(C=O)cc1,train
Cc1ccco1,train
CC(C#N)CCC#N,train
CC(=O)N[C@@H](CC(C)C)C(=O)O,valid
Cc1c[nH]cn1,train
CCCCCC(O)/C=C/C=O,train
Cn1c(=O)c2c(ncn2C)n(C)c1=O.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
CC(CCc1ccccc1)NCC(O)c1ccc(O)c(C(N)=O)c1,train
Cn1c([N+](=O)[O-])cnc1COC(N)=O,test
OC(c1ccc(F)cc1)(c1cncnc1)c1ccccc1Cl,train
c1cc(OCC2CO2)ccc1Cc1ccc(OCC2CO2)cc1,train
Oc1ccccc1[Hg]Cl,train
C[C@@H]1O[C@@H]1P(=O)([O-])[O-],train
COc1c(N2CCNC(C)C2)c(F)cc2c(=O)c(C(=O)O)cn(C3CC3)c12,valid
COC(=O)c1cncn1C(C)c1ccccc1,train
COc1cc(NCCCC(C)N)c2ncccc2c1,train
N[C@H]1[C@@H]2CN(c3nc4c(cc3F)c(=O)c(C(=O)O)cn4-c3ccc(F)cc3F)C[C@H]12,train
c1ccc(-c2nc[nH]c2-c2ccccc2)cc1,train
NC(=O)N1c2ccccc2CC(=O)c2ccccc21,test
CCN(CC)CC(=O)Nc1c(C)cc(C)cc1C,train
CC(=O)NCCCCCC(=O)O,train
O=C(O)c1cc(=O)c2ccccc2o1,train
CN1CCN(c2c(F)cc3c(=O)c(C(=O)O)cn4c3c2SCC4)CC1,train
CNC(=O)OCc1cccc(COC(=O)NC)n1,valid
O=C(O)CCCCCCCCCCCBr,train
C=C1C(=O)O[C@H]2[C@H]1CCC(C)=CCC[C@@]1(C)O[C@@H]21,train
C=C1/C(=C\C=C2/CCC[C@@]3(C)[C@H]2CC[C@@H]3[C@H](C)/C=C/[C@H](C)C(C)C)C[C@@H](O)C[C@@H]1O,train
CCCN(CC)CC1COC2(CCC(C(C)(C)C)CC2)O1,train
CCCN(CCC)c1c([N+](=O)[O-])cc(C(F)(F)F)c(N)c1[N+](=O)[O-],test
COc1cccc2c1[C@@H]1CN(CCCCn3c(=O)[nH]c4c(sc5ncc(-c6ccccc6)nc54)c3=O)C[C@@H]1CO2,train
C[C@@H]1C[C@H]2C3C[C@H](F)C4=CC(=O)C=CC4(C)[C@@]3(F)[C@@H](O)CC2(C)[C@@]1(O)C(=O)COC(=O)C(C)(C)C,train
COc1ccc(C(=O)Nc2ccccc2CCC2CCCCN2C)cc1,train
CC(C)C(=O)c1c(C(C)C)nn2ccccc12,train
CCNC(=O)[C@@H]1CCCN1C(=O)[C@H](CCCNC(=N)N)NC(=O)[C@H](CC(C)C)NC(=O)[C@@H](CC(C)C)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@H](CO)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)[C@H](Cc1cnc[nH]1)NC(=O)[C@@H]1CCC(=O)N1,valid
CCCCCCCCCCCCCCCCSCC(COC)COP(=O)([O-])OCC[N+](C)(C)C,train
C[C@@H]1[C@@H]2Cc3ccc(O)cc3[C@@]1(C)CCN2CC1CC1,train
CCN(CC)CCn1c(=O)c(Cc2ccc(OC)cc2)nc2ccccc21,train
CCCCOCC1CO1,train
CC(C)(C)OC=O,test
CCCCn1cc[n+](C)c1C.O=S(=O)([O-])C(F)(F)F,train
CC(C)(C)OOC(=O)c1ccccc1,train
O=C1[N-]C(=O)c2ccccc21,train
C=C[Si](OCCOC)(OCCOC)OCCOC,train
CCCCCCCCCCCl,valid
CC(C)[N+](C)(CCOC(=O)C1c2ccccc2Oc2ccccc21)C(C)C,train
CCCN(CCC)C(=O)C(CCC(=O)O)NC(=O)c1ccccc1,train
CCC1(c2ccccc2)C(=O)NCNC1=O,train
CCC(C)(C)OC,train
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CCC2=O,test
CCCCCON=O,train
CN(N=O)C(N)=O,train
COc1cccc(N)c1,train
CC(C)=CCCC(C)CCOC=O,train
CN(C)CCC=C1c2ccccc2CCc2ccccc21,valid
CO[C@H]1C=CO[C@@]2(C)Oc3c(C)c(O)c4c(O)c(c(/C=N/N5CCN(C6CCCC6)CC5)c(O)c4c3C2=O)NC(=O)C(C)=CC=C[C@H](C)[C@H](O)[C@@H](C)[C@@H](O)[C@@H](C)[C@H](OC(C)=O)[C@@H]1C,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)C(=NOCOCCOC)[C@H](C)[C@@H](O)[C@]1(C)O,train
CCC(=O)c1ccccc1,train
CC(C)(C)c1ccc(OC(=O)c2ccccc2O)cc1,train
CCOC(=O)C(=O)OCC,test
O=S(=O)(O)c1ccc(Cl)cc1,train
O=C(Nc1ccccc1)c1cc(Cl)ccc1O,train
CCO[Si](CCCS)(OCC)OCC,train
CNC(=O)Oc1cccc2c1OC(C)(C)O2,train
CC(C)OP(=S)(OC(C)C)SCCNS(=O)(=O)c1ccccc1,valid
O=C1O[C@H]([C@@H](O)CO)C([O-])=C1O,train
O=C1O[C@H]([C@@H](O)CO)C(O)=C1O,train
COC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](N)CC(=O)O,train
C1C2OC2C2C1C1CC2C2OC12,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H]4C[C@H]([C@@H]3[C@@]1(Cl)C2(Cl)Cl)[C@H]1O[C@@H]41,test
OC(c1ccc(Cl)cc1)(c1ccc(Cl)cc1)C(Cl)(Cl)Cl,train
O1[As]2O[As]3O[As]1O[As](O2)O3,train
CC(=O)Oc1ccccc1C(=O)O,train
CCOP(=S)(OCC)Oc1nc(Cl)c(Cl)cc1Cl,train
CCN(CC)C(C)=O,valid
O=S(=O)([O-])O.[Na+],train
Cc1cc(CC(=O)[O-])n(C)c1C(=O)c1ccc(Cl)cc1,train
N#C[N-]C#N,train
COCCOC(=O)C1=C(C)NC(C)=C(C(=O)OC/C=C/c2ccccc2)C1c1cccc([N+](=O)[O-])c1,train
c1cnc2c(c1)ccc1cccnc12,test
ClSC(Cl)(Cl)Cl,train
CCC=CC#N,train
CC=CCC#N,train
CCCCCC[C@@H](O)C/C=C\CCCCCCCC(=O)O,train
CCC(C)(NC(=O)c1cc(Cl)c(C)c(Cl)c1)C(=O)CCl,valid
CO/N=C(/C(=O)OC)c1ccccc1CON=C(C)c1cccc(C(F)(F)F)c1,train
NCCO.O=C(O)c1nc(Cl)ccc1Cl,train
O=c1oc2ccccc2c(O)c1C(CC(O)c1ccc(-c2ccc(Br)cc2)cc1)c1ccccc1,train
N[Pt](N)(Cl)Cl,train
O=C(O)c1ccc(Cl)c(Cl)c1,test
O=S(=O)(c1ccc(Cl)cc1)c1ccc(Cl)cc1,train
CC1(C)C(=O)N(Cl)C(=O)N1Cl,train
C=CCOC(=O)COCCC(C)C,train
CC[C@H](C)COC(=O)/C=C/c1ccc(/N=C/c2ccc(OC)cc2)cc1,train
O=Cc1cccc(Oc2ccccc2)c1,valid
Cc1ccc2oc(=O)ccc2c1,train
CCCC[Sn](CCCC)(CCCC)OC(=O)c1ccccc1,train
CCOC(=S)SSC(=S)OCC,train
CCCCCCCCCC[N+](C)(CCCCCCCCCC)CCC[Si](OC)(OC)OC,train
CC/C(=C(\c1ccccc1)c1ccc(OCCN(C)C)cc1)c1ccccc1,test
CC(=O)S[C@@H]1CC2=CC(=O)CC[C@]2(C)[C@H]2CC[C@@]3(C)[C@@H](CC[C@@]34CCC(=O)O4)[C@@H]21,train
N[C@H]1C(O)O[C@H](CO)[C@H](O)[C@@H]1O,train
CC[C@H]1CC[C@H](NCc2cc3c(cc2OC)[C@H]2C[C@H]2C(=O)N3C)[C@H](c2ccccc2)N1,train
NC(=O)Oc1ccc(Cc2ccccc2)cc1,train
CC(C)(C)C(=O)Oc1ccc(S(=O)(=O)Nc2ccccc2C(=O)NCC(=O)[O-])cc1,valid
FC(F)(F)c1cccc(C2=CCN(CCc3ccc4ccccc4c3)CC2)c1,train
Oc1cn2c(n1)Nc1ccc(Cl)c(Cl)c1C2,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)COC(=O)c1cccc(S(=O)(=O)[O-])c1,train
COc1ccc(C(=O)/C(Br)=C\C(=O)O)cc1,train
CCCCCCCCCCCCCCCC(=O)OC[C@H](COP(=O)([O-])OCC[N+](C)(C)C)OC(=O)CCCCCCCCCCCCCCC,test
COC(=O)[C@H]1[C@H]2C[C@@H]3c4[nH]c5cc(OC)ccc5c4CCN3C[C@H]2C[C@@H](OC(=O)COc2ccc(OC)cc2)[C@@H]1OC,train
CN(Cc1ccccc1)Cc1ccccc1,train
O=C(NNCNNC(=O)c1ccncc1)c1ccncc1,train
O=C([O-])[C@H](CC(=O)N1C[C@H]2CCCC[C@H]2C1)Cc1ccccc1.O=C([O-])[C@H](CC(=O)N1C[C@H]2CCCC[C@H]2C1)Cc1ccccc1,train
CC(C)(COP(=O)([O-])OP(=O)([O-])OC[C@H]1O[C@@H](n2cnc3c(N)ncnc32)[C@H](O)[C@@H]1OP(=O)([O-])O)C(O)C(=O)NCCC(=O)NCCS,valid
Clc1cc(Cl)c(Cl)cc1Cl,train
CCCCCCCOC(=O)CCC,train
CCOP(=S)(OCC)OP(=S)(OCC)OCC,train
CC(C)CCCCCCOC(=O)CCCCC(=O)OCCCCCCC(C)C,train
CCC(O)OCCCOC,test
CC(C)(C)c1n[nH]c(=S)n(N)c1=O,train
O=[N+]([O-])c1cc(C2CCCCC2)c(O)c([N+](=O)[O-])c1,train
O=[N+]([O-])c1cccc([N+](=O)[O-])c1,train
O=[N+]([O-])c1ccccc1[N+](=O)[O-],train
CCCc1nc2c(C)cc(-c3nc4ccccc4n3C)cc2n1Cc1ccc(-c2ccccc2C(=O)O)cc1,valid
Cc1ccc(O)cc1C,train
Cc1cccc(C)c1O,train
CC(=O)c1ccc(S(=O)(=O)NC(=O)NC2CCCCC2)cc1,train
CC(=O)Nc1ccc(O)cc1,train
CC(N)=O,test
CC=O,train
COC(=O)/C=C\C(=O)OC,train
O=C(Nc1ccc(Cl)c(Cl)c1)c1cc(Cl)cc(Cl)c1O,train
Cc1cc(SCc2sc(-c3ccc(C(F)(F)F)c(F)c3)nc2C)ccc1OCC(=O)O,train
CC#N,valid
COc1cc(Br)c(C[N+]2(CCOCCC3CCC4CC3C4(C)C)CCOCC2)cc1OC,train
c1ccc(CCCc2ccncc2)cc1,train
CC(=O)Nc1ccc(C)cc1C,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@@H](c3ccccc3)S(=O)(=O)[O-])C(=O)N2[C@H]1C(=O)[O-],train
Oc1cccc(-c2c3nc(c(-c4cccc(O)c4)c4ccc([nH]4)c(-c4cccc(O)c4)c4ccc(n4)c(-c4cccc(O)c4)c4ccc2[nH]4)CC3)c1,test
N[C@@H](CCC(=O)N[C@@H](CSSC[C@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)C(=O)NCC(=O)O)C(=O)O,train
C[C@]12CCC(=O)C[C@@H]1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@]2(C)O,train
CCOC(=O)CC(C(C)=O)C(C)=O,train
Cc1c(C)c2c(c(C)c1OC(=O)CCC(=O)OCCO)CC[C@@](C)(CCC[C@H](C)CCC[C@H](C)CCCC(C)C)O2,train
CC(CC(c1ccccc1)c1ccccc1)NC(C)(C)C,valid
Cc1cccc(N)n1,train
C=C(C)C(=O)OCC(C)(C)COC(=O)C(=C)C,train
O=C1NCCN1CCN1CCC(c2cn(-c3ccc(F)cc3)c3ccc(Cl)cc23)CC1,train
C[C@]12C[C@H](O)[C@H]3[C@@H](CCC4=CC(=O)C=C[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)COC(=O)CCC(=O)O,train
CN(N=O)c1ccc([N+](=O)[O-])cc1,test
COc1cc(C(=O)OC(COCCC(C)C)CN2CCOCC2)cc(OC)c1OC,train
CC(=O)Oc1ccc(C2(c3ccc(OC(C)=O)cc3)Oc3ccccc3NC2=O)cc1,train
COc1ccc2c(C(=S)N(C)CC(=O)O)cccc2c1C(F)(F)F,train
CN1CCC(N(Cc2cccs2)c2ccccc2)CC1,train
O=C1[C@H]2[C@@H]3CC[C@@H](C3)[C@H]2C(=O)N1CCCCN1CCN(c2ncccn2)CC1,valid
CC(COc1ccccc1)NN,train
CCCN(CCC)C(=O)C(CCC(=O)OCCCN1CCN(CCOC(=O)Cc2c(C)n(C(=O)c3ccc(Cl)cc3)c3ccc(OC)cc23)CC1)NC(=O)c1ccccc1,train
CCOP(=O)(CC)Oc1ccc([N+](=O)[O-])cc1,train
CC(C)(O)c1ccc(C(C)(C)O)cc1,train
O=C(Nc1ccc(Cl)cc1)Nc1ccc(Cl)c(Cl)c1,test
C1C[C@H]2[C@@H]3CC[C@H](C3)[C@H]2C1,train
CC(C)(c1ccccc1)c1ccc(O)c(C(C)(C)c2ccccc2)c1,train
C=C[Si](OC)(OC)OC,train
CCCCCCCCCCCCCCCCCC(=O)[O-].OCC[NH+](CCO)CCO,train
Oc1ccc(Cl)c(Cl)c1,valid
Cc1ccccc1C=O,train
Oc1cc(Cl)cc(Cl)c1,train
Oc1cccc(Cl)c1Cl,train
Clc1cccc(Cl)c1,train
Oc1cc(Cl)ccc1Cl,test
CCCCSCCCC,train
CCCN(CCC)c1c([N+](=O)[O-])cc(C(C)C)cc1[N+](=O)[O-],train
CCc1ccc2c(c1)N(CC(C)CN(C)C)c1ccccc1S2,train
c1cc2c(c(NC3=NCCN3)c1)CCCC2,train
NS(=O)(=O)c1cc2c(cc1Cl)NC(Cc1ccccc1)NS2(=O)=O,valid
CC(C)C1(C)N=C(c2nc3ccccc3cc2C(=O)O)NC1=O,train
O=c1[nH]c2ccccc2n1CCCN1CCC(n2c(=O)[nH]c3cc(Cl)ccc32)CC1,train
Clc1ccccc1-c1nc(-c2ccncc2)no1,train
O=C(O)CCc1nc(-c2ccccc2)c(-c2ccccc2)o1,train
CC[C@]12CCCN3CCc4c(n(c5ccccc45)C(=O)C1)[C@@H]32,test
CCCCOCN(C(=O)CCl)c1c(CC)cccc1CC,train
CC(C)N1C(=O)N(c2ccccc2)CSC1=NC(C)(C)C,train
O=C(O)Cc1c[nH]c2ccccc12,train
O=Cc1ccc(F)cc1,train
CC(C)Cc1ccc(C(C)C(=O)O)cc1,valid
Oc1cccc2cccnc12,train
c1ccc2[nH]ccc2c1,train
CCCSP(=O)(OCC)Oc1ccc(Br)cc1Cl,train
CN1C[C@H](CNC(=O)OCc2ccccc2)C[C@@H]2c3cccc4c3c(cn4C)C[C@H]21,train
CCN1CCC[C@H]1CNC(=O)c1cc(S(N)(=O)=O)ccc1OC,test
OCCO,train
CCc1ccccc1,train
CC(C)(CCl)c1ccccc1,train
CCN(N=O)C(N)=O,train
CCOC(=O)C1OC1(C)c1ccccc1,valid
O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,train
O=S1(=O)OCCO1,train
CC(C)(C)C(=O)C(Oc1ccc(Cl)cc1)n1cncn1,train
CC[C@H](C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](CC=C(C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H](O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)C=CC=C3CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,train
COc1ccc([C@@H]2CC(=O)c3c(O)cc(O[C@@H]4O[C@H](CO[C@@H]5O[C@@H](C)[C@H](O)[C@@H](O)[C@H]5O)[C@@H](O)[C@H](O)[C@H]4O)cc3O2)cc1OC,test
CCCCC/C=C\C/C=C\CCCCCCCC(=O)OC,train
CC1OC1C1CCC2(C)OC2C1,train
COS(C)(=O)=O,train
C=C(C)C(=O)OC,train
Cc1ccc2c(c1[N+](=O)[O-])C(=O)c1ccccc1C2=O,valid
CN(N=O)C(=N)N[N+](=O)[O-],train
NCc1ccccc1,train
O=[N+]([O-])c1ccc(O)cc1,train
CN(C)c1ccc(C=O)cc1,train
CCN(CC)CCO,test
Cc1cccc([N+](=O)[O-])c1,train
Cc1ccc(N(C)C)cc1,train
O=[N+]([O-])[O-].[NH4+],train
CC1=C(C(=O)OCCN(Cc2ccccc2)c2ccccc2)C(c2cccc([N+](=O)[O-])c2)C(P2(=O)OCC(C)(C)CO2)=C(C)N1,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CCC4=C3C=C[C@@]21CC,valid
Cc1ccc(S(=O)(=O)NC(=O)NN2CC3CCCC3C2)cc1,train
COc1ccc2c(c1)C(=O)N(CCc1ccc(S(=O)(=O)NC(=O)NC3CCCCC3)cc1)C(=O)C2(C)C,train
Cc1cc(C(=O)NCCc2ccc(S(=O)(=O)NC(=O)NN3CCCCCC3)cc2)no1,train
CC(C)[C@H]1CC[C@@H](C)CC1=O,train
O=C(O)CS,test
COC1CCC=CO1,train
CC(CO)(CO)C(=O)O,train
OCCOCCOCCOCCOCCO,train
COC(=O)c1cccnc1,train
CC1=C2N=C(C=C3N=C(C(C)=C4N([Co+]C[C@H]5O[C@@H](n6cnc7c(N)ncnc76)[C@H](O)[C@@H]5O)C(C(CC(N)=O)C4(C)CCC(=O)NCC(C)OP(=O)([O-])O[C@@H]4[C@@H](CO)O[C@H](n5cnc6cc(C)c(C)cc65)[C@@H]4O)C4(C)N=C1C(CCC(N)=O)C4(C)CC(N)=O)C(CCC(N)=O)C3(C)C)C(CCC(N)=O)C2(C)CC(N)=O,valid
CC(C)C[C@@H]1NC(=O)[C@H](CCCN)NC(=O)[C@H](C(C)C)NC(=O)[C@@H]2CCCN2C(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](CCCN)NC(=O)[C@H](C(C)C)NC(=O)[C@@H]2CCCN2C(=O)[C@@H](Cc2ccccc2)NC1=O,train
CNCc1ccccc1,train
CN(C)CCC[C@@]1(c2ccc(F)cc2)OCc2cc(C#N)ccc21,train
CC(C)CC(=O)OCc1ccccc1,train
CC(C)(O)Cc1ccccc1,test
CCC(=O)OC/C=C/c1ccccc1,train
NC(=O)Cn1c(=O)cc(CCN2CCN(c3nccc4sccc34)CC2)c2ccc(F)cc21,train
CCCC(=O)OCCc1ccccc1,train
O=C(C=Cc1ccccc1)OCCc1ccccc1,train
COc1ccc(S(=O)(=O)N2C(=O)[C@@](c3ccccc3OC)(N3C[C@H](O)C[C@H]3C(=O)N(C)C)c3cc(Cl)ccc32)c(OC)c1,valid
O=C(Oc1ccc(Br)cc1)N1CCN2CCC1CC2,train
CC(C)C(=O)OCC=Cc1ccccc1,train
CCCCC1CCC(=O)O1,train
NCCS,train
NS(=O)(=O)c1cc2c(cc1Cl)NC(C1CC3C=CC1C3)NS2(=O)=O,test
N[C@@H]1CONC1=O,train
CN1CCC(=C2c3ccccc3C=Cc3ccccc32)CC1,train
O=C(OC1CN2CCC1CC2)c1ccccc1,train
Cc1ccc2c(c1)C1CN(C)CCC1N2,train
COc1ccccc1Oc1c(NS(=O)(=O)c2ccc(C(C)(C)C)cc2)nc(-c2ncccn2)nc1OCCO,valid
O=C1CCCCC1CN1CCCCC1,train
CC1=CC(=O)/C(=C(/C)[O-])C(=O)O1,train
Nc1ccc([As](=O)([O-])O)cc1,train
CCCCCCCc1ccc(O)cc1,train
O[C@H]1CO[C@@H]2[C@H](O)CO[C@H]12,test
Fc1ccc(C2(Cn3cncn3)OC2c2ccccc2Cl)cc1,train
O[C@H](CBr)[C@@H](O)[C@H](O)[C@H](O)CBr,train
CC(=O)Nc1c(I)c(NC(C)=O)c(I)c(C(=O)[O-])c1I,train
Cc1ccc(N)cc1[N+](=O)[O-],train
OC[C@H]1OC(O)[C@H](O)[C@@H](O)[C@@H]1O,valid
Cc1c(C(=O)O)cccc1[N+](=O)[O-],train
CC1=Nc2ccc(Cl)cc2S(=O)(=O)N1,train
NC[C@@H]1CC[C@@H](N)[C@@H](O[C@@H]2[C@@H](N)C[C@@H](N)[C@H](O[C@H]3O[C@H](CO)[C@@H](O)[C@H](N)[C@H]3O)[C@H]2O)O1,train
CCOCCn1c(N2CCCN(C)CC2)nc2ccccc21,train
NS(=O)(=O)c1cc(-c2nnn[nH]2)c(NCc2cccs2)cc1Cl,test
COc1cc2c(cc1OC)CC(=O)N(CCCN(C)C[C@H]1Cc3cc(OC)c(OC)cc31)CC2,train
CCOC(=O)[C@H](CCc1ccccc1)N[C@@H](C)C(=O)N1C(=O)N(C)C[C@H]1C(=O)O,train
CC1(C)C[C@@H]1C(=O)N/C(=C\CCCCSC[C@H](N)C(=O)O)C(=O)[O-],train
CCOC(=O)[C@H](CCc1ccccc1)N[C@H]1CS[C@H](c2cccs2)CN(CC(=O)O)C1=O,train
CCCCC/C=C\CCC(=O)N[C@H]1[C@H](Oc2c3cc4cc2Oc2ccc(cc2Cl)[C@@H](O[C@@H]2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2NC(C)=O)[C@@H]2NC(=O)[C@H](NC(=O)[C@@H]4NC(=O)[C@H]4NC(=O)[C@@H](Cc5ccc(c(Cl)c5)O3)NC(=O)[C@@H](N)c3ccc(O)c(c3)Oc3cc(O)cc4c3)c3ccc(O)c(c3)-c3c(O[C@H]4O[C@H](CO)[C@@H](O)[C@H](O)[C@@H]4O)cc(O)cc3[C@H](C(=O)O)NC2=O)O[C@H](CO)[C@@H](O)[C@@H]1O,valid
S=C(NC1CCCCC1)NC1CCCCC1,train
CCCN(CCC)c1c([N+](=O)[O-])cc(S(C)(=O)=O)cc1[N+](=O)[O-],train
CCCCCCCCCCCCN1CCCC1=O,train
CCCCCCCCN(C)CCCCCCCC,train
CNC(=O)C(c1ccccc1)c1ccccc1,test
CC(Oc1cccc(Cl)c1)C(=O)O,train
O=Cc1ccc(F)c(Br)c1,train
O=C(O)c1cccc(/C=C2/C[C@H]3[C@H](C[C@@H](O)[C@@H]3/C=C/[C@@H](O)C3CCCCC3)O2)c1,train
O=C(O)c1ccccc1S,train
C=COCCOCCOC=C,valid
CS(=O)(=O)NC(=O)CCC/C=C\C[C@H]1C(=O)C[C@@H](O)[C@@H]1/C=C/[C@H](O)COc1ccccc1,train
OCC(O)COc1ccc(Cl)cc1,train
CN(C)CCCNCCC#N,train
O=P(O)(O)Oc1ccccc1,train
CN(C1CCCCC1)S(=O)(=O)c1ccccc1N,test
C=CC(=O)OCCOCCOCC,train
C=C(C)[C@H]1Cc2c(ccc3c2O[C@@H]2COc4cc(OC)c(OC)cc4[C@@H]2C3=O)O1,train
N=C1C=CC(=C(c2ccc(N)cc2)c2ccc(N)cc2)C=C1,train
Cc1cc(C(=C2C=CC(=N)C=C2)c2ccc(N)cc2)ccc1N,train
CO[C@H]1C=CO[C@@]2(C)Oc3c(C)c(O)c4c(O)c(c(/C=N/N5CCN(C)CC5)c(O)c4c3C2=O)NC(=O)C(C)=CC=C[C@H](C)[C@H](O)[C@@H](C)[C@@H](O)[C@@H](C)[C@H](OC(C)=O)[C@@H]1C,valid
CC/N=c1/cc2oc3cc(NCC)c(C)cc3c(-c3ccccc3C(=O)OCC)c-2cc1C,train
CCCCCCCCCCCCCCCC(=O)OC/C=C(C)/C=C/C=C(C)/C=C/C1=C(C)CCCC1(C)C,train
CC(=O)OC/C=C(C)/C=C/C=C(C)/C=C/C1=C(C)CCCC1(C)C,train
CO[Si](C)(OC)OC,train
O=C1Nc2ccccc2C1=O,test
CCN(CC)C1CCCCC1,train
c1ccc2ncccc2c1,train
CCC(=O)c1c(O)cc(O)cc1O,train
O=[N+]([O-])c1ccccc1O,train
Oc1cccc2ccccc12,valid
O=NN1CCCCC1,train
O=c1c2ccccc2oc2ccccc12,train
Cc1ccccc1N=O,train
O=C(O)CN(CC(=O)O)CC1=CC2C(=CC1=O)Oc1cc(O)c(CN(CC(=O)O)CC(=O)O)cc1C21OC(=O)c2ccccc21,train
CN(C)P(=O)(N(C)C)N(C)C,test
CCCCCC(N)=O,train
CCCCCCOc1cc(C)c(O)c(C)c1C,train
Oc1c(Cl)cc(Cl)c(Cl)c1Cc1c(O)c(Cl)cc(Cl)c1Cl,train
CCCCCCC(O)CCCCCCCCCCC(=O)OC,train
CCCCOCCOCCOC(=O)CCCCC(=O)OCCOCCOCCCC,valid
CC(C)OC(=S)[S-],train
CCc1cccc(CC)c1N(COC)C(=O)CCl,train
CNC(=O)ON=C(C)SC,train
COc1ccc([C@@H]2CC(=O)c3c(O)cc(O[C@@H]4O[C@H](CO[C@@H]5O[C@@H](C)[C@H](O)[C@@H](O)[C@H]5O)[C@@H](O)[C@H](O)[C@H]4O)cc3O2)cc1O,train
Cc1ccc([N+](=O)[O-])cc1,test
O=S(=O)([O-])CCN1CCN(CCO)CC1,train
Cc1c(C2(c3cc(Br)c(O)c(Br)c3C)OS(=O)(=O)c3ccccc32)cc(Br)c(O)c1Br,train
N=C(N)c1ccc(OCCCCCOc2ccc(C(=N)N)cc2)cc1.O=S(=O)(O)CCO.O=S(=O)(O)CCO,train
C[C@]12C/C(=C/O)C(=O)C[C@@H]1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@]2(C)O,train
O=C(CCCCCCC(=O)Nc1ccccc1)NO,valid
CCCOC(OCCC)OCCC,train
COC(=O)CCCCC(=O)O,train
Nc1cc[nH]c(=O)n1,train
CC[N+](CC)(CC)CC,train
CCOC(=O)CC(=O)CCl,test
C=C(C)C(=O)OCCCCCCOC(=O)C(=C)C,train
C[C@H](C(=O)[O-])c1cccc(Oc2ccccc2)c1.C[C@H](C(=O)[O-])c1cccc(Oc2ccccc2)c1,train
CCSc1ccc2c(c1)N(CCCN1CCN(C)CC1)c1ccccc1S2.O=C(O)CC(O)C(=O)O.O=C(O)CC(O)C(=O)O,train
NC(=O)c1cc[n+](CC2=C(C(=O)[O-])N3C(=O)[C@@H](NC(=O)[C@H](c4ccccc4)S(=O)(=O)[O-])[C@@H]3SC2)cc1,train
CNC[C@H](O)[C@@H](O)[C@@H](O)[C@H](O)CO.Cc1c(Nc2ncccc2C(=O)O)cccc1C(F)(F)F,valid
CCCCC[C@H](O)/C=C/[C@H]1[C@H](O)C[C@H](O)[C@@H]1C/C=C\CCCC(=O)O.COC(N)(CO)CO,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@@H](NC(=O)N3CCNC3=O)c3ccccc3)C(=O)N2[C@H]1C(=O)[O-],train
c1cc(OCC2CO2)cc(OCC2CO2)c1,train
O=C1CCc2ccccc2O1,train
CC1(C)OC(=O)NC1=O,test
CNC(=O)CSP(=S)(OC)OC,train
Oc1c(Cl)cc(Cl)cc1Sc1cc(Cl)cc(Cl)c1O,train
BrC(Br)C(Br)(Br)Br,train
NS(=O)(=O)c1cc2c(cc1C(F)(F)F)NCNS2(=O)=O,train
Clc1ccc(C(Cl)(Cl)Cl)c(Cl)c1,valid
CCCCNc1ccc(C(=O)OCCN(C)C)cc1,train
CCCCOc1ccc(C(=O)CCN2CCCCC2)cc1,train
COC(=O)C1CCC(C(=O)OC)CC1,train
CNC(=O)Oc1c(C(C)(C)C)cc(C)cc1C(C)(C)C,train
CCC(C)(CN(C)C)OC(=O)c1ccccc1,test
CCCCCCCC/C=C\CCCCCCCC(=O)N(CCO)CCO,train
CCCCCCCCCCCCCCCC[N+](C)(C)CCN(Cc1ccc(OC)cc1)c1ncccn1,train
CCCOc1cc(N)ccc1C(=O)OCCN(CC)CC,train
O=C(Nc1ccccc1)OCC(CN1CCCCC1)OC(=O)Nc1ccccc1,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@@]21CC,valid
O=[N+]([O-])c1ccccc1[O-],train
CCOC(=O)CBr,train
NCC(O)c1cccc(O)c1,train
Oc1cc(Cl)c(Cl)c(Cl)c1,train
CC(O)[C@H]1CC[C@H]2[C@@H]3CC[C@@H]4C[C@@H](O)CC[C@]4(C)[C@H]3CC[C@]12C,test
C=CCn1c(=O)n([C@@H]2O[C@H](CO)[C@@H](O)[C@H]2O)c2nc(N)[nH]c(=O)c21,train
CC1=C[C@H](O)CC(C)(C)[C@H]1/C=C/C(C)=C/C=C/C(C)=C/C=C\C=C(C)\C=C\C=C(C)\C=C\C1=C(C)C[C@@H](O)CC1(C)C,train
CC(C)(Cc1c[nH]c2ccccc12)NCC(O)COc1ccccc1C#N,train
CCOc1cc(-c2nccc3cc(OC)c(OC)cc23)cc(OCC)c1OCC,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,valid
Cc1nc(-c2ccc(Cl)cc2)oc1COC(C)(C)C(=O)O,train
CN1CCC(OC(=O)C(Oc2ccc(Cl)cc2)Oc2ccc(Cl)cc2)CC1,train
N[Pt]1(N)OC(=O)C2(CCC2)C(=O)O1,train
CCC1Nc2cc(Cl)c(S(N)(=O)=O)cc2S(=O)(=O)N1,train
CCCC(=O)OCCC(C)C,test
Cc1ccccc1NC(=N)NC(=N)N,train
C1=Cc2ccccc2C1,train
O=C(O)C(=O)Cc1c[nH]c2ccccc12,train
O=[Se](O)O,train
NC(N)=[Se],valid
CCc1oc2ccccc2c1C(=O)c1cc(Br)c(O)c(Br)c1,train
CCCC(=NOCC)C1=C(O)CC(CC(C)SCC)CC1=O,train
O=C([O-])[C@H](O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,train
CC(C)CCCCCCCCCCCCCCC(=O)O,train
CCOP(=O)(OCC)Oc1ccc([N+](=O)[O-])cc1,test
CCOS(=O)(=O)OCC,train
COS(=O)(=O)[O-].Cn1c(-c2ccccc2)cc(-c2ccccc2)[n+]1C,train
O=C(NC(=O)c1c(F)cccc1F)Nc1ccc(Cl)cc1,train
Cc1ccc(NC=O)c(C)c1,train
Cc1ccc(C=O)s1,valid
CC1(C)C2CCC3(C2)[C@H]1C(=O)CCC3(C)C,train
CC(=O)c1ccc2c(c1)CCCC2,train
C=CC(=O)CCCCC,train
CC(=O)CCc1ccc2c(c1)OCO2,train
C=CC(C)(CCC=C(C)C)OC(=O)CC(C)C,test
CNC(C)=O,train
COc1ccc(CCC(C)=O)cc1,train
Cc1ccc2c(c1)OCC(=O)CO2,train
CCCC1CCCC(=O)O1,train
C[C@]12C[C@H](O)[C@@]3(F)[C@@H](CCC4=CC(=O)C=C[C@@]43C)[C@@H]1C[C@@H](O)[C@]2(O)C(=O)CO,valid
Cn1sccc1=O,train
CNC(=O)Oc1ccccc1C1OCCO1,train
CC(=O)[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
CCCc1c(OCCCOc2ccc(OCC(=O)O)cc2)ccc(C(C)=O)c1O,train
C=C(C)C(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COC(=O)C(=C)C)cc2)cc1,test
C=C(C)CS(=O)(=O)[O-],train
NC(=O)Cc1cccc(C(=O)c2ccccc2)c1N,train
C[Si](NC1CCCCC1)(NC1CCCCC1)NC1CCCCC1,train
CCCCOC(=O)CCS,train
CCCCCCCCOCCCN,valid
CC(C)OP(=O)(CP(=O)(OC(C)C)OC(C)C)OC(C)C,train
O=Cc1ccc(Cl)c([N+](=O)[O-])c1,train
Nc1nc(=S)[nH][nH]1,train
C=CC(=O)NCOCC(C)C,train
CC1=Nc2ccc3ccccc3c2C1(C)C,test
CC(C)(C)NC(=O)[C@H]1CC[C@H]2[C@@H]3CC=C4C=C(C(=O)O)CC[C@]4(C)[C@H]3CC[C@]12C,train
O=[N+]([O-])c1cccc(C(F)(F)F)c1,train
CCCCCCCCC,train
Cc1ccccc1[N+](=O)[O-],train
O=[N+]([O-])c1ccccc1C(F)(F)F,valid
CCOC(=O)N(C)N=O,train
C#C[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CCCC[C@@H]4[C@H]3CC[C@@]21C,train
CC(=O)Nc1ccc2c(c1)Cc1cc(NC(C)=O)ccc1-2,train
O=C1NCCN1CCO,train
CNCCS(=O)(=O)[O-],test
CCc1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,train
COc1cc(NC(=O)CC(C)=O)c(OC)cc1Cl,train
CCO[Si](CCCCl)(OCC)OCC,train
Oc1c(Br)c(Br)c(Br)c(Br)c1Br,train
O=C(Nc1ccccc1-c1ccc(Cl)cc1)c1cccnc1Cl,valid
CCCCCCCCCCCC(C)=O,train
OC[C@H]1O[C@](O)(CO)[C@@H](O)[C@@H]1O,train
Clc1ccc(Cn2c(CN3CCCC3)nc3ccccc32)cc1,train
O=C1CCc2cc(OCCCCc3nnnn3C3CCCCC3)ccc2N1,train
Oc1cc(O)c2c(c1)O[C@H](c1ccc(O)c(O)c1)[C@H](O)C2,test
c1ccc(C2CC2)c(OCC2=NCCN2)c1,train
Clc1cccc(N2CCNCC2)c1,train
NC(Cc1ccc(Cl)cc1)C(=O)O,train
Nc1ccc2c(c1)C(=O)c1ccccc1C2=O,train
Cc1cc(N=Nc2ccccc2C)ccc1N,valid
Nc1cc([N+](=O)[O-])ccc1O,train
Nc1ccc([N+](=O)[O-])cc1O,train
Nc1ncc([N+](=O)[O-])s1,train
Nc1ccc(O)c([N+](=O)[O-])c1,train
Nc1nc(-c2ccc([N+](=O)[O-])cc2)cs1,test
O=C(NN1CCCC1)NS(=O)(=O)c1ccc(Cl)cc1,train
C=CC[N+]1([C@H]2C[C@H]3[C@@H]4CC[C@H]5C[C@H](OC(C)=O)[C@@H](N6CCCCC6)C[C@]5(C)[C@H]4CC[C@]3(C)[C@H]2OC(=O)CC)CCCCC1,train
CCOC(=O)CCC(=O)OCC,train
O=[N+]([O-])c1nccn1CO[C@@H](CO)[C@H](O)CO,train
CC[C@H]1C[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@]2(C)[C@H]1O,valid
CCCCCC1Nc2cc(C(F)(F)F)c(S(N)(=O)=O)cc2S(=O)(=O)N1,train
CO/N=C(/C(=O)N[C@@H]1C(=O)N2C(C(=O)O)=C(Cn3nnc(C)n3)CS[C@H]12)c1csc(N)n1,train
Cc1cccc(N(C)C(=S)Oc2ccc3c(c2)CCC3)c1,train
CC(=O)OCC(=O)Nc1cc(C#N)cc(NC(=O)COC(C)=O)c1Cl,train
C[C@](N)(Cc1ccc(O)c(O)c1)C(=O)O.C[C@](N)(Cc1ccc(O)c(O)c1)C(=O)O,test
Cc1ccc2cc3c(ccc4ccccc43)c3c2c1CC3,train
Cc1ccc(O)c(O)c1,train
Cc1cc(Cc2ccc(N)c(C)c2)ccc1N,train
FC(F)(F)C(Cl)(Cl)Cl,train
N#CCC(=O)O,valid
CCSCC,train
CN(C)c1ccc(Cc2ccc(N(C)C)cc2)cc1,train
ClCCl,train
FC(F)(F)c1ccc(Cl)c(Cl)c1,train
O=C=Nc1cccc(C(F)(F)F)c1,test
CC[C@@H](CO)NC(=O)[C@@H]1C=C2c3cccc4c3c(cn4C)C[C@H]2N(C)C1,train
C[C@H]1C[C@H]2[C@@H]3CC[C@](O)(C(=O)CO)[C@@]3(C)C[C@H](O)[C@@H]2[C@@]2(C)C=CC(=O)C=C12,train
CC(=O)OCC(=O)[C@@]1(O)CC[C@H]2[C@@H]3C[C@H](C)C4=CC(=O)C=C[C@]4(C)[C@H]3[C@@H](O)C[C@@]21C,train
C[C@H]1C[C@H]2[C@@H]3CC[C@](O)(C(=O)COC(=O)CCC(=O)[O-])[C@@]3(C)C[C@H](O)[C@@H]2[C@@]2(C)C=CC(=O)C=C12,train
C=CCOC(=O)C(=C)C,valid
Nc1ccc(Cl)c(Cl)c1,train
NCCCC(=O)O,train
Nc1ccccc1Cl,train
CCC(C)C=O,train
FC(F)(F)c1ccc(Cl)cc1,test
C[Si](C)(C)Cl,train
CCC(Cl)C(Cl)C(Cl)CC(Cl)C(Cl)C(C)Cl,train
C=Cc1ccc(C=C)cc1,train
Nc1ccc([As](=O)(O)O)cc1,train
O=[N+]([O-])C(Cl)(Cl)Cl,valid
Clc1ccc(Sc2cc(Cl)c(Cl)cc2Cl)cc1,train
Cc1ccc(C)c2ccccc12,train
O=S(=O)(Oc1ccc(Cl)cc1)c1ccc(Cl)cc1,train
Cc1nnc2n1-c1sc(CCc3ccc(CC(C)C)cc3)cc1C(c1ccccc1Cl)=NC2C,train
CCC(=O)[O-].CCC(=O)[O-],test
COC(=O)c1cc(C(=O)OC)cc(S(=O)(=O)[O-])c1,train
O=C([O-])CCl,train
CC(C)C(OC(=O)c1ccccc1)C(C)(C)COC(=O)c1ccccc1,train
OCCCO,train
O=C(CC(=O)c1ccccc1)c1ccccc1,valid
CC(C)(C)c1cc(O)c(C(C)(C)C)cc1O,train
COC(=O)[C@H](c1ccccc1Cl)N1CCc2sccc2C1,train
CC(C)CCCCOC(=O)c1ccccc1C(=O)OCCCCC(C)C,train
Cc1ncc(CO)c(C=O)c1O,train
NS(=O)(=O)Cc1noc2ccccc12,test
CNC1=Nc2ccc(Cl)cc2C(c2ccccc2)=[N+]([O-])C1,train
O=C1Nc2ccc(Cl)cc2[C@@](C#CC2CC2)(C(F)(F)F)O1,train
O=C(NC(=O)c1cc(F)c(F)cc1Cl)Nc1cc(F)ccc1N1CCC(C(=O)O)CC1,train
CCOC(=O)O[C@H](C)OC(=O)c1ccc2c(c1)cc(C(=O)NC1CCN(C(C)C)CC1)n2Cc1cc(-c2ccc(Cl)s2)on1,train
COc1ccc(C(=O)Nc2nc3ccccc3c(NCc3ccccc3)c2C#N)cc1,valid
O=C(O)c1ccccc1-n1cc(C(=O)O)c(=O)c2cc(Nc3c(F)cc(F)cc3Cl)c(Cl)cc21,train
CC[C@H](NC(=O)c1cc(F)ccc1[N-]S(=O)(=O)c1cccc2cccnc12)c1ccccc1,train
NCc1cccc(C2CCN(C(=O)c3ccc(C#Cc4ccccc4F)o3)CC2)c1,train
CCN(Cc1cccnc1)C(=O)c1cccc(Cl)c1[C@]1(C)C(=O)N(Cc2ccc(OC)cc2OC)c2ccc(Cl)cc21,train
CC(C)(C)C(=O)CCc1ccc(Cl)cc1,test
CCCCCC(C)OC(=O)COc1ccc(Cl)c2cccnc12,train
Cc1nc(C)c(Cl)c(O)c1Cl,train
CC(C)=CCCC(C)CC=O,train
O=c1oc2ccccc2c(O)c1C1CCCc2ccccc21,train
COCC(C)OC(C)=O,valid
Cc1cc(C)cc(C)c1,train
Cc1cccc(N)c1,train
COC(=O)C1=C(C)NC(C)=C(C(=O)OCCN2CCN(C(c3ccccc3)c3ccccc3)CC2)C1c1cccc([N+](=O)[O-])c1,train
Clc1nc(Cl)nc(Cl)n1,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CCCC[C@@H]4[C@H]3C(=C)C[C@@]21CC,test
CC(=O)OCC(=O)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]12C,train
CC(C)Nc1cccnc1N1CCN(C(=O)c2cc3cc(NS(C)(=O)=O)ccc3[nH]2)CC1,train
CCNC(=O)[C@@H]1CCCN1C(=O)[C@H](CCCNC(=N)N)NC(=O)[C@H](CC(C)C)NC(=O)[C@@H](Cc1c[nH]c2ccccc12)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@H](CO)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)[C@H](Cc1c[nH]cn1)NC(=O)[C@@H]1CCC(=O)N1,train
CCCCCCCCCCCCCCCCCCCCCC(=O)Nc1ccn([C@@H]2O[C@H](CO)[C@@H](O)[C@@H]2O)c(=O)n1,train
COc1ccc2[nH]c(S(=O)Cc3ncc(C)c(OC)c3C)nc2n1,valid
COc1cc(C)c(Cc2cnc(N)nc2N)cc1OC,train
Oc1ccc(C(O)CNCCCCCCNCC(O)c2ccc(O)c(O)c2)cc1O,train
O=C([O-])[C@H]1/C(=C/CO)O[C@@H]2CC(=O)N21,train
CCC1CCC(=O)O1,train
C=CC(C)(CCC=C(C)C)OC(=O)C=Cc1ccccc1,test
C/C=C/C(=O)Oc1c(CCCCCC(C)C)cc([N+](=O)[O-])cc1[N+](=O)[O-],train
O=C(O)CCCCCCCCCC(=O)O,train
CC(C)[C@H]1CC[C@H](C(=O)N[C@H](Cc2ccccc2)C(=O)O)CC1,train
COc1ccc2cc([C@H](C)C(=O)O)ccc2c1,train
Nc1cc(N2CCCCC2)nc(N)[n+]1[O-],valid
CC[C@H](C)C(=O)O[C@H]1CCC=C2C=C[C@H](C)[C@H](CC[C@@H]3C[C@@H](O)CC(=O)O3)[C@H]21,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C=C(C)C4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
COC(=O)Nc1nc2cc(C(=O)c3ccccc3)ccc2[nH]1,train
C[C@@H]1OC(=O)C[C@H](O)C[C@H](O)C[C@H](O)CC[C@@H](O)[C@H](O)C[C@]2(O)C[C@H](O)[C@@H](C(=O)O)C(C[C@@H](O[C@H]3O[C@@H](C)[C@H](O)[C@@H](N)[C@H]3O)C=CC=CC=CC=CCCC=CC=C[C@H](C)[C@@H](O)[C@H]1C)O2,train
COc1cc(OC)c(C(=O)CCCN2CCCC2)c(OC)c1,test
OCCSc1ccccc1,train
CC(C)c1cccc(C(C)C)c1N1C(=O)c2ccc(O)cc2C1=O,train
O=C1Cc2ccccc2N1c1c(Cl)cccc1Cl,train
CCCCCSCCCCC,train
C=CC(=O)OCC(COC(=O)C=C)(COC(=O)C=C)COC(=O)C=C,valid
CSc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl,train
CC(S)C(C)S,train
O=C(Nc1ccccc1)N(Cc1ccc(Cl)cc1)C1CCCC1,train
CCCC(Cn1cncn1)c1ccc(Cl)cc1Cl,train
CC(=O)c1ccc[nH]1,test
CC(=O)c1cnccn1,train
CCCCCC/C=C/C=O,train
COC(=O)c1ccccc1OC,train
CCc1ccc(C=O)cc1,train
CC(C)(C)C1CCCCC1=O,valid
CCOP(=S)(OCC)SCSc1cc(Cl)ccc1Cl,train
CCCC(=O)OC,train
CC(CN(CCN(CC(C)OCCO)CC(C)OCCO)CC(C)OCCO)OCCO,train
CCN(CC)CCCN(c1ccccc1)C1Cc2ccccc2C1,train
CO.COc1cc(Nc2c(C#N)cnc3cc(OCCCN4CCN(C)CC4)c(OC)cc23)c(Cl)cc1Cl,test
COC(=O)C1=C(C)NC(C)=C(C(=O)O[C@@H]2CCCN(Cc3ccccc3)C2)[C@@H]1c1cccc([N+](=O)[O-])c1,train
COCCCOc1cc(C[C@@H](C[C@H](N)[C@@H](O)C[C@H](C(=O)NCC(C)(C)C(N)=O)C(C)C)C(C)C)ccc1OC,train
CC(=O)Nc1ccc(S(=O)(=O)Nc2cc(C)on2)cc1,train
C[C@@H](O[C@H]1OCCN(CC2=NC(=O)N=N2)[C@H]1C1CCC(F)CC1)c1cc(C(F)(F)F)cc(C(F)(F)F)c1,train
CCCN(CCC)C(=O)Cc1c(-c2ccc(Cl)cc2)nc2ccc(Cl)cn12,valid
CC1(C)N=C(N)N=C(N)N1c1ccc(Cl)cc1,train
NC(=O)COCCN1CCN(C(c2ccccc2)c2ccc(Cl)cc2)CC1,train
CCNc1nc(Cl)nc(NCC)n1,train
CCN(CC)CCOc1ccc(-c2nc3cc(Cl)ccc3s2)cc1,train
C=C1CC[C@H](O)C/C1=C/C=C1\CCC[C@@]2(C)[C@H]1CC[C@@H]2[C@H](C)CC[C@@H](O)C(C)(C)O,test
O=[Se]=O,train
Oc1ccc2c(c1)OCO2,train
CN1C(=O)CCS(=O)(=O)C1c1ccc(Cl)cc1,train
S=C(SSSSSSC(=S)N1CCCCC1)N1CCCCC1,train
ClC/C=C\CCl,valid
CCc1ncnc2cc(OC)c(OC)cc12,train
CC(C)(C)C(=O)C(=O)O,train
Cc1c(N)cccc1N,train
CCOC(=O)NC(O)C(Cl)(Cl)Cl,train
CC(=O)[C@@]1(O)CC[C@H]2[C@@H]3C=C(Cl)C4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,test
O=NN1CCCCCC1,train
O=S(=O)(O)c1ccccc1,train
CC[C@@H](C)CCCCC(=O)N[C@H](CCN)C(=O)N[C@@H](C(=O)N[C@H](CCN)C(=O)N[C@@H]1CCNC(=O)C([C@@H](C)O)NC(=O)[C@@H](CCN)NC(=O)[C@@H](CCN)NC(=O)[C@@H](CC(C)C)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@@H](CCN)NC1=O)[C@@H](C)O,train
NC(=O)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1,train
COc1cccc(OC)c1C(=O)N[C@@H]1C(=O)N2[C@@H](C(=O)[O-])C(C)(C)S[C@H]12,valid
OC[C@H]1O[C@](O)(CO)[C@@H](O)[C@@H]1O[C@@H]1O[C@H](CO)[C@H](O)[C@H](O)[C@H]1O,train
O=[N+]([O-])O[C@H]1CO[C@H]2[C@@H]1OC[C@H]2O[N+](=O)[O-],train
CC(C(=O)O)c1ccc(N2Cc3ccccc3C2=O)cc1,train
Oc1cc2c(cc1O)[C@H]1c3ccc(O)c(O)c3OC[C@@]1(O)C2,train
ClC1=C(Cl)C2(Cl)C3C(Cl)C=CC3C1(Cl)C2(Cl)Cl,test
COC1=CC(=O)O[C@@H](/C=C/c2ccc3c(c2)OCO3)C1,train
COC1=CC(=O)C[C@@H](C)[C@]12Oc1c(Cl)c(OC)cc(OC)c1C2=O,train
CCN(Cc1cccc(S(=O)(=O)[O-])c1)c1ccc(C(=C2C=CC(=[N+](CC)Cc3cccc(S(=O)(=O)[O-])c3)C=C2)c2ccc(O)cc2S(=O)(=O)[O-])cc1,train
COC(=O)CS,train
CCCN1CCCC[C@H]1C(=O)Nc1c(C)cccc1C,valid
O=C([O-])C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
O=S(=O)([O-])C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
CC1(C)[C@H]2CC[C@](C)(C2)[C@H]1O,train
CC(C)c1c(C(=O)Nc2ccccc2)c(-c2ccccc2)c(-c2ccc(F)cc2)n1CC[C@@H](O)C[C@@H](O)CC(=O)[O-].CC(C)c1c(C(=O)Nc2ccccc2)c(-c2ccccc2)c(-c2ccc(F)cc2)n1CC[C@@H](O)C[C@@H](O)CC(=O)[O-],train
CCOC(=O)C(C)C(C)=O,test
ClCC(Br)CBr,train
c1ccc2c(c1)Oc1ccccc1O2,train
BrCCBr,train
CCCCCCCCCCCOC(=O)c1ccccc1C(=O)OCCCCCCCCCCC,train
C=Cc1ccccc1C=C,valid
CCCCCCCCC/C=C/CC1CC(=O)OC1=O,train
NCCCN1CCN(CCCN)CC1,train
C=CC(C)(O)CCC=C(C)CCC=C(C)C,train
CCCCCCCCCCCCOCCO,train
CCCCCCCCCC(CC)c1ccc(S(=O)(=O)[O-])cc1,test
Cc1cc2c(C(C)C)c(O)c(O)c(C=O)c2c(O)c1-c1c(C)cc2c(C(C)C)c(O)c(O)c(C=O)c2c1O,train
CN(C(=O)CN(CCO)CC(=O)N(C)C(C)(C)Cc1ccccc1)C(C)(C)Cc1ccccc1,train
c1ccc(COCc2ccccc2)cc1,train
COc1ccccc1OCC(O)CO,train
CCCCCc1cc(O)cc(O)c1,valid
CCCCCCCC/C=C\CCCCCCCC(=O)OC,train
O=C(O)C(=O)O,train
O=C(O)c1cc(O)nc(O)n1,train
Cc1ccccc1C(OCCN(C)C)c1ccccc1,train
O=C1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,test
CN(C)Cc1cc(CN(C)C)c(O)c(CN(C)C)c1,train
IC(I)=C(I)I,train
O=P([O-])([O-])OP(=O)([O-])[O-],train
CC(C)NC[C@@H](O)COc1cccc2ccccc12,train
CC(=O)OCC(=O)[C@@]12OC(C)(C)O[C@@H]1C[C@H]1[C@@H]3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]12C,valid
CC(=O)OCC(=O)[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
CNC(=O)ON=C(CSC)C(C)(C)C,train
O=c1nn[nH]c2ccccc12,train
Nc1ccc2cc(S(=O)(=O)O)cc(O)c2c1,train
CCOC(=O)COc1cc(-n2ncc(C(F)(F)F)c(C)c2=O)c(F)cc1Cl,test
CCCCOC(=O)C(C)Oc1ccc(Oc2ccc(C(F)(F)F)cn2)cc1,train
COc1nn(C(=O)[N-]S(=O)(=O)c2ccccc2OC(F)(F)F)c(=O)n1C,train
CCCCCOC(=O)c1ccccc1C(=O)OCCCCC,train
CCCOC(=O)c1ccccc1C(=O)OCCC,train
CCc1ccc(C(=O)NN(C(=O)c2cc(C)cc(C)c2)C(C)(C)C)cc1,valid
c1nc[n-]n1,train
N#CCCSCCC#N,train
CCOC(=O)C1=C[C@]2(CC)CCCN3CCc4c(n1c1ccccc41)[C@@H]32,train
Cc1nnc2n1-c1ccc(Cl)cc1C(c1ccccc1Cl)=NC2,train
CCOc1ccc2c3c1O[C@H]1[C@@H](O)C=C[C@H]4[C@@H](C2)N(C)CC[C@]314,test
CC(=O)Oc1ccc2c3c1O[C@H]1[C@@H](OC(C)=O)C=C[C@H]4[C@@H](C2)N(C)CC[C@]314,train
CCC(=O)O[C@]1(c2ccccc2)CCN(C)C[C@H]1C,train
CC(C)Cn1cnc2c1c(=O)n(C)c(=O)n2C,train
Brc1c2ccccc2c(Br)c2ccccc12,train
Cc1nc2ccccc2c(=O)o1,valid
CCn1cc[n+](C)c1.COS(=O)(=O)[O-],train
C/C=C(\C)C=O,train
CN(C)c1ccc(CCO)cc1,train
O=C1c2ccccc2-c2ccccc21,train
CNC(=O)Oc1ccccc1C(C)C,test
CC(C)c1ccc(NC(=O)N(C)C)cc1,train
Cc1ccc(C(C)C)cc1O,train
CCOCCOCCOCCO,train
CC(Cl)C(Cl)Cl,train
CCN(CC)CC,valid
CCCCCCCCCCCCCCCC/C=C/C1CC(=O)OC1=O,train
CCOP(=S)(OCC)SCn1nnc2ccccc2c1=O,train
CC(C)Nc1nc(N)nc(Cl)n1,train
CCNc1nc(N)nc(Cl)n1,train
CCNc1nc(NC(C)C)nc(OC)n1,test
O=C1CCCCCCCCCCCCCC1,train
CCC(=O)C=Cc1ccc(OC)cc1,train
CC(C)(Cl)[N+](=O)[O-],train
O=[N+]([O-])c1cc(C(F)(F)F)ccc1Cl,train
CC1=C(O)C(=O)CO1,valid
C=CC(=C)CCCC(C)(C)OC(C)=O,train
C=CC(C)(O)CC/C=C(\C)CC,train
CCC(=O)Oc1ccc(/C(CC)=C(\CC)c2ccc(OC(=O)CC)cc2)cc1,train
CC(C)C1CCC(O)CC1,train
COc1cc(Cl)c(OC)cc1Cl,test
CC(C)CC=C(C=O)c1ccccc1,train
Clc1cccc2ccccc12,train
CCC(Cl)[N+](=O)[O-],train
CC(=O)OCC(C)OC(C)=O,train
CC(C)CCCC(C)CC=O,valid
CC(C)(C)c1cccc(O)c1,train
CC1=CCC(O)(C(C)C)CC1,train
C=C(C)C(=O)[O-],train
CC(CCO)CCc1ccccc1,train
CCOC(=O)C(C#N)=C(c1ccccc1)c1ccccc1,test
COc1ccc(C(=O)CC(=O)c2ccc(C(C)(C)C)cc2)cc1,train
C=CCCCCCCCCC(=O)OCC,train
O=C1NC(=O)c2cc([N+](=O)[O-])ccc21,train
O=C1OC(=O)c2cc([N+](=O)[O-])ccc21,train
O=c1[nH][nH]c(=O)c2cc([N+](=O)[O-])ccc12,valid
Nc1ccc([N+](=O)[O-])c(N)c1,train
O=[N+]([O-])c1ccc(NCCO)c(OCCO)c1,train
O=S(=O)([O-])c1ccc(/N=N/c2c(O)ccc3cc(S(=O)(=O)[O-])ccc23)cc1,train
CC(/C=C/C=C(\C)C(=O)O[C@@H]1O[C@H](CO[C@@H]2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)[C@@H](O)[C@H](O)[C@H]1O)=C\C=C\C=C(C)\C=C\C=C(/C)C(=O)O[C@@H]1O[C@H](CO[C@@H]2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)[C@@H](O)[C@H](O)[C@H]1O,train
O=C([O-])c1[nH]n(-c2ccc(S(=O)(=O)[O-])cc2)c(=O)c1/N=N/c1ccc(S(=O)(=O)[O-])cc1,test
CCN(CC)CCCl,train
COC(=O)c1ccc2cc(C(=O)OC)ccc2c1,train
Cc1cccc[n+]1[O-],train
CC[N+](CC)(CC(=O)Nc1c(C)cccc1C)Cc1ccccc1.O=C([O-])c1ccccc1,train
CCC(C)C1CCCCC1=O,valid
NC1CCN(c2cccc(Cl)n2)CC1,train
C#CCC(Cc1cnc2nc(N)nc(N)c2n1)c1ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc1,train
C/C=C1\NC(=O)[C@H]2CSSCCC=C[C@H](CC(=O)N[C@H](C(C)C)C(=O)N2)OC(=O)[C@H](C(C)C)NC1=O,train
CC1CCC(CO)CC1,train
CCS(=O)(=O)C(C)(C)S(=O)(=O)CC,test
NCCCC[C@H](NC(=O)[C@@H]1CCCN1C(=O)[C@@H]1CSSC[C@H](N)C(=O)N[C@@H](Cc2ccc(O)cc2)C(=O)N[C@@H](Cc2ccccc2)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CC(N)=O)C(=O)N1)C(=O)NCC(N)=O,train
CC(=O)SC[C@@H](C)C(=O)N1CCC[C@H]1C(=O)N[C@@H](Cc1ccccc1)C(=O)O,train
CCC(C)(C)OC(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCSC)C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](Cc1ccccc1)C(N)=O,train
Nc1c2c(nc3ccccc13)CCCC2O,train
N=C(N)NC(=O)Cc1c(Cl)cccc1Cl,valid
O=c1c2ccccc2[se]n1-c1ccccc1,train
O=C1NCN(c2ccccc2)C12CCN(CCCC(c1ccc(F)cc1)c1ccc(F)cc1)CC2,train
Cc1nc2c([nH]1)c(=O)n(C)c(=O)n2Cc1ccco1,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H](C4C=C[C@H]3C4)C1(Cl)C2(Cl)Cl,train
NC(=O)NC1NC(=O)NC1=O,test
C=CCO,train
C=CCCl,train
C=CCOCC1CO1,train
C=CCN=C=S,train
C=CCOC(=O)CC(C)C,valid
[S-]c1nc2ccccc2s1,train
CCCCNCC,train
Cc1[nH]c(=O)c(C#N)cc1-c1ccncc1,train
CN1CCN2c3ncccc3Cc3ccccc3C2C1,train
CC#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CCC4=C3[C@@H](c3ccc(N(C)C)cc3)C[C@@]21C,test
Cc1cccc2cccnc12,train
O=S([O-])CO,train
Cc1ccc2ncccc2c1,train
COC(=O)C(c1ccccc1)C1CCCCN1,train
NC(=O)CS(=O)C(c1ccccc1)c1ccccc1,valid
Oc1ccc(Cl)cc1,train
Cc1ccc(N)cc1,train
C=CC(=O)OCC(C)C,train
CCCBr,train
OCCCl,test
CCC#N,train
CCc1cc(Cc2cc(CC)cc(C(C)(C)C)c2O)c(O)c(C(C)(C)C)c1,train
CCCC(C)C1(CC)C(=O)N=C([S-])NC1=O,train
CCC(=O)OCCc1ccccc1,train
CC1(C)C(=O)NC(=O)N1CO,valid
C#C[C@]1(OC(C)=O)CC[C@H]2[C@@H]3CCC4=C[C@@H](OC(C)=O)CC[C@@H]4[C@H]3CC[C@@]21C,train
CCc1cccc2c3c([nH]c12)C(CC)(CC(=O)O)OCC3,train
CCN(CC)CCOc1ccc(C(=C(Cl)c2ccccc2)c2ccccc2)cc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
CCOC(=O)C(C)(C)Oc1ccc(Cl)cc1,train
O=C(O)CC(O)(CC(=O)O)C(=O)O,test
CC(C)(Oc1ccc(C2CC2(Cl)Cl)cc1)C(=O)O,train
Nc1ccccc1C(=O)OCC=Cc1ccccc1,train
CCCCCCCCCCCC[N+](C)(C)CC(=O)[O-],train
CCCCCCCCC(CO)CCCCCC,train
Cc1c(Cl)cccc1[N+](=O)[O-],valid
C=CCOCC(O)CCl,train
CCOC(=O)c1nc(C(Cl)(Cl)Cl)n(-c2ccc(Cl)cc2Cl)n1,train
Nc1cc([N+](=O)[O-])ccc1C(=O)O,train
CCC(C(=O)NC(N)=O)c1ccccc1,train
O=c1ssc(Cl)c1Cl,test
CCN1C(=CC=Cc2ccc3ccccc3[n+]2CC)C=Cc2ccccc21,train
N=C(N)NN=C(C=Cc1ccc([N+](=O)[O-])o1)C=Cc1ccc([N+](=O)[O-])o1,train
CCCCCCCCOC(=O)CCCCCCCCC(=O)OCCCCCCCC,train
O=C(OCCCCCCOC(=O)c1ccccc1)c1ccccc1,train
NC(=O)N(O)[C@@H]1COc2cc(OCc3ccccc3)ccc21,valid
CC(=O)c1ccc2c(c1)[C@H](NC(=O)c1ccc(F)cc1)[C@@H](O)C(C)(C)O2,train
CCc1cc(SCc2sc(-c3ccc(C(F)(F)F)cc3)nc2CN2CCN(c3ccc(OC)cc3)CC2)ccc1OCC(=O)O,train
CCCCN1C(=O)[C@@H]([C@H](O)C2CCCCC2)NC(=O)C12CCN(Cc1ccc(Oc3ccc(C(=O)O)cc3)cc1)CC2,train
Cc1oc(-c2ccccc2)nc1CCOc1ccc(C[C@H](Nc2ccccc2C(=O)c2ccccc2)C(=O)O)cc1,train
CNS(=O)(=O)c1cc(C(=O)N2CCC(CCN3C4CCC3CC(n3c(C)nc5ccccc53)C4)(c3cccc(F)c3)CC2)c(Cl)cc1F,test
N#Cc1ccc(N(CC(N)=O)CC(F)(F)F)cc1C(F)(F)F,train
CCN(C(=O)c1cc(C)cc(OC[C@H](C)Nc2ccncc2)c1)C(C)C,train
O=C(NCCCN1CCOCC1)c1ccc(Cl)cc1,train
CSCC[C@H](NC(=O)[C@H](Cc1ccc(OS(=O)(=O)O)cc1)NC(=O)[C@@H](N)CC(=O)O)C(=O)NCC(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCSC)C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](Cc1ccccc1)C(N)=O,train
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@H]2O,valid
Cc1ccc(O)c2ncccc12,train
CC(O)C(=O)O.CCCCCCCCCCCCCCNCCO,train
CNCCCC1c2ccccc2C=Cc2ccccc21,train
O=C(Nc1ccc(C(=O)O)c(O)c1)c1ccccc1,train
COc1ccccc1OC(=O)Oc1ccccc1OC,test
CC(=O)CCC=C(C)CC/C=C(\C)CC/C=C(\C)CCC=C(C)C,train
O=C(Nc1ccc(O)cc1)c1ccccc1O,train
CC(C)(C)C1(CCc2ccc(Cl)cc2)CO1,train
O=P([O-])(O)C(Sc1ccc(Cl)cc1)P(=O)([O-])O,train
CCCCCCCC/C=C\CCCCCCCC(=O)N(C)CC(=O)O,valid
CCc1ccccc1N,train
CCCCCCCCCCCCCCCC(=O)OCCC1CCN(CCCN2c3ccccc3Sc3ccc(S(=O)(=O)N(C)C)cc32)CC1,train
CS(=O)(=O)Nc1cc2occ(NC=O)c(=O)c2cc1Oc1ccccc1,train
NNc1nncc2ccccc12,train
CC(C)=CCC/C(C)=C/CC/C(C)=C/CC/C(C)=C/CC1=C(C)C(=O)c2ccccc2C1=O,test
O=S(=O)(OC[C@H]1O[C@H](O[C@]2(COS(=O)(=O)O[AlH3](O)O)O[C@H](COS(=O)(=O)O[AlH3](O)O)[C@@H](OS(=O)(=O)O[AlH3](O)O)[C@@H]2OS(=O)(=O)O[AlH3](O)O)[C@H](OS(=O)(=O)O[AlH3](O)O)[C@@H](OS(=O)(=O)O[AlH3](O)O)[C@@H]1OS(=O)(=O)O[AlH3](O)O)O[AlH3](O)O.O[AlH3](O)[AlH3](O)O.O[AlH3](O)[AlH3](O)O.O[AlH3](O)[AlH3](O)O.O[AlH3](O)[AlH3](O)O,train
CC(=O)OCCN(CCOC(C)=O)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2C#N)cc1,train
CCCCCCCCCCCCCCN(CCO)CCO,train
CC(=O)[C@H]1CC[C@H]2[C@@H]3CC[C@H]4C[C@H](O)CC[C@]4(C)[C@H]3CC[C@]12C,train
CCN(CC)CCC(=O)N1c2ccccc2Sc2ccc(C(F)(F)F)cc21,valid
CCCOC(=O)c1cc(O)c(O)c(O)c1,train
CC(O)CO,train
Brc1ccc(Oc2ccc(Br)cc2Br)c(Br)c1,train
O=C1CCC(c2ccccc2)(C2CCN(Cc3ccccc3)CC2)C(=O)N1,train
CCCc1cc(=O)[nH]c(=S)[nH]1,test
CCc1cnc(C(=O)O)c(C(=O)O)c1,train
CCO[Si](CCC#N)(OCC)OCC,train
CCO[Si](CCCN)(OCC)OCC,train
FC(F)(F)c1ccccc1,train
c1ccc2scnc2c1,valid
c1ccc2c(c1)ccc1ncccc12,train
CC(=O)OC(C(=O)c1ccccc1)c1ccccc1,train
CN(C)C/C=C(/c1ccc(Br)cc1)c1cccnc1,train
CI.CN1[C@H]2CC[C@@H]1CC(OC(=O)c1c[nH]c3ccccc13)C2,train
O=C(CCCN1CCC(O)(c2cccc(C(F)(F)F)c2)CC1)c1ccc(F)cc1,test
Cc1[nH]c(=O)c(C#N)cc1-c1ccc2nccn2c1,train
O=C(O)c1cc(O)c(O)c(O)c1,train
Cc1ccc(C)c(OCCCC(C)(C)C(=O)O)c1,train
CN(C)c1ccc(C(=C2C=CC(=[N+](C)C)C=C2)c2ccc(N(C)C)cc2)cc1,train
CC(=O)OC/C=C(\C)CCC=C(C)C,valid
C=C1C[C@]23C[C@@]1(O)CC[C@H]2[C@@]12C=C[C@H](O)[C@@](C)(C(=O)O1)[C@H]2[C@@H]3C(=O)O,train
BrCCCCBr,train
OC[C@H]1O[C@H](O[C@H]2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,train
O=C(Cc1ccccc1)OCCc1ccccc1,train
CC(=O)CCc1ccc(O)cc1,test
CC(C)CCCC(C)CCO,train
NNC(=O)CCCCC(=O)NN,train
O=C1CCCC(=O)O1,train
CCCCCCCCCC(=O)OCC,train
CCn1cc[n+](C)c1.O=S(=O)([O-])C(F)(F)F,valid
CCn1cc[n+](C)c1.N#CN=C=[N-],train
CCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
O=C(O)c1ccccc1O.OCCN(CCO)CCO,train
CCCC[N+]1(C)CCCC1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
COP(=O)([O-])OC.Cn1cc[n+](C)c1,test
O=S(=O)([O-])OOS(=O)(=O)[O-],train
O=[N+]([O-])[O-].[K+],train
CCCNCCC,train
CCC(O)OC(O)CC,train
Nc1nc(F)nc2c1ncn2[C@@H]1O[C@H](CO)[C@@H](O)[C@@H]1O,valid
O=C(Nc1ccccc1)Nc1ccccc1,train
OC1(c2ccc(Cl)c(C(F)(F)F)c2)CCN(CCCC(c2ccc(F)cc2)c2ccc(F)cc2)CC1,train
OCCN1CCN(CC/C=C2/c3ccccc3Sc3ccc(C(F)(F)F)cc32)CC1,train
CS(=O)(=O)O,train
C[Si](C)(C)C,test
CC1(c2ccc(Oc3ccccc3)cc2)OC(=O)N(Nc2ccccc2)C1=O,train
Nc1nc(=O)c2nc(CNc3ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc3)cnc2[nH]1,train
CCOC(=O)Cc1cccc2ccccc12,train
BrCC1CO1,train
O=C([O-])c1ccccc1-c1c2cc(Br)c(=O)c(Br)c-2oc2c(Br)c([O-])c(Br)cc12,valid
CCOC1Oc2ccc(OS(C)(=O)=O)cc2C1(C)C,train
CCOc1cc(C(C)(C)C)ccc1C1COC(c2c(F)cccc2F)=N1,train
Cc1cc(Cl)ccc1O,train
Cc1cc(O)c2c(c1)C(=O)c1cc(O)cc(O)c1C2=O,train
COC(=O)C1=C(C)NC(C)=C(C(=O)OC(C)C)C1c1cccc2nonc12,test
Cl[Zn]Cl,train
Cc1cc(C(=O)NNCc2ccccc2)no1,train
CC(C)COC(=O)c1ccc(N)cc1,train
O=[N+]([O-])O[C@@H]1CO[C@@H]2[C@@H](O)CO[C@H]12,train
CC(C)[N+](C)(CCC(C(N)=O)(c1ccccc1)c1ccccc1)C(C)C,valid
O=c1n(Cl)c(=O)n(Cl)c(=O)n1Cl,train
Nc1ccc2c(O)cc(S(=O)(=O)O)cc2c1,train
Cc1cc(S(=O)(=O)O)c(N)cc1Cl,train
Cc1ccc(N)c(S(=O)(=O)O)c1,train
CC(C)(C)c1ccccc1O,test
Cc1ccc(C(C)(C)C)c(O)c1,train
Cc1cc(N)c(S(=O)(=O)O)cc1Cl,train
C[C@]12CC[C@H]3[C@@H](CC[C@H]4C[C@@H]5S[C@@H]5C[C@@]43C)[C@@H]1CC[C@@H]2O,train
Cc1c(NC(=O)c2cccnc2)c(=O)n(-c2ccccc2)n1C,train
C[C@@H]1C[C@H]2[C@@H]3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)CO,valid
O=C(CCCN1CCC(n2c(=O)[nH]c3ccccc32)CC1)c1ccc(F)cc1,train
O=C(CCCN1CCN(c2ccccn2)CC1)c1ccc(F)cc1,train
CCCCCCCCCCCCCCCCOS(=O)(=O)[O-],train
CCCCOc1ccc(CC(=O)NO)cc1,train
CCCCc1c(C)nc(NCC)nc1OS(=O)(=O)N(C)C,test
CC(Cl)Cl,train
Clc1ccccc1Cl,train
Clc1ccc(Cl)cc1,train
Nc1ccc(-c2ccc(N)c(Cl)c2)cc1Cl,train
ClCCCl,valid
Oc1ccc(Cl)cc1Cl,train
CCCCOC(=O)CCC(=O)OCCCC,train
c1ccc2c(c1)oc1ccccc12,train
NCCN1CCNCC1,train
CCC(=O)[O-],test
CCN(CC)C(=O)c1cccc(C)c1,train
Nc1ccc(C(=O)Oc2ccccc2)c(O)c1,train
COCCCN,train
CC1(C)C(C=C(Cl)Cl)C1C(=O)OCc1cccc(Oc2ccccc2)c1,train
COc1nc(C)nc(NC(=O)NS(=O)(=O)c2ccccc2OCCCl)n1,valid
CC(C)N(C(=O)SCC(Cl)=C(Cl)Cl)C(C)C,train
Brc1ccc(Br)c(Br)c1,train
CCCCN(CCCC)CCCCCCN(CCCC)CCCC,train
CC(COCC(C)OC(=O)c1ccccc1)OC(=O)c1ccccc1,train
CCCCCCCCCC(CC)c1ccc(S(=O)(=O)O)cc1,test
CC(C)CCCCCCCOC(=O)CCCCC(=O)OCCCCCCCC(C)C,train
CC(C)(C)CC(C)(C)c1ccccc1O,train
CCCCCCCCCCCCc1ccccc1O,train
CCCCC(CC)CO[N+](=O)[O-],train
NC(=S)NC(N)=S,valid
N#C/N=C1\SCCN1Cc1ccc(Cl)nc1,train
CN1COCN(Cc2cnc(Cl)s2)C1=N[N+](=O)[O-],train
CC1=CC(=O)CC(C)(C)C1(O)/C=C/C(C)=C\C(=O)O,train
CC1=CC(=O)[N-]S(=O)(=O)O1,train
CCOC(C)OCC,test
CC1(C)C(C(=O)OC(C#N)c2cccc(Oc3ccccc3)c2)C1(C)C,train
COC(=O)c1c(Cl)c(Cl)c(C(=O)OC)c(Cl)c1Cl,train
CC(Cl)(Cl)C(=O)[O-],train
C=CC(O)CCCCC,train
C[Sn](Cl)(Cl)Cl,valid
C=CCS(=O)(=O)[O-],train
CCCC(C)c1ccccc1O,train
CC(C)CCOCc1ccccc1,train
Cc1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,train
O=C1O[Pt]2(N[C@@H]3CCCC[C@H]3N2)OC1=O,test
C[C@]12CC[C@H]3C(=CCc4cc(O)ccc43)[C@@H]1CCC2=O,train
CC(=O)[C@@]1(O)CC[C@H]2[C@@H]3C[C@H](C)C4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
CC1(C)O[C@@H]2C[C@H]3[C@@H]4C[C@H](F)C5=CC(=O)CC[C@]5(C)[C@H]4[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1,train
CCCCCCC1CCOC1=O,train
C[C@H](CCC(=O)NCC(=O)O)[C@H]1CC[C@H]2[C@H]3[C@H](C[C@H](O)[C@@]21C)[C@@]1(C)CC[C@@H](O)C[C@H]1C[C@H]3O,valid
CCC(C)(C)C1CCC(=O)CC1,train
O=P([O-])(O)OP(=O)([O-])O,train
C=CCCCCCCCCOC(C)=O,train
Cc1ccccc1C#N,train
CCCCCCCCCOC(C)=O,test
O=Cc1cc2ccccc2o1,train
CC(=O)c1c(C)cc(C(C)(C)C)cc1C,train
COC(=O)C=C(C)CCC=C(C)C,train
CC(=O)OCC=C(C)C,train
CCN(CC#N)c1cccc(C)c1,valid
Oc1cccc2cccnc12.Oc1cccc2cccnc12,train
N[C@@H](Cc1c[nH]c2ccc(O)cc12)C(=O)O,train
CC(=O)OC(C)(C)C,train
NC(=O)NO,train
COC(=O)OC,test
CC1CCCNC1,train
CCc1ccc(C)cc1,train
O=C(O)CNC(=O)c1ccc([N+](=O)[O-])cc1,train
O=[N+]([O-])CCO,train
CN(CCCl)CCCl,valid
C=C(Cl)Cl,train
N=C(N)S(=O)O,train
O=C(O)c1nc(Cl)ccc1Cl,train
Cc1ccc(N)nc1,train
Cc1ccc(C(C)c2ccc(C)c(C)c2)cc1C,test
C=COC(C)=O,train
C=Cc1cccc(C)c1,train
C=CC1CC=CCC1,train
COP(=O)(OC)OC,train
CC(C)(C)c1ccc(CCOc2ncnc3ccccc23)cc1,valid
O=C(NC(=O)c1c(F)cccc1F)Nc1cc(Cl)c(OC(F)(F)C(F)C(F)(F)F)cc1Cl,train
O=C(NC(=O)c1ccccc1Cl)Nc1ccc(OC(F)(F)F)cc1,train
O=C(O)c1ccc(-n2nc(-c3ccccc3O)nc2-c2ccccc2O)cc1,train
CO[C@@]12CC[C@@]3(C[C@@H]1[C@](C)(O)C(C)(C)C)[C@H]1Cc4ccc(O)c5c4[C@@]3(CCN1CC1CC1)[C@H]2O5,train
COc1ccc2c3c1O[C@H]1[C@@H](O)C=C[C@H]4[C@@H](C2)NCC[C@]314,test
Cc1ccc(C)c(C)c1,train
CCC(C)(O)CCC=C(C)C,train
O=P(O)(O)OC(Cn1cncn1)(Cn1cncn1)c1ccc(F)cc1F,train
C[C@H]1[C@@H]2Cc3ccc(O)cc3[C@@]1(C)CCN2C.C[C@H]1[C@@H]2Cc3ccc(O)cc3[C@@]1(C)CCN2C,train
C[C@@H]1[C@H]2Cc3ccc(O)cc3[C@]1(C)CCN2C,valid
CC(=O)OC(C)(C)Cc1ccccc1,train
CC(=O)NCCO,train
CCCC(=O)O[C@]1(C(=O)COC(=O)CC)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3[C@@H](O)C[C@@]21C,train
CCNc1nc(NC(C)C(C)C)nc(SC)n1,train
CON(C)C(=O)Nc1ccc(Cl)c(Cl)c1,test
CCCCOCCOC(=O)CCCCCCCCC(=O)OCCOCCCC,train
O=C1c2c(O)ccc(O)c2C(=O)c2c(NCCNCCO)ccc(NCCNCCO)c21,train
COc1nc(C)nc(Cl)c1NC1=NCCN1,train
CCOC(=O)[N-]c1c[n+](N2CCOCC2)no1,train
COc1ccccc1N1CCN(CC(O)COc2cccc3ccccc23)CC1,valid
CC12CC3CC(C)(C1)CC(N)(C3)C2,train
CNCCC(Oc1ccccc1OC)c1ccccc1,train
C[C@@H](Cc1ccc(O)c(O)c1)[C@H](C)Cc1ccc(O)c(O)c1,train
Cc1ncc(CNC(=O)N(CCCl)N=O)c(N)n1,train
Cc1c(N)c(=O)n(-c2ccccc2)n1C,test
CN(C)CCN(Cc1ccc(Cl)cc1)c1ccccn1,train
C[C@H](CN1CCCC1)C(=O)c1ccc(C(F)(F)F)cc1,train
Cc1nccn1CC1CCc2c(c3ccccc3n2C)C1=O,train
CCCCC1C(=O)NC(=O)N(C2CCCCC2)C1=O,train
O=C1CC[C@H](C(=O)N2CCCCC2)N1,valid
C=CC[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CCC4=C3C=C[C@@]21C,train
C=CCc1ccccc1O,train
N#Cc1cc(Br)c(O)c(Br)c1,train
N#Cc1cc(I)c(O)c(I)c1,train
Oc1ccc(/N=N/c2ccccc2)cc1,test
C=C(C)C(=O)OCCCCCCCCCCCC,train
CCCCCCCCCCCCCCCC(=O)OC(C)C,train
NCCCCCCNCCCCCCN,train
COc1c(C)c2c(c(O)c1C/C=C(\C)CCC(=O)OCCN1CCOCC1)C(=O)OC2,train
CC(C)(C)NCC(O)COc1cccc2c1C[C@H](O)[C@H](O)C2,valid
C=C1CC[C@@]2(O)[C@H]3Cc4ccc(O)c5c4[C@@]2(CCN3CC2CC2)[C@H]1O5,train
C=C(C)C(=O)OCCOc1ccccc1,train
C=CCN1CC[C@]23c4c5ccc(O)c4O[C@H]2C(=O)CC[C@@]3(O)[C@H]1C5,train
CC(CC(C)C)=NO,train
COc1ccc(COC(C)=O)cc1,test
CC(C)(O)C(=O)c1ccc(OCCO)cc1,train
C=CC(=O)OCCC#N,train
O=C1CCCCCCCCCCCC(=O)OCCO1,train
OCCN1CCNCC1,train
CC(C)(O)CCc1ccccc1,valid
CCCCc1ccc(N)cc1,train
CCCCCCCCCc1ccc(O)cc1,train
CN(C)Cc1ccccc1,train
CO[Si](CCCNCCC[Si](OC)(OC)OC)(OC)OC,train
Cc1ccccc1OP(=O)(Oc1ccccc1C)Oc1ccccc1C,test
c1coc(CNc2[nH]cnc3ncnc2-3)c1,train
Cc1c(C)c2c(c(C)c1OC(=O)CCC(=O)O)CC[C@@](C)(CCC[C@H](C)CCC[C@H](C)CCCC(C)C)O2,train
O=C(Oc1ccccc1)c1cccc(C(=O)Oc2ccccc2)c1,train
Cc1c(N=C=O)cccc1N=C=O,train
O=C1CCCCC1,valid
OC[C@H]1O[C@@H]2O[C@@H]3[C@@H](CO)O[C@H](O[C@@H]4[C@@H](CO)O[C@H](O[C@@H]5[C@@H](CO)O[C@H](O[C@@H]6[C@@H](CO)O[C@H](O[C@@H]7[C@@H](CO)O[C@H](O[C@@H]8[C@@H](CO)O[C@H](O[C@H]1[C@H](O)[C@H]2O)[C@H](O)[C@H]8O)[C@H](O)[C@H]7O)[C@H](O)[C@H]6O)[C@H](O)[C@H]5O)[C@H](O)[C@H]4O)[C@H](O)[C@H]3O,train
O=C1c2ccccc2C(=O)c2c(O)ccc(O)c21,train
CCCCCCCCCCO[C@@H]1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,train
C=C(C)C1CCC(C)(O)CC1,train
C=C[Si](OCC)(OCC)OCC,test
c1csc(-c2ccc(-c3cccs3)s2)c1,train
O=NN([O-])c1ccccc1,train
O=S(=O)([O-])NC1CCCCC1,train
N#CNC(=N)N,train
N=c1ccn2c(n1)O[C@H]1[C@H](O)[C@@H](CO)O[C@H]12,valid
CC(CO)c1ccccc1,train
Nc1ccc2c(c1)Cc1ccccc1-2,train
CCCCCCCCCCCC[N+](C)(C)CCCS(=O)(=O)[O-],train
OCc1c2ccccc2cc2ccccc12,train
S=c1nccc[nH]1,test
CCn1cc[n+](C)c1.F[B-](F)(F)F,train
CC(=O)C(=O)CC(C)C,train
CCCC[n+]1ccccc1C,train
CCCCN(CC)C(=O)SCCC,train
CC(C)NCC(O)COc1cccc2ccccc12,valid
Cc1ncc([C@@H](CCCCCCc2ccc3c(n2)NCCC3)CC(=O)O)cn1,train
CC(C)(O)c1cnn2c(-c3ccc(F)c(-c4c(F)cccc4C#N)c3)cnc2n1,train
CCCCc1ccc2c(n1)[C@@H](c1ccc(OC)cc1C[C@H](C)C(=O)[O-])[C@H](C(=O)[O-])[C@H]2c1ccc2c(c1)OCO2,train
CCOc1cccc(-n2cc(C(=O)N3CCN(c4cc(C(=O)[O-])c5ccccc5c4)CC3)nc2-c2ccc(C)cc2)c1,train
Fc1ccccc1[C@@H](F)CO[C@H]1CC[C@H](Nc2[nH]cnc3nncc2-3)CC1,test
CO[C@@H]1COCC[C@@H]1[NH2+][C@@H]1CC[C@@](C(=O)N2CCc3ncc(C(F)(F)F)cc3C2)(C(C)C)C1.O=C([O-])CCC(=O)O,train
OC12N=C(c3ccccc3)SC1Cc1cc(Cl)ccc12,train
CN(C)C(=O)NC1(c2ccccc2)CCN(CC[C@@]2(c3ccc(F)c(F)c3)CN(C(=O)c3ccccc3)CCO2)CC1,train
Cc1cccc(C)c1NC1=NCCCS1,train
CN1[C@H]2CC[C@@H]1C[C@H](OC(=O)C(CO)c1ccccc1)C2,valid
Cl[Ni]Cl,train
CCNc1nc(Cl)nc(NC(C)C)n1,train
O=S(=O)(O)c1ccc(O)cc1,train
O=C(CCN1CCN2CCCC2C1)N1c2ccccc2Sc2ccc(Cl)cc21,train
COc1ccc(CCN2CCC(Nc3nc4ccccc4n3Cc3ccc(F)cc3)CC2)cc1,test
NC(=O)NS(=O)(=O)c1ccc(N)cc1,train
CCN(CC)CCNC(=O)COc1ccc(Cl)cc1,train
COc1ccc(CN2CCN(C(c3ccc(F)cc3)c3ccc(F)cc3)CC2)c(OC)c1OC,train
CC(C)(C)NCC(O)COc1ccc(NC(=O)NC2CCCCC2)cc1,train
CCOC(=O)C1=C(C)NC(C)=C(C(=O)OCC)C1c1ccccc1/C=C/C(=O)OC(C)(C)C,valid
Nc1ncn([C@@H]2O[C@H](CO)[C@@H](O)[C@H]2O)c(=O)n1,train
CCP(CC)(CC)=[Au]S[C@H]1O[C@@H](COC(C)=O)[C@H](OC(C)=O)[C@@H](OC(C)=O)[C@@H]1OC(C)=O,train
CN(C)c1ccc(C(=N)c2ccc(N(C)C)cc2)cc1,train
CCOP(=S)(Cl)OCC,train
COP(=S)(Cl)OC,test
CO[Si](CCCOCC1CO1)(OC)OC,train
C=CCc1cc(S(=O)(=O)c2ccc(O)c(CC=C)c2)ccc1O,train
CCCCCCOC(=O)C(C)CC,train
Cc1ccc(S(=O)(=O)Oc2ccc(N=Nc3ccc(-c4ccc(N=Nc5c(O)ccc6cc(S(=O)(=O)[O-])cc(S(=O)(=O)[O-])c56)c(C)c4)cc3C)cc2)cc1,train
[Cr+2].c1cc[cH-]c1.c1cc[cH-]c1,valid
CCOC(=O)[C@H](CCc1ccccc1)N[C@@H](C)C(=O)N1Cc2ccccc2C[C@H]1C(=O)O,train
CC1=CCC(C(C)C)C=C1,train
Cn1cc(C(=O)[C@@H]2CCc3[nH]cnc3C2)c2ccccc21,train
ON=C1C=CC(=NO)C=C1,train
C[C@@H]1[C@@H]2Cc3ccc(O)cc3[C@@]1(C)CCN2CCc1ccccc1,test
CC(C)CCCCCCc1ccc(O)cc1,train
c1coc(-c2nc3ccccc3[nH]2)c1,train
O=C1OCCN1/N=C/c1ccc([N+](=O)[O-])o1,train
Cl[Sb](Cl)Cl,train
CCn1cc(C(=O)O)c(=O)c2cc3c(cc21)OCO3,valid
CC(C)NNC(=O)c1ccncc1,train
O=[N+]([O-])c1ccc(Nc2ccc(Nc3ccccc3)c(S(=O)(=O)[O-])c2)c([N+](=O)[O-])c1,train
COc1ccc2[nH]c(S(=O)Cc3ncc(C)c(OC)c3C)nc2c1,train
COCC(C)N,train
O=S(=O)([O-])c1cc(S(=O)(=O)[O-])c2c(/N=N/c3ccccc3)c(O)ccc2c1,test
COc1cc(/C=C/C(=O)O[C@H]2CC[C@]34C[C@]35CC[C@]3(C)[C@@H]([C@H](C)CCC=C(C)C)CC[C@@]3(C)[C@@H]5CC[C@H]4C2(C)C)ccc1O,train
O=C1Nc2ccc(Cl)cc2C(c2ccccc2)=NC1O,train
CNC(=O)ON=C(SC)C(=O)N(C)C,train
COc1ccc(CO)cc1,train
Cc1ccc2c(c1)[C@]13CCCC[C@@H]1[C@H](C2)N(C)CC3,valid
O=c1[nH]c(=O)n([C@H]2C[C@H](O)[C@@H](CO)O2)cc1Br,train
CN(C)Cc1ccccc1O,train
CC(=O)N(CC(O)CO)c1c(I)c(C(=O)NCCO)c(I)c(C(=O)NCC(O)CO)c1I,train
CC(C)(C(=O)O)c1ccc(C(O)CCCN2CCC(C(O)(c3ccccc3)c3ccccc3)CC2)cc1,train
COc1ncc(F)c2nc(S(=O)(=O)Nc3c(F)cccc3F)nn12,test
CCOC(=O)Nc1ccc2c(c1)N(C(=O)CN(C)C)c1ccccc1CC2,train
CCN(CCO)CCn1c(Cc2ccccc2)nc2c1c(=O)n(C)c(=O)n2C,train
C=CC1(C)CC(OC(=O)CO)C2(C)C(C)CCC3(CCC(=O)C32)C(C)C1O,train
C=C1CC2C3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)COC(C)=O,train
CCCCCCCCCCCCCC[P+](CCCCCC)(CCCCCC)CCCCCC,valid
CCCCCCn1cc[n+](C)c1,train
CCCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
CCCCCCCCn1cc[n+](C)c1.O=S(=O)([O-])C(F)(F)F,train
Clc1cc(Cl)cc(Cl)c1,train
Clc1cccc(Cl)c1Cl,test
O=C(CCl)c1ccc(Cl)cc1Cl,train
C=CC1=C(C(=O)O)N2C(=O)[C@@H](NC(=O)/C(=N/OCC(=O)O)c3csc(N)n3)[C@H]2SC1,train
N[C@@H](C(=O)N[C@@H]1C(=O)N2C(C(=O)O)=C(CSc3c[nH]nn3)CS[C@H]12)c1ccc(O)cc1,train
Cc1nnc(SCC2=C(C(=O)O)N3C(=O)[C@@H](NC(=O)Cn4cnnn4)[C@H]3SC2)s1,train
NS(=O)(=O)c1cc(C(=O)O)cc(N2CCCC2)c1Oc1ccccc1,valid
CC(=O)c1ccc2c(c1)N(CCCN1CCC(CCO)CC1)c1ccccc1S2,train
CN1CCN(CC(=O)N2c3ccccc3C(=O)Nc3cccnc32)CC1,train
CCCc1ccc(O)cc1,train
CCC(CO)(CO)CO,train
CCC(C)N,test
Cc1cccc(Nc2ncccc2C(=O)O)c1C,train
Cc1cccc(C)c1Nc1ncccc1C(=O)O,train
O=C(NCCO[N+](=O)[O-])c1cccnc1,train
Cc1cccc(C(C)c2c[nH]cn2)c1C,train
CCOc1ccccc1O[C@H](c1ccccc1)[C@H]1CNCCO1,valid
COC(C)(C)OC,train
CC(CO)(CO)CO,train
CCCCCCOc1ccc(N)cc1,train
O=C(NCCc1c[nH]c2ccccc12)c1cccnc1,train
CC[n+]1c(-c2ccccc2)c2cc(N)ccc2c2ccc(N)cc21,test
CN(C)c1nc(N(C)C)nc(N(C)C)n1,train
Nc1ccc(-c2ccc(F)cc2)cc1,train
CCC[N+](=O)[O-],train
CC(C)[N+](=O)[O-],train
O=C(O)CC[N+](=O)[O-],valid
O=[N+]([O-])c1ccc2ccc3cccc4ccc1c2c34,train
C=CCN(C1=NCCN1)c1c(Cl)cccc1Cl,train
Oc1ncnc2[nH]ncc12,train
C=CC[C@]1(O)CC[C@H]2[C@@H]3CCC4=CCCC[C@@H]4[C@H]3CC[C@@]21C,train
CC(=O)[C@H]1CC[C@H]2[C@@H]3CC[C@H]4C[C@H](O)CC[C@]4(C)[C@H]3C(=O)C[C@]12C,test
C=C(CC)C(=O)c1ccc(OCC(=O)O)c(Cl)c1Cl,train
Clc1cc(Cl)c(Cl)c(Cl)c1,train
CCCOC/C(=N\c1ccc(Cl)cc1C(F)(F)F)n1ccnc1,train
CC1CS(=O)(=O)CCN1N=Cc1ccc([N+](=O)[O-])o1,train
C[C@H](NCCc1ccc(O)cc1)[C@H](O)c1ccc(O)cc1,valid
Cc1cccc(CN2CCN(C(c3ccccc3)c3ccc(Cl)cc3)CC2)c1,train
CC[C@@H](CO)NCCN[C@@H](CC)CO,train
N=C(N)NCC1COC2(CCCCC2)O1.N=C(N)NCC1COC2(CCCCC2)O1,train
CCN(CC)C(C)CN1c2ccccc2Sc2ccccc21,train
Cc1cc2c(cc1S(N)(=O)=O)S(=O)(=O)CCC2,test
CC(C)(C)NCC(O)c1cc(O)cc(O)c1.CC(C)(C)NCC(O)c1cc(O)cc(O)c1,train
CC(C=O)Cc1ccc(C(C)(C)C)cc1,train
CC(C)c1c(CN(C)C(C)Cc2ccccc2)n(C)n(-c2ccccc2)c1=O,train
O=C(Cn1ccnc1[N+](=O)[O-])NCCO,train
NCC(CC(=O)O)c1ccc(Cl)cc1,valid
CCCCN(CC)c1c([N+](=O)[O-])cc(C(F)(F)F)cc1[N+](=O)[O-],train
CCCCC(O)(Cn1cncn1)c1ccc(Cl)cc1Cl,train
Nc1ccc(/N=N/c2ccc(-c3ccc(/N=N/c4c(S(=O)(=O)[O-])cc5cc(S(=O)(=O)[O-])c(/N=N/c6ccccc6)c(O)c5c4N)cc3)cc2)c(N)c1,train
O=[Bi]Cl,train
CC(C)(c1ccc(O)cc1)c1ccc(O)cc1,test
CCCCCCCC/C=C\CCCCCCCC(=O)OCCOC(=O)CCCCCCC/C=C\CCCCCCCC,train
CCCCCCCC/C=C\CCCCCCCCOCCO,train
CCCCCCCC/C=C\CCCCCCCC(=O)OCCO,train
O=C1C=CC(=O)N1c1ccc(Cc2ccc(N3C(=O)C=CC3=O)cc2)cc1,train
Oc1c(I)cc(I)c2cccnc12,valid
CC(=O)N(CC(O)CO)c1c(I)c(C(=O)NCC(O)CO)c(I)c(C(=O)NCC(O)CO)c1I,train
CC1=C(CC(=O)O)c2cc(F)ccc2/C1=C\c1ccc(S(C)=O)cc1,train
O=S([O-])CNc1ccc(S(=O)(=O)c2ccc(NCS(=O)[O-])cc2)cc1,train
Nc1ccc(S(N)(=O)=O)cc1,train
O=C1OC(c2ccc(O)c(S(=O)(=O)[O-])c2)(c2ccc(O)c(S(=O)(=O)[O-])c2)c2c(Br)c(Br)c(Br)c(Br)c21,test
CCC(Cc1c(I)cc(I)c(N)c1I)C(=O)O,train
C[C@H](O)C(=O)Nc1c(I)c(C(=O)NC(CO)CO)c(I)c(C(=O)NC(CO)CO)c1I,train
CCOC(=O)Oc1c(OC)cc(C(=O)O[C@@H]2C[C@@H]3CN4CCc5c([nH]c6cc(OC)ccc56)[C@H]4C[C@@H]3[C@H](C(=O)OC)[C@H]2OC)cc1OC,train
CNS(=O)(=O)Cc1ccc2[nH]cc(CCN(C)C)c2c1,train
O=C([O-])CCC(=O)OC[C@@H](NC(=O)C(Cl)Cl)[C@H](O)c1ccc([N+](=O)[O-])cc1,valid
CC1=CC2C3CC(C=C3C)C2C1,train
Nc1ccc2cc3ccc(N)cc3nc2c1.Nc1ccc2cc3ccc(N)cc3nc2c1,train
C/C(=C\C(C)CC(C)CC(C)C)C1CC(=O)OC1=O,train
O=C(O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
[Br-].[Na+],test
COc1cc2c(c(OC)c1OC)-c1ccc(OC)c(=O)cc1[C@@H](NC(C)=O)CC2,train
CC(CO)(CO)[N+](=O)[O-],train
CCC(C)(C)O,train
[Co+2].c1cc[cH-]c1.c1cc[cH-]c1,train
CC(C)Oc1ccccc1O,valid
Cc1ccc2c(c1)C(=O)c1ccccc1C2=O,train
CC12CCC(CC1=O)C2(C)C,train
COC(OC)C(C)c1ccccc1,train
SC1CCCC1,train
O=C1[N-]S(=O)(=O)c2ccccc21.O=C1[N-]S(=O)(=O)c2ccccc21,test
COc1cc(C)ccc1O,train
O=C1OC(=O)C2C3C=CC(C3)C12,train
CC(CC=O)CC(C)(C)C,train
CC1(C)OC[C@@H]2O[C@@]3(C(=O)O)OC(C)(C)O[C@H]3[C@@H]2O1,train
C1CCN(CN2CCCCC2)CC1,valid
CCCCCCCCCCCCCCCCCCOS(=O)(=O)[O-],train
Clc1ccc2[nH]nnc2c1,train
NC(=O)c1ccccc1[N+](=O)[O-],train
NC(=O)c1cccc([N+](=O)[O-])c1,train
O=C(O)c1cccc([N+](=O)[O-])c1,test
O=C(O)c1ccccc1[N+](=O)[O-],train
CN(C)c1ccc(C(c2ccccc2)c2ccc(N(C)C)cc2)cc1,train
COc1cc(C=CC(=O)N2CCN(CC(=O)N3CCCC3)CC2)cc(OC)c1OC,train
Nc1cccc(S(=O)(=O)O)c1,train
CCN(CC)C(=O)Nc1ccc(OCC(O)CNC(C)(C)C)c(C(C)=O)c1,valid
CCCCCCCCCCOc1cc2c(O)c(C(=O)OCC)cnc2cc1OCC,train
CC(C)OC(=O)Nc1ccc2[nH]c(-c3cscn3)nc2c1,train
CC1(C)CCC(C)(C)c2cc(NC(=O)c3ccc(C(=O)O)cc3)ccc21,train
CO[C@@H]1[C@@H](O[C@@H]2O[C@H](C)[C@@H](O[C@H]3C[C@@](C)(O)[C@@H](OC(=O)CC(C)C)[C@H](C)O3)[C@H](N(C)C)[C@H]2O)[C@@H](CC=O)C[C@@H](C)C(=O)C=CC2OC2C[C@@H](C)OC(=O)C[C@H]1OC(C)=O,train
CC[C@H]1NC(=O)[C@@H](NC(=O)c2ncccc2O)C(C)OC(=O)[C@H](c2ccccc2)NC(=O)C2CC(=O)CCN2C(=O)[C@H](Cc2ccccc2)N(C)C(=O)[C@@H]2CCCN2C1=O,test
Cc1nc(NS(=O)(=O)c2ccc(N)cc2)nc(C)c1Br,train
CCCCCCCCCCCCCOP(=O)(O)O,train
O=C([O-])c1ccccc1C(=O)O,train
N#C/C=C/C#N,train
C=C1CC[C@H](O)C/C1=C/C=C1\CCC[C@@]2(C)[C@H]1CC[C@@H]2[C@H](C)CCCC(C)C,valid
Cc1ccc(Nc2ccc(O)c3c2C(=O)c2ccccc2C3=O)cc1,train
COc1ccc(C(O)(c2cncnc2)C2CC2)cc1,train
CCCC(=O)Nc1ncnc2c1ncn2[C@@H]1O[C@@H]2COP(=O)(O)O[C@H]2[C@H]1OC(=O)CCC,train
CC(C)NC(C)C,train
CC(C)N=C=NC(C)C,test
CC(C)CC(=O)CC(C)C,train
CC(=O)C=Cc1ccco1,train
COc1cc(CC(C)N)cc2c1OCO2,train
CC(Cc1ccc2c(c1)OCO2)NO,train
CCc1cc(OC)c(C[C@H](C)N)cc1OC,valid
CCc1cc(OC)c(C[C@@H](C)N)cc1OC,train
COc1ccc(OC)c(CC(C)N)c1,train
CC1(C)C2CC[C@]3(C)[C@H](C(=O)C=C4[C@@H]5C[C@@](C)(C(=O)O)CC[C@]5(C)CC[C@]43C)[C@@]2(C)CC[C@@H]1O[C@H]1O[C@H](C(=O)[O-])[C@@H](O)[C@H](O)[C@H]1O[C@@H]1O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]1O,train
COc1cc(C[C@@H](C)N)c(OC)cc1C,train
O=C(NC(O)C(Cl)(Cl)Cl)NC(O)C(Cl)(Cl)Cl,test
CCNC(C)Cc1ccc2c(c1)OCO2,train
CNC(C)Cc1ccc2c(c1)OCO2,train
CC=Cc1ccc(OC)cc1,train
C/C=C/c1ccc(OC)cc1,train
CCC1(CCC(C)C)C(=O)NC(=O)NC1=O,valid
CC[C@@H](N)CO,train
OC[C@@H](O)[C@H]1O[C@@H]2O[C@H](C(Cl)(Cl)Cl)O[C@@H]2[C@H]1O,train
CC=C(CC)C(=O)NC(N)=O,train
NNCCc1ccccc1,train
c1ccc(OCC2CO2)cc1,test
NNc1ccccc1,train
CNC[C@H](O)c1cccc(O)c1,train
CC(O)CN(CC(C)O)CC(C)O,train
NC(Cc1c[nH]c2ccccc12)C(=O)O,train
N[C@@H](Cc1c[nH]c2ccccc12)C(=O)O,valid
C=CC(=O)OCC(C)O,train
COC1=CC(=O)O[C@@H](/C=C/c2ccccc2)C1,train
Nc1cc(S(=O)(=O)O)ccc1S(=O)(=O)O,train
COc1ccc(N)c([N+](=O)[O-])c1,train
CC(C)=CCCC(C)C=O,test
CN1C(=O)CN=C(c2ccccc2)c2cc([N+](=O)[O-])ccc21,train
C=CCOC(=O)CCC1CCCCC1,train
Cc1cn([C@H]2C=C[C@@H](CO)O2)c(=O)[nH]c1=O,train
CC(C)CCOC(=O)CC(C)C,train
CC1=CC[C@H](CC=O)C1(C)C,valid
O=C(CCNNC(=O)c1ccncc1)NCc1ccccc1,train
COC(=O)C1=C(C)NC(C)=C(C(=O)OCCN(C)Cc2ccccc2)C1c1cccc([N+](=O)[O-])c1,train
C(=NC1CCCCC1)=NC1CCCCC1,train
CC(C)(C)CC(C)(C)c1ccc(OCCOCC[N+](C)(C)Cc2ccccc2)cc1,train
OCc1cccnc1,test
O=C(OCC(COC(=O)c1cccnc1)(COC(=O)c1cccnc1)COC(=O)c1cccnc1)c1cccnc1,train
CCOC(=O)C1=C(COCCN)NC(C)=C(C(=O)OC)C1c1ccccc1Cl.O=S(=O)(O)c1ccccc1,train
CC(C=O)Cc1ccc2c(c1)OCO2,train
CCCCN(CCCC)C(=O)N(CCCC)CCCC,train
OC1OCCOC1O,valid
CC(C)(COC(=O)c1ccccc1)COC(=O)c1ccccc1,train
Clc1ccc(C(c2ccc(Cl)cc2)C(Cl)Cl)cc1,train
Clc1ccc(C(c2ccccc2Cl)C(Cl)Cl)cc1,train
Nc1ccc(S(=O)(=O)c2ccc(N)cc2)cc1,train
CN(C)NC(=O)CCC(=O)O,test
Clc1ccc(C(c2ccc(Cl)cc2)C(Cl)(Cl)Cl)cc1,train
ClC(Cl)=C(c1ccc(Cl)cc1)c1ccc(Cl)cc1,train
C[C@]12CC[C@H]3[C@@H](CC=C4C[C@@H](O)CC[C@@]43C)[C@@H]1CCC2=O,train
CC(=O)OCC(=O)[C@@]12N=C(C)O[C@@H]1C[C@H]1[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@H]3[C@@H](O)C[C@@]12C,train
O=C1NC(=O)[C@@H]2CC=CC[C@H]12,valid
OCCCCC(O)CO,train
Nc1ccc(O)c(C(=O)O)c1,train
CCCCCCCCOS(=O)(=O)[O-],train
CN[C@H]1C[C@@H](N)[C@H](O)[C@@H](O[C@@H]2O[C@H](CO)[C@H](O)[C@@H]3OC4(O[C@H]23)O[C@H](C(N)CO)[C@H](O)[C@H](O)[C@H]4O)[C@@H]1O,train
CCCCCCCCCCOS(=O)(=O)[O-],test
Cc1cccn2c(=O)c(-c3nnn[n-]3)cnc12,train
Nc1ncnc(Nc2ccccc2)n1,train
CCC(=O)OC(OP(=O)(CCCCc1ccccc1)CC(=O)N1C[C@H](C2CCCCC2)C[C@H]1C(=O)[O-])C(C)C,train
C[C@]12CC[C@H]3[C@@H](CCC4=C(Cl)C(=O)CC[C@@]43C)[C@@H]1CC[C@@H]2O,train
COc1cc(C(=S)N2CCOCC2)cc(OC)c1OC,valid
CC(=O)O[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=O)CCC4=C3C=C[C@]12C,train
CCOc1ccc(NC(=S)Nc2ccc(OCC)cc2)cc1,train
COC(=O)C1=C(C#N)NC(C)=C(C(=O)OC(C)C)C1c1cccc([N+](=O)[O-])c1,train
CCCCCCCCCC(=O)O[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]12C,train
CCOc1ccccc1OCCN[C@H](C)Cc1ccc(OC)c(S(N)(=O)=O)c1,test
COc1cccc(OC)c1C(=O)O,train
CCCCP(CCCC)CCCC,train
COc1ccc2cc(S(=O)(=O)N[C@H](CC(=O)N[C@H](Cc3ccc(CN4[C@@H](C)CCC[C@H]4C)cc3)C(=O)N(C)C(C)C)c3ccc4c(c3)OCO4)ccc2c1,train
CO/N=C(/C(=O)N[C@@H]1C(=O)N2C(C(=O)[O-])=C(COC(C)=O)CS[C@H]12)c1csc(N)n1,train
CCOC(=N)CC(=O)OCC,valid
C=C1[C@@H](n2cnc3c(=O)[nH]c(N)nc32)C[C@H](O)[C@H]1CO,train
C[C@@H]1CC(=O)NN=C1c1ccc(NN=C(C#N)C#N)cc1,train
Nc1nc(NC2CC2)c2ncn([C@@H]3C=C[C@@H](CO)C3)c2n1,train
CNCC[C@H](Oc1cccc2ccccc12)c1cccs1,train
C#CCOC(C(=O)OCCN(C)C)(c1ccccc1)c1ccccc1,test
O=C(O)C(=O)Cc1ccccc1,train
CCOC(=O)C(SP(=S)(OC)OC)c1ccccc1,train
CCOP(=O)(N=C1SCCS1)OCC,train
O=C1[C@H]2CCCC[C@H]2C(=O)N1CCCCN1CCN(c2nsc3ccccc23)CC1,train
O=C([O-])C[C@H](O)CC(O)/C=C/c1c(C2CC2)nc2ccccc2c1-c1ccc(F)cc1.O=C([O-])C[C@H](O)C[C@H](O)/C=C/c1c(C2CC2)nc2ccccc2c1-c1ccc(F)cc1,valid
CC(=O)S[C@H](C(=O)N[C@H]1Cc2ccccc2[C@H]2CCC[C@@H](C(=O)O)N2C1=O)C(C)C,train
CCc1c(C(=O)NN2CCCCC2)nn(-c2ccc(Cl)cc2Cl)c1-c1ccc(Br)cc1,train
Brc1ccccc1-c1ccccc1,train
Brc1ccccc1,train
OCCN(CCO)c1nc(N2CCCCC2)c2nc(N(CCO)CCO)nc(N3CCCCC3)c2n1,test
CC(C)(Oc1ccc(Cl)cc1)C(=O)O,train
Cc1cc(C(C#N)c2ccc(Cl)cc2)c(Cl)cc1NC(=O)c1cc(I)cc(I)c1O,train
CC=CC(=O)N(CC)c1ccccc1C,train
Cc1c(O)c(=O)ccn1C,train
COc1cc(C(=O)NC2CCN(C)CC2)ccc1Nc1ncc2c(n1)N(C1CCCC1)CC(F)(F)C(=O)N2C,valid
O=C(O)c1cccc(N(Cc2cccnc2)c2ccc(OC(F)F)c(OC(F)F)c2)c1,train
Cc1cc(F)ccc1-c1cc(N2CC[C@@H](O)[C@H]2CO)ncc1N(C)C(=O)C(C)(C)c1cc(C(F)(F)F)cc(C(F)(F)F)c1,train
O=C(CCCCCCCCc1cccnc1)N1CCC(C=C(c2ccccc2)c2ccccc2)CC1,train
Cn1c(=O)cc(Oc2ccc(F)cc2F)c2cnc(NCC(C)(C)O)nc21,train
CS(=O)(=O)c1ccc([C@@H](CC2CCCC2)C(=O)Nc2cnccn2)cc1Cl,test
CCCN(C1CCN(C(=O)C2CCNCC2)CC1)[C@@H]1CCc2ccc(OC)cc2C1,train
CC(C)CC(O)Cc1ccccc1,train
CC1(C)SC(C)(C)SC(C)(C)S1,train
CC(=O)OC(c1ccccc1)C(Cl)(Cl)Cl,train
C/C=C\c1ccc(OCC)c(O)c1,valid
CO[C@H]1C[C@@H]2CC[C@@H](C)[C@@](O)(O2)C(=O)C(=O)N2CCCC[C@H]2C(=O)O[C@H]([C@H](C)C[C@@H]2CC[C@@H](OCCO)[C@H](OC)C2)CC(=O)[C@H](C)C=C(C)[C@@H](O)[C@@H](OC)C(=O)[C@H](C)C[C@H](C)C=CC=CC=C1C,train
O=C(O)CSCC(=O)NC1CCSC1=O,train
CCCCCCCCCCCCCCCC(=O)O[C@@H]1[C@@H](O)[C@@H](O)[C@@H]([C@H](NC(=O)[C@@H]2C[C@@H](CCC)CN2C)[C@H](C)Cl)O[C@@H]1SC,train
CC(C)=CCOc1c2occc2cc2ccc(=O)oc12,train
CCOC(=O)COc1ccc2c(=O)cc(-c3ccccc3)oc2c1,test
N=C(N)NCCCCNC(=N)N,train
CCN(CC)N(O)N=O.CCNCC,train
O=C(O)[C@@H](O)[C@H](O)[C@H](O)[C@@H](O)C(=O)O,train
COc1cc([C@@H]2c3cc4c(cc3[C@@H](O)[C@H]3COC(=O)[C@H]23)OCO4)cc(OC)c1O,train
C[n+]1ccn(Cc2ccccc2)c1,valid
C=CCn1cc[n+](C)c1,train
CCCCCCCC[N+](C)(CCCCCCCC)CCCCCCCC.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
Cc1ccc(-c2cc(C(F)(F)F)nn2-c2ccc(S(N)(=O)=O)cc2)cc1,train
CON=C(C(=O)N[C@@H]1C(=O)N2C(C(=O)O)=C(COC(N)=O)CS[C@H]12)c1ccco1,train
CCCCCC(=O)O,test
O=C(O)CCCCC(=O)O,train
CCCCCC=O,train
CCCCCC/C=C/CCCCCCCC(=O)O,train
CCCCCCCCCCCCCCCC(=O)O,train
O=C(C(Cl)(Cl)Cl)C(Cl)(Cl)Cl,valid
CCCCCCC(=O)O,train
CCCC(=O)CC,train
CC(C)C#N,train
CCOC(=O)c1ccccc1N,train
CCNc1ccccc1,test
Nc1nc2c(ncn2COCCO)c(=O)[nH]1,train
Nc1ncnc2[nH]cnc12,train
Cc1ccc(/C(=C\CN2CCCC2)c2cccc(/C=C/C(=O)O)n2)cc1,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](COP(=O)(O)OP(=O)(O)OP(=O)(O)O)[C@@H](O)[C@H]1O,valid
N#Cc1ccc(C2CCCc3cncn32)cc1,train
CN(C)c1ccc(N=NS(=O)(=O)[O-])cc1,train
CC[N+](=O)[O-],train
O=C(O)c1ccc([N+](=O)[O-])cc1,train
CCCC[N+](=O)[O-],test
O=[N+]([O-])c1ccccc1,train
O=[N+]([O-])c1ccc2[nH]cnc2c1,train
COc1ccccc1[N+](=O)[O-],train
CNC(=O)O/N=C/C(C)(C)S(C)=O,train
O=[N+]([O-])c1ccc2c3c(cccc13)CC2,valid
Nc1ccc([N+](=O)[O-])cc1,train
Cc1nc(N(C)C)nc(OC(=O)N(C)C)c1C,train
CCCCc1ccc2[nH]c(NC(=O)OC)nc2c1,train
Clc1ccc(C(OCCN2CCCCC2)c2ccccc2)cc1,train
C=CC[N@@+]12CC[C@@]34c5ccccc5N5C=C6[C@H]7C[C@H]8[C@@]9(CC[N@@+]8(CC=C)C/C7=C/CO)c7ccccc7N(C=C([C@@H](C[C@@H]31)/C(=C\CO)C2)[C@H]54)[C@@H]69,test
CCc1c(C)[nH]c2c1C(=O)C(CN1CCOCC1)CC2,train
CCN(CC)c1cc(C)nc2ncnn12,train
CC(Cc1ccc(O)cc1)NCC(O)c1cc(O)cc(O)c1,train
CC(C)C1CCC(Cc2ccc(Cl)cc2)C1(O)Cn1cncn1,train
CCOP(=S)(OCC)Oc1nc(Cl)n(C(C)C)n1,valid
COC(=O)c1ccc(I)cc1S(=O)(=O)[N-]C(=O)Nc1nc(C)nc(OC)n1,train
II,train
Oc1c(Br)cc(Br)cc1Br,train
CCOC(=O)c1ccccc1O,train
O=C(Oc1ccccc1)c1ccccc1O,test
CCCCCCCCOC(=O)c1ccccc1C(=O)OCCCCCCCC,train
CCOP(=S)(OCC)Oc1ccc(S(C)=O)cc1,train
O=P(Oc1ccccc1)(Oc1ccccc1)Oc1ccccc1,train
Cc1ccc(S(=O)(=O)[O-])cc1.[Na+],train
CC1C=[C-]C=C1.[C-]#[O+].[C-]#[O+].[C-]#[O+].[Mn+],valid
O=C1C[C@@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@H]3C[C@H]46,train
C[N+](C)(C)CCOC(=O)CCC(=O)OCC[N+](C)(C)C,train
O=C(O)CCC(=O)O,train
CC1(C)[C@H](C(=O)O)N2C(=O)C[C@H]2S1(=O)=O,train
COc1cc(NS(=O)(=O)c2ccc(N)cc2)nc(OC)n1,test
N=C(N)NS(=O)(=O)c1ccc(N)cc1,train
COc1ncnc(NS(=O)(=O)c2ccc(N)cc2)c1OC,train
CCCCC(CC)COC(=O)CS,train
C=CC(=O)[O-],train
C=C(C)C(=O)O[C@@H]1C[C@H]2CC[C@]1(C)C2(C)C,valid
C=CC[N+](C)(C)CC=C,train
CCOC(=O)/C=C(C)/C=C/C[C@@H](C)CCCC(C)C,train
COC(C)(C)CCC[C@H](C)C/C=C/C(C)=C/C(=O)OC(C)C,train
CCCCCCCCS(=O)(=O)Cl,train
Cn1nnc2c(C(N)=O)ncn2c1=O,test
O=C([O-])C(Cl)(Cl)Cl,train
Cc1cc(C)c(C2=C(OC(=O)CC(C)(C)C)C3(CCCC3)OC2=O)c(C)c1,train
CCC(C)(C)C(=O)OC1=C(c2cc(Cl)cc(Cl)c2)C(=O)OC12CCCCC2,train
CCCCCCCCCCC1CO1,train
CCC(CC)C(=O)O,valid
CCC(CO)(CO)[N+](=O)[O-],train
CCC(CC)CO,train
Cc1cc(Br)ccc1NC(=O)CCl,train
C=C(C)[C@@H]1CC=C(C)C(=O)C1,train
O=C(CCl)NCO,test
O=C(O)COc1ccccc1/C=N/NC(=O)c1ccncc1,train
NCCC[C@H](N)CC(=O)N[C@H]1CNC(=O)[C@H]([C@H]2C[C@H](O)N=C(N)N2)NC(=O)/C(=C/NC(N)=O)NC(=O)[C@H](CO)NC(=O)[C@H](CO)NC1=O,train
O=C(CN1CCN(Cc2ccc3c(c2)OCO3)CC1)N1c2ccccc2Sc2ccccc21,train
O=[N+]([O-])c1cccc(S(=O)(=O)NCCNS(=O)(=O)c2cccc([N+](=O)[O-])c2)c1,train
CCN(CC)CCOC(=O)C(C)(c1ccccc1)c1ccccc1,valid
OC(C#CCN1CCCC1)(c1ccccc1)c1ccccc1,train
O=C1NC(=O)[C@H]2[C@@H]1[C@@H]1C=C[C@H]2C1,train
CCCOC(=O)c1ccc(N)cc1,train
SCCCCCCCCS,train
C#CC1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@@]21CC,test
c1csc(SSc2cccs2)c1,train
CC(=O)OC(C)C,train
C=CC(C)(O)CCCC(C)CCCC(C)CCCC(C)C,train
CCCCCCCCCCCCCCN(C)C,train
CCCCCCCCCCCCCCO,valid
CCCCCCCCCCCCCCCCN(C)C,train
OCCOCCOCCOCCO,train
CCCCCCOCCOCCO,train
CC(C)CC(C)CC(O)CC(C)C,train
OCCN1CCN(CCO)CC1,test
CCCCCC(C=O)=Cc1ccccc1,train
O=C1CCCC1,train
O=C(OCc1ccccc1)c1ccccc1,train
COc1ccc(C)cc1[N+](=O)[O-],train
CCC(N)(CO)CO,valid
COC(=O)C1=C(C)NC(C)=C(C(=O)OC)C1c1ccccc1[N+](=O)[O-],train
O=C1c2ccccc2C(=O)C1(O)O,train
O=C(Cl)C(Cl)(Cl)Cl,train
CCC/C=C/C=C/C(=O)O[C@H]1/C(=C/C(=O)OC)C[C@H]2C[C@H]([C@@H](C)O)OC(=O)C[C@H](O)C[C@@H]3C[C@H](OC(C)=O)C(C)(C)[C@](O)(C[C@@H]4C/C(=C/C(=O)OC)C[C@H](C=CC(C)(C)[C@]1(O)O2)O4)O3,train
Oc1ccc([Hg]Cl)cc1,test
COC(COC(N)=O)C1=C(N2CC2)C(=O)C(C)=C(N2CC2)C1=O,train
CC1C(=O)CC2C3(C)CCC2(C(C)C(=O)O)C(=O)C13,train
COC(=O)CC(=O)C(C)(C)C,train
COC(=O)c1cc(Cl)cc(Cl)c1,train
COC(=O)C(Br)CBr,valid
COC(=O)Cc1cccc2ccccc12,train
Cc1cccc(NC(=S)N(C)C)c1,train
Nc1cccc(N)n1,train
Nc1ncnc2c1ncn2[C@@H]1O[C@@H]2COP(=O)(O)O[C@H]2[C@H]1O,train
CCCCCCCCCCCC[N+](C)(C)Cc1ccccc1,test
CC(C)(CO)[C@@H](O)C(=O)NCCC(=O)[O-].CC(C)(CO)[C@@H](O)C(=O)NCCC(=O)[O-],train
Nc1nc(=O)n([C@@H]2CS[C@H](CO)O2)cc1F,train
NCCCC[C@H](NC(=O)[C@@H]1CCCN1C(=O)[C@@H]1CSSC[C@H](NC(=O)CNC(=O)CNC(=O)CN)C(=O)N[C@@H](Cc2ccc(O)cc2)C(=O)N[C@@H](Cc2ccccc2)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CC(N)=O)C(=O)N1)C(=O)NCC(N)=O,train
C[C@]12CC[C@@H]3c4ccc(OC(=O)N(CCCl)CCCl)cc4CC[C@H]3[C@@H]1CC[C@@H]2OP(=O)([O-])[O-],train
NC1(c2ccc(-c3nc4ccn5c(=O)[nH]nc5c4cc3-c3ccccc3)cc2)CCC1,valid
Cc1nnc(C(C)C)n1C1C[C@H]2CC[C@H](C1)N2CC[C@H](NC(=O)C1CCC(F)(F)CC1)c1ccccc1,train
COc1cc2c(c(OC)c1OC)-c1c(cc3c(c1OC)OCO3)C[C@@H](C)[C@](C)(O)C2,train
Cn1cnc2c(F)c(Nc3ccc(Br)cc3Cl)c(C(=O)NOCCO)cc21,train
O=C(NOCC1CC1)c1ccc(F)c(F)c1Nc1ccc(I)cc1Cl,train
CCCCCCCCCCCCCCCCCCOP(=O)([O-])OC1CC[N+](C)(C)CC1,test
COc1cc2c(N3CCN(C(=O)Nc4ccc(OC(C)C)cc4)CC3)ncnc2cc1OCCCN1CCCCC1,train
ClC=C(Cl)Cl,train
ClCC(Cl)Cl,train
CC(Cl)(Cl)Cl,train
Oc1c(Cl)cc(Cl)cc1Cl,valid
CC(Oc1cc(Cl)c(Cl)cc1Cl)C(=O)O,train
FC(Cl)(Cl)Cl,train
O=C1c2ccccc2C(=O)N1SC(Cl)(Cl)Cl,train
O=C(O)COc1cc(Cl)c(Cl)cc1Cl,train
COP(=O)(OC)C(O)C(Cl)(Cl)Cl,test
CC(=O)OCC(=O)[C@@]1(OC(C)=O)[C@@H](C)C[C@H]2[C@@H]3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
CN(C)C1CCCCC1,train
O=C(Cl)c1ccccc1,train
CCOP(=S)(OCC)SC(CCl)N1C(=O)c2ccccc2C1=O,train
CCOP(=O)(OCC)Oc1cc(C)nc(C(C)C)n1,valid
CCOc1ccc(NC(=O)OC(C)C)cc1OCC,train
CCCCCCCCS(=O)C(C)Cc1ccc2c(c1)OCO2,train
CC(=O)c1ccccn1,train
CCCCOCCOCCOCc1cc2c(cc1CCC)OCO2,train
C1CCNCC1,test
CC[C@@H]1C(=O)OC[C@@H]1Cc1cncn1C,train
C[C@@H]1CC=CC=CC=CC=C[C@H](O[C@@H]2O[C@H](C)[C@@H](O)[C@H](N)[C@@H]2O)C[C@@H]2O[C@](O)(C[C@@H](O)C[C@H]3O[C@@H]3C=CC(=O)O1)C[C@H](O)[C@H]2C(=O)O,train
Nc1c(Cl)c(Cl)nc(C(=O)O)c1Cl,train
C=C(C)C(=O)OCCO,train
C=CC(=O)OCCO,valid
O=P(c1ccccc1)(c1ccccc1)c1ccccc1,train
CCOP(=S)(OCC)SCSc1ccc(Cl)cc1,train
Oc1ccc(Oc2ccccc2)cc1,train
C#CC(O)CCCCC,train
CC(C)(C)NC(=O)[C@@H]1C[C@@H]2CCCC[C@@H]2CN1C[C@@H](O)[C@H](Cc1ccccc1)NC(=O)[C@H](CC(N)=O)NC(=O)c1ccc2ccccc2n1,test
C[Si](C)=O,train
CCOc1cc([N+](=O)[O-])c(OCC)cc1N1CCOCC1,train
O=S(=O)([O-])c1cccc(S(=O)(=O)[O-])c1,train
CN(C)CCOCCN(C)CCO,train
O=C([O-])c1cccc([N+](=O)[O-])c1,valid
O=C1NC(=O)c2cccc3cccc1c23,train
CCOC(=O)C1=NN(c2ccccc2)C(=O)C1,train
C=C[Si]1(C)O[Si](C)(C=C)O[Si](C)(C=C)O[Si](C)(C=C)O1,train
CCCCOC(=O)CCC(C)=O,train
c1ccc(CNc2ncnc3c2ncn3C2CCCCO2)cc1,test
Cc1ccc(N)c([N+](=O)[O-])c1,train
ClCC=CCCl,train
Cc1ccc([N+](=O)[O-])c(N)c1,train
CC1=CCC2CC1C2(C)C,train
CC(=O)NNC(C)=O,valid
C=CCOC(=O)c1ccccc1C(=O)OCC=C,train
Oc1cccc(O)c1O,train
S=C1NCCN1,train
O=[N+]([O-])c1ccc2c(c1)Cc1ccccc1-2,train
CCCCC(CC)COC(=O)CCCCC(=O)OCC(CC)CCCC,test
O=c1c(-c2ccccc2)c1-c1ccccc1,train
O=C(OCc1ccccc1)c1cccnc1,train
O=C1CN(N=Cc2ccc([N+](=O)[O-])o2)C(=O)N1,train
OC1OC[C@@H](O)[C@H](O)[C@@H]1O,train
NC(=O)CCCCC1CCSS1,valid
CCOC(=O)CC#N,train
CCOC(=O)CCl,train
COC(=O)CC(C)=O,train
COc1ccc(C)cc1,train
CCc1ccc(CC)cc1,test
OCC1CCC(CO)CC1,train
C=C(C)C(=O)OCCN(CC)CC,train
CCN(CC)CCOCCOC(=O)C(CC)(CC)c1ccccc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
O=P([O-])([O-])OC[C@@]1(O)O[C@H](COP(=O)([O-])O)[C@@H](O)[C@@H]1O,train
CN1CCN(c2c(F)cc3c(=O)c(C(=O)O)cn4c3c2OCN4C)CC1,valid
NC1CCCCC1.NC1CCCCC1.O=P(O)(O)OC[C@H](O)CO,train
O=c1[nH]c(=O)n([C@H]2C[C@H](O)[C@@H](CO)O2)cc1C(F)(F)F,train
CCC(=O)O.CCC(=O)O.O=C(Nc1cccc(C2=NCCN2)c1)Nc1cccc(C2=NCCN2)c1,train
O=S(=O)([O-])CC(S)CS,train
CCCCCCCCCCCCCC[N+](C)(C)Cc1ccccc1,test
CCC(CC)Nc1c([N+](=O)[O-])cc(C)c(C)c1[N+](=O)[O-],train
Brc1ccc(Oc2cc(Br)c(Br)cc2Br)c(Br)c1,train
Clc1cc(Cl)c(Cl)c(Cl)c1Cl,train
CCOc1cc(Oc2ccc(C(F)(F)F)cc2Cl)ccc1[N+](=O)[O-],train
CC(C)(C)C(O)C(Cc1ccc(Cl)cc1)n1cncn1,valid
O=C(O)c1ccccc1OP(=O)(O)O,train
COc1c2c(cc3c1OCO3)CCN(C)C2,train
CCCCOc1cc(C(=O)OCCN(CC)CC)ccc1N,train
CN(C)CCOC(=O)C(c1ccccc1)C1(O)CCCC1,train
CC(C)CC(NC(=O)CNC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@@H](CO)NC(=O)[C@@H](Cc1c[nH]c2ccccc12)NC(=O)[C@@H](Cc1c[nH]cn1)NC(=O)[C@H]1CCC(=O)N1)C(=O)N[C@H](CCCNC(=N)N)C(=O)N1CCC[C@@H]1C(=O)NCC(N)=O,test
C[C@]12CC[C@H]3[C@H]([C@@H]1[C@@H]1C[C@@H]1[C@@]21CCC(=O)O1)[C@H]1C[C@H]1C1=CC(=O)CC[C@@]13C,train
NC(=O)OCCCc1ccccc1,train
C[C@H]1CNc2c(cccc2S(=O)(=O)N[C@@H](CCCNC(=N)N)C(=O)N2CC[C@@H](C)C[C@@H]2C(=O)O)C1,train
COC(=O)c1ccccc1C(=O)c1ccc(OCCN2CCCCC2)cc1,train
C[C@@H]1C[C@H]2[C@@H]3C[C@H](F)C4=CC(=O)C(Cl)=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)CO,valid
CC(C)OC(=O)C(C(=O)OC(C)C)=C1SC=CS1,train
CN[C@@H]1[C@@H](O)[C@@H](O[C@@H]2[C@@H](O)[C@H](O[C@H]3O[C@H](CN)[C@@H](O)[C@H](O)[C@H]3O)[C@@H](N)C[C@H]2NC(=O)C(O)CN)OC[C@]1(C)O,train
Cc1ccnc2c1NC(=O)c1cccnc1N2C1CC1,train
Cc1cc(-n2c(=O)[nH]c(=O)n(C)c2=O)ccc1Oc1ccc(S(=O)(=O)C(F)(F)F)cc1,train
CCOC(=O)[C@H](CCc1ccccc1)N[C@H]1CCCN2CCC[C@@H](C(=O)O)N2C1=O,test
CC(=O)Nc1c(I)c(NC(C)=O)c(I)c(C(=O)O)c1I,train
CCOC(=O)NCCC[Si](OCC)(OCC)OCC,train
CCO[Si](CC(C)C)(OCC)OCC,train
Cc1ccccc1C(OCCN(C)C)c1ccccc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
COc1cccc(C(=O)CCN[C@@H](C)[C@H](O)c2ccccc2)c1,valid
O=[N+]([O-])C(Br)(CO)CO,train
OCCCBr,train
Cc1cccc(Br)c1,train
BrCc1ccccc1,train
C[N+]1(C)[C@H]2C[C@H](OC(=O)[C@H](CO)c3ccccc3)C[C@@H]1[C@H]1O[C@@H]21,test
COP(=S)(NC(C)C)Oc1ccc(C)cc1[N+](=O)[O-],train
CCCCCOC(=O)CCC,train
c1cncc([C@@H]2CCCCN2)c1,train
CCOC(=O)C(Br)c1ccccc1,train
CCCCCC(=O)CC,valid
CCN(CCO)CCO,train
COc1ccc(C2CNC(=O)C2)cc1OC1CCCC1,train
CCOC(OCC)OCC,train
CCOC(=O)C(F)(F)F,train
C=C[C@@H](C)CCC=C(C)C,test
NC1=NC(=O)N2CC12,train
SCCSCCS,train
CCCCNC(C)(C)[PH](=O)O,train
N[C@@H](Cc1cc(I)c(O)c(I)c1)C(=O)O,train
CN(C(=O)Cc1ccc(Cl)c(Cl)c1)[C@H]1CC[C@@]2(CCCO2)C[C@@H]1N1CCCC1,valid
CCC[C@@H]1C[C@@H](C(=O)N[C@H]([C@H](C)Cl)[C@H]2O[C@H](SC)[C@H](OP(=O)(O)O)[C@@H](O)[C@H]2O)N(C)C1,train
O=C1CC2(CCCC2)CC(=O)N1CCNCC1COc2ccccc2O1,train
O=C(C(=O)c1ccccc1)c1ccccc1,train
N=C(N)c1ccc(OCCCOc2ccc(C(=N)N)cc2)cc1,train
CCCCCCC#N,test
O=C(O)CCCCCC(=O)O,train
COc1ccc(Nc2ccccc2)c(C)c1,train
CCCC(=O)OC[C@@H](OC(=O)CCC)[C@@H](OC(=O)CCC)[C@H](Cn1c2nc(=O)[nH]c(=O)c-2nc2cc(C)c(C)cc21)OC(=O)CCC,train
CCO[Si](CCCSSSSCCC[Si](OCC)(OCC)OCC)(OCC)OCC,train
C=COC(=O)C(C)(CC)CCC(C)C,valid
CCCC[N+]1(C)[C@H]2C[C@H](OC(=O)[C@H](CO)c3ccccc3)C[C@@H]1[C@H]1O[C@@H]21,train
CCCCCCCCSCC,train
O=S(O)CO[Na],train
C=COCCOCCOCCOC=C,train
CCC(C)C(=O)O,test
CC(C)CC#N,train
Cc1ccc(C(=O)O)o1,train
CC(C)c1ccc(CO)cc1,train
CC(=O)CCC=C(C)C,train
CCC(C)C(C(=O)OCC[N+](C)(CC)CC)c1ccccc1,valid
Nc1ncnc(Nc2ccc(Cl)cc2)n1,train
CN(C)CCCSc1ccccc1NC(=O)/C=C/c1ccccc1,train
CC(C)NCC(O)c1ccc(N)c(C#N)c1,train
CN1C(=O)COc2c(C(=O)NC3CN4CCC3CC4)cc(Cl)cc21,train
C=C1C2CCC(C2)C1(C)C,test
CC(C(O)c1ccc(O)cc1)N1CCC(Cc2ccccc2)CC1,train
O=C(O)CN1C(=O)c2cccc3cccc(c23)C1=O,train
NP(N)(=O)NC(=O)c1ccc(F)cc1,train
Clc1cccc(SC2CCNCC2)n1,train
CC(=O)OC1CN2CCC1CC2,valid
CCCCC(CC)COC(=O)C(C#N)=C(c1ccccc1)c1ccccc1,train
CCCC=C(C=O)CC,train
C=CC(=O)OCC(CC)CCCC,train
CCCCC(C=O)CC,train
CCCC(O)C(CC)CO,test
CCCCC(CC)C(=O)O,train
O=[N+]([O-])c1ccc(-c2nnc(O)o2)o1,train
O=C(O)c1cccc(Oc2ccccc2)c1,train
COc1ccc([N+](=O)[O-])cc1N,train
NC(=O)N/N=C/c1ccc([N+](=O)[O-])o1,valid
CC(=O)OC(OC(C)=O)c1ccc([N+](=O)[O-])o1,train
O=C(O)Cc1ccc(-c2ccccc2)cc1,train
NC(=O)c1cc[n+](CC2=C(C(=O)[O-])N3C(=O)[C@@H](NC(=O)Cc4cccs4)[C@H]3SC2)cc1,train
CCOc1nc(C(Cl)(Cl)Cl)ns1,train
O=C1OC(CN2CCOCC2)CN1N=Cc1ccc([N+](=O)[O-])o1,test
CCN(CC)CCNC(=O)c1cc(Br)c(N)cc1OC,train
C=C1c2cccc(O)c2C(=O)C2=C(O)[C@]3(O)C(=O)C(C(N)=O)=C(O)[C@@H](N(C)C)[C@@H]3[C@@H](O)[C@H]12,train
CC(=O)N[C@@H](CC(=O)N[C@@H](CCC(=O)O)C(=O)O)C(=O)O,train
C[N+]1([O-])[C@H]2CC[C@@H]1C[C@H](OC(=O)C(CO)c1ccccc1)C2,train
C[C@H]1CCC[C@@H](C)N1,valid
CCCCCCCCN1CCCC1=O,train
COC(=O)[C@@]1(C)[C@H](C)C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@H]3[C@@H](O)C[C@@]21C,train
COc1ccc2cc(CCC(C)=O)ccc2c1,train
Cc1cc(-n2nc3ccc(Cl)cc3n2)c(O)c(C(C)(C)C)c1,train
CN[C@@H]1[C@@H](O)[C@@H](O[C@@H]2[C@@H](O)[C@H](OC3OC(CN)=CCC3N)[C@@H](N)C[C@H]2N)OC[C@]1(C)O.CN[C@@H]1[C@@H](O)[C@@H](O[C@@H]2[C@@H](O)[C@H](OC3OC(CN)=CCC3N)[C@@H](N)C[C@H]2N)OC[C@]1(C)O,test
CC(C)(C)NCC(O)COc1cccc2c1CCC(=O)N2,train
O=C(O)c1cc2ccccc2nc1C(=O)O,train
CCCCCCCCCCCCCCCCCC#N,train
CN(C)c1ccc(N=Nc2ccccc2)cc1,train
Cc1ncc([N+](=O)[O-])n1C,valid
COP(C)(=O)OC,train
C/C=C(C)/C=C/C=C(C)C,train
COC(=O)c1ccc(C(=O)OC)cc1,train
CC(=O)N(C)C,train
CCNc1nc(Cl)nc(NC(C)(C)C#N)n1,test
Nc1nc(N)nc(NC2CC2)n1,train
CC1(C)C(C=C(Cl)Cl)C1C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1,train
NC(=O)NC(=O)Cc1ccccc1,train
OCCN1CCN(CCCN2c3ccccc3Sc3ccc(Cl)cc32)CC1,train
CCC[C@H](N[C@@H](C)C(=O)N1[C@H](C(=O)O)C[C@@H]2CCCC[C@@H]21)C(=O)OCC,valid
CNC(=O)O/N=C(\C)C(C)S(C)(=O)=O,train
CC(=O)CC(C)=O,train
CN(C)C(=S)[S-].CN(C)C(=S)[S-].[Zn+2],train
O=[N+]([O-])c1cccc(Cl)c1,train
c1ccc(Nc2ccccc2)cc1,test
CC(C)c1ccc(C=O)cc1,train
CCc1ccc(O)cc1,train
CCCCN(CCCC)C(=S)[S-].CCCCN(CCCC)C(=S)[S-].[Zn+2],train
CC(C)C(C)(N)C#N,train
C[C@H]1CCCC(=O)CCCC=Cc2cc(O)cc(O)c2C(=O)O1,valid
OCc1cccc(Oc2ccccc2)c1,train
Clc1ccccc1CN1CCc2sccc2C1,train
CCN(CC)CCOC(=O)C(c1ccccc1)C1CCCCC1,train
CN1CCC(=C2c3ccccc3CC(=O)c3sccc32)CC1,train
CCN(CC)CCSC(=O)C(c1ccccc1)c1ccccc1,test
C#C[C@]1(O)CC[C@H]2[C@H]3[C@H](CC[C@@]21C)C1=C(CC(=O)CC1)C[C@H]3C,train
Nc1ccn([C@H]2CS[C@@H](CO)O2)c(=O)n1,train
CC(C(=O)O)c1ccc(C(=O)c2ccccc2)s1,train
CC(O)C(=O)[O-].CC(O)C(=O)[O-],train
[Ca+2].[Cl-].[Cl-],valid
Cn1c(=O)c2c(ncn2C)n(C)c1=O,train
C=C1CC[C@H](O)C/C1=C/C=C1\CCC[C@@]2(C)[C@H]1CC[C@@H]2[C@H](C)/C=C/[C@H](C)C(C)C,train
CCC(C)OC(C)CC,train
O=C(O)C=Cc1ccc(O)c(O)c1,train
CCCOCCOCCO,test
CN(C)C(=O)COC(=O)Cc1ccc(OC(=O)c2ccc(NC(=N)N)cc2)cc1,train
CCOc1nc2cccc(C(=O)OC(C)OC(=O)OC3CCCCC3)c2n1Cc1ccc(-c2ccccc2-c2nnn[nH]2)cc1,train
CC(CC(=O)O)CC(C)(C)C,train
c1cc(N(CC2CO2)CC2CO2)ccc1OCC1CO1,train
CC(=O)c1ccc2ccccc2c1,valid
C=C(C)C(=O)OCCOC(=O)CC(C)=O,train
COC(=O)[C@H]1CC[C@H](C(=O)OC)CC1,train
CCOC(=O)C(C)(C)C,train
O=C/C=C/c1ccco1,train
CCCCCCCCCCCCCCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,test
CCCCCCCCCCCCCCCCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
CCCCCCCCCCCCCCCCCl,train
CCCCCCCCCI,train
CCCCCCCCCCCCI,train
CN(C)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1,valid
COC(=O)/C=C\C(=O)O,train
CC(C)c1ccc2cc(C(C)C)ccc2c1,train
CCOC(=O)c1ccccc1,train
CC(=O)OCCC(C)C,train
CC(C)CCOC(=O)C=Cc1ccccc1,test
O=c1[nH]ncc2ccccc12,train
CCCCCCCCCCCCCCCCCC[N+](C)(C)C,train
CCOCCOCCOC(C)=O,train
CCCCOCCOC(C)=O,train
CCCCCCCCCCCCN(C)C,valid
CCCCCCOCCO,train
CC(=O)C=C(C)C,train
CN1c2c(O)nc(N)nc2NCC1CNc1ccc(C(=O)NC(CCC(=O)[O-])C(=O)[O-])cc1,train
CC(C)(CO)C(O)C(=O)NCCC(=O)NCCSSCCNC(=O)CCNC(=O)C(O)C(C)(C)CO,train
CCCCCCCCCCCCCC(=O)OCC(COP(=O)([O-])OCC[N+](C)(C)C)OC(=O)CCCCCCCCCCCCC,test
Clc1cccc(C(c2ccc3nc[nH]c3c2)n2ccnc2)c1,train
Cc1ccc2nc(N)[nH]c(=O)c2c1Sc1ccncc1,train
CC1(C)[C@H](C=C(Cl)Cl)[C@H]1C(=O)OCc1c(F)c(F)cc(F)c1F,train
N=C(NN=Cc1ccc(Cl)cc1)NN=Cc1ccc(Cl)cc1,train
CCCCC(O)c1ccccc1,valid
CCN(CC)CCOC[C@H]1O[C@@H](O)[C@H](OCCN(CC)CC)[C@@H](O)[C@@H]1O,train
CC#Cc1cc(C)nc(Nc2ccccc2)n1,train
Cl[Hg]c1ccccc1,train
C[N+](C)(C)CC(O)CC(=O)[O-],train
C[C@H]1CC[C@]2(NC1)O[C@H]1C[C@H]3[C@@H]4CC[C@H]5C[C@@H](O[C@@H]6O[C@H](CO)[C@H](O[C@@H]7O[C@H](CO)[C@@H](O)[C@H](O[C@@H]8OC[C@@H](O)[C@H](O)[C@H]8O)[C@H]7O[C@@H]7O[C@H](CO)[C@@H](O)[C@H](O)[C@H]7O)[C@H](O)[C@H]6O)CC[C@]5(C)[C@H]4CC[C@]3(C)[C@H]1[C@@H]2C,test
O=NN1CCCC1,train
C[C@H]1[C@H]2Cc3ccc(O)cc3[C@]1(C)CCN2CCc1ccccc1,train
CCC(=O)N(c1ccccc1)C1(COC)CCN(CCn2nnn(CC)c2=O)CC1,train
CC(C)=CCN1CC[C@@]2(C)c3cc(O)ccc3C[C@@H]1[C@@H]2C.O=C(O)CCC(=O)O,train
O=Cc1ccccc1O,valid
CCC(=O)N(c1ccccc1)[C@@H]1CCN(CCc2ccccc2)C[C@@H]1C,train
CCC(=O)N(c1ccccc1)[C@H]1CCN(CCc2ccccc2)C[C@@H]1C,train
CCC(=O)N(c1ccccc1)C1(COC)CCN(CCc2cccs2)CC1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
CCC(=O)N(c1ccc(F)cc1)C1CCN(CCc2ccccc2)CC1,train
CC(=O)N[C@H]1C(O)O[C@H](CO)[C@@H](O)[C@@H]1O,test
CCC(=O)N(c1ccccc1)C1CCN(C(C)Cc2ccccc2)CC1,train
CCC(=O)N(c1ccccc1)C1CCN(Cc2ccccc2)CC1,train
CC(C)Br,train
C=C1C(CCl)(CCl)C2(Cl)C(Cl)C(Cl)C1(Cl)C2(Cl)Cl,train
C=CCSCC1Nc2cc(Cl)c(S(N)(=O)=O)cc2S(=O)(=O)N1,valid
Cc1ccc(NC(N)=O)cc1,train
CN(C)CCOc1ccc(/C(=C(/CCCl)c2ccccc2)c2ccccc2)cc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
Cc1ccccc1,train
Cc1ccc(N=C=O)cc1N=C=O,train
Cc1ccccc1S(N)(=O)=O,test
C[SiH](O[Si](C)(C)C)O[Si](C)(C)C,train
CCOc1ccccc1OCC,train
C1N2CN3CN1CN(C2)C3,train
Cc1cc(Cl)ccc1N=CN(C)C,train
[SbH6+3],valid
O=[N+]([O-])OCC(CO[N+](=O)[O-])(CO[N+](=O)[O-])CO[N+](=O)[O-],train
CCCCCCCCCCCc1ccccc1,train
Cn1c(N)c(N=O)c(=O)n(C)c1=O,train
C[N+]1([O-])CCOCC1,train
CCOP(=S)(OCC)Oc1ccc([N+](=O)[O-])cc1,test
CC(Cc1cccc(C(F)(F)F)c1)NCCOC(=O)c1ccccc1,train
CC1(C)S[C@@H]2[C@H](NC(=O)COc3ccccc3)C(=O)N2[C@H]1C(=O)[O-],train
COc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl,train
ClC(Cl)C(Cl)(Cl)Cl,train
O=[N+]([O-])c1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl,valid
Nc1ccc(C(=O)O)cc1,train
Nc1nc2ccccc2s1,train
COc1ccc(OC)c(C[C@H](C)N)c1,train
CCCCCCc1ccc(O)cc1O,train
CC(=O)N(O)CCCCCNC(=O)CCC(=O)N(O)CCCCCNC(=O)CCC(=O)N(O)CCCCCN,test
O=C([O-])c1cc(Oc2ccc(C(F)(F)F)cc2Cl)ccc1[N+](=O)[O-],train
CCCOC(=O)c1c(Cl)c(Cl)c(Cl)c(Cl)c1C(=O)OCCC,train
COc1ccc(/N=N/c2cc(C)c(/N=N/c3ccc(S(=O)(=O)[O-])cc3)cc2OC)cc1,train
CC1(C)Cc2cccc(N)c2O1,train
NC(=O)CC[C@H](Nc1ccc([N+](=O)[O-])cc1)C(=O)O,valid
CCCCC(C)C(=O)O,train
O=C1CCCN1C1CCCCC1,train
Cc1ccc(Cl)c(N)c1Cl,train
Nc1ccc2cc(S(=O)(=O)[O-])cc(O)c2c1/N=N/c1ccccc1C(F)(F)F,train
COCc1ccc(COC)cc1,test
Nc1cc(C(=O)O)c(O)c(S(=O)(=O)O)c1,train
C=CCCC(=O)/C=C/C1C(C)=CCCC1(C)C,train
O=CN1CCCCC1,train
C[C@@H]1CC2(OC3C[C@@]4(C)C5=CC[C@H]6C(C)(C)C(O[C@@H]7OC[C@@H](O)[C@H](O)[C@H]7O)CC[C@@]67C[C@@]57CC[C@]4(C)C31)OC(O)C1(C)OC21,train
O=C(/C=C/c1ccc(O)c(O)c1)O[C@@H](C(=O)O)[C@@H](OC(=O)/C=C/c1ccc(O)c(O)c1)C(=O)O,valid
CC(C)(C)c1csc(-c2cc3cc(OCc4ccccc4CC(=O)O)ccc3o2)n1,train
Cc1ccc2oc(CN(Cc3ccccc3)C(=O)Nc3c(F)cc(F)cc3F)c(-c3ccc(Cl)cc3)c2c1,train
O=C1c2ccccc2C(=O)N1CCc1noc(-c2ccc(CN3CCCCCC3)s2)n1,train
COc1cc(C=O)cc2c1[C@H](COC(N)=O)[C@]1(OC(C)=O)ON2C[C@H]2[C@@H]1N2C(C)=O,train
Cc1[nH]cnc1C[C@H]1CCc2c(C)c3ccccc3n2C1=O,test
Cc1oc2c(NC(=O)c3c(Cl)cccc3Cl)cccc2c1C(C)(C)O,train
COc1ccc(-n2nc(C(F)F)cc2-c2ccc(S(C)=O)cc2)cc1,train
OCCc1ccccc1,train
NC=O,train
COC(=O)c1ccccc1C(=O)OC,valid
NC(Cc1ccc(F)cc1)C(=O)O,train
O=[N+]([O-])c1ccc(F)c([N+](=O)[O-])c1,train
O=C(Cl)c1ccccc1F,train
CCCCCCC1CCC(=O)O1,train
O=CNc1ccccc1,test
C[C@]12C=CC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@]2(C)O,train
CC[N+](C)(CC)CCOC(=O)C1c2ccccc2Oc2ccccc21,train
CC(=O)OC(C)C[N+](C)(C)C,train
Cc1ccccc1-n1c(C)nc2ccccc2c1=O,train
CC(C)=CC1C(C(=O)OCc2cccc(Oc3ccccc3)c2)C1(C)C,valid
COC(=O)/C=C(\C)OP(=O)(OC)OC,train
CCCC(=O)c1ccccc1,train
O=P1(N(CCCl)CCCl)NCCCO1,train
C[C@H]1C[C@H](C)C(=O)[C@H]([C@H](O)CC2CC(=O)NC(=O)C2)C1,train
CCS(=O)(=O)CCSP(=O)(OC)OC,test
C1CCC2OC2C1,train
O=C1C=CCCC1,train
c1cc2c3c(c1)ccc1cccc(c13)C2,train
Cc1ccc(S(=O)(=O)NC2CCCCC2)cc1,train
N#Cc1ccccc1C#N,valid
CC1=CC[C@H](C(C)C)C=C1,train
O=C(Cl)OCCOCCOC(=O)Cl,train
CC(=O)CCc1ccc(OC(C)=O)cc1,train
O=c1c(O)c(O)c1=O,train
O=C(O)c1ccccn1,test
CC1=Nc2ccccc2C1(C)C,train
COC(=O)C1=C(C)NC(C)=C(C(=O)OC(C)(C)CN(C)CCC(c2ccccc2)c2ccccc2)C1c1cccc([N+](=O)[O-])c1,train
Nc1cccc2c1CN(C1CCC(=O)NC1=O)C2=O,train
CCCCN(CCCC)CC(O)c1cc(Cl)cc2c1-c1ccc(Cl)cc1/C2=C/c1ccc(Cl)cc1,train
COc1cccc(CCc2ccccc2OCC(CN(C)C)OC(=O)CCC(=O)O)c1,valid
COc1nc(N)nc2nc[nH]c12,train
Nc1nc(=O)c2c(CCc3ccc(C(=O)N[C@@H](CCC(=O)[O-])C(=O)[O-])cc3)c[nH]c2[nH]1,train
Cc1nc(Nc2ncc(C(=O)Nc3c(C)cccc3Cl)s2)cc(N2CCN(CCO)CC2)n1,train
CCN(CC)c1nc(C)cc(OP(=S)(OC)OC)n1,train
O=P(O)(O)O,test
COP(=S)(OC)SCN1C(=O)c2ccccc2C1=O,train
C=C(C)C(N)=O,train
CC1CCc2c(N3CCC(O)CC3)c(F)cc3c(=O)c(C(=O)O)cn1c23,train
Cc1nc2ccc(CN(C)c3ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)s3)cc2c(=O)[nH]1,train
COc1ccc(-c2ccc3cc(C(=O)O)ccc3c2)cc1C12CC3CC(CC(C3)C1)C2,valid
Cc1oc(=O)oc1CN1CCN(c2cc3c(cc2F)c(=O)c(C(=O)O)c2n3C(C)S2)CC1,train
CN(C)CC/C=C1/c2ccccc2COc2ccc(CC(=O)O)cc21,train
C[C@@H](c1ncncc1F)[C@](O)(Cn1cncn1)c1ccc(F)cc1F,train
C[N+]1(CCC(C(N)=O)(c2ccccc2)c2ccccc2)CCCCC1,train
CC(=O)NC[C@H]1CN(c2ccc(N3CCOCC3)c(F)c2)C(=O)O1,test
COCc1c(C(C)C)nc(C(C)C)c(/C=C/[C@@H](O)C[C@@H](O)CC(=O)[O-])c1-c1ccc(F)cc1,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)CN(C)[C@H](C)[C@@H](O)[C@]1(C)O,train
OCCOCCN1CCN(C(c2ccccc2)c2ccc(Cl)cc2)CC1,train
CCCCCCCCOC(=O)c1ccc(C(=O)OCCCCCCCC)c(C(=O)OCCCCCCCC)c1,train
CCCCCCOC(=O)CC(CC(=O)OCCCCCC)(OC(=O)CCC)C(=O)OCCCCCC,valid
CN(C[C@@H](CC[N+]12CCC(C3CCCCC3)(CC1)CC2)c1ccc(Cl)c(Cl)c1)C(=O)c1cc(C(F)(F)F)cc(C(F)(F)F)c1,train
CCCOc1ccc(S(=O)(=O)N2CCN(CC)CC2)cc1-c1nc2c(CC)n(Cc3ccccn3)nc2c(=O)[nH]1,train
COCCOC[C@H](CC1(C(=O)N[C@H]2CC[C@@H](C(=O)O)CC2)CCCC1)C(=O)Oc1ccc2c(c1)CCC2,train
COCc1nnc(-c2ccc[n+]([O-])c2)n1-c1c(Cl)c(Cl)cc2[nH]c(=O)c(=O)[nH]c12,train
O=C(N[C@H]1CN2CCC1CC2)c1cc2ccoc2cn1,test
C=CC(=O)OCCCC,train
C[C@@H](Oc1ccc(CNC(=O)c2cccnc2Oc2ccc3c(c2)OCO3)c(F)c1)C(=O)O,train
O=C(O)CC(C(=O)O)C(CC(=O)O)C(=O)O,train
CNCc1cc(F)ccc1Oc1ccc(Cl)c(Cl)c1,train
CC(C)(O)c1ccc(CNC(=O)c2cccnc2Oc2ccc3nonc3c2)cc1,valid
C=C(NC(=O)C(=C)NC(=O)c1csc(C2=NC3c4csc(n4)C4NC(=O)c5csc(n5)C(C(C)(O)C(C)O)NC(=O)C5CSC(=N5)/C(=C\C)NC(=O)C(C(C)O)NC(=O)c5csc(n5)C3(CC2)NC(=O)C(C)NC(=O)C(=C)NC(=O)C(C)NC(=O)C(C(C)CC)NC2C=Cc3c(C(C)O)cc(nc3C2O)C(=O)OC4C)n1)C(N)=O,train
Cc1ccc(C=O)cc1C,train
CC(C)NC(=O)c1ccccc1N,train
O=C(O)CONC(=O)c1ccccc1,train
CCOC(=O)CCN(SN(C)C(=O)Oc1cccc2c1OC(C)(C)C2)C(C)C,test
O=C(Nc1ccccc1)c1ccccc1I,train
CCOC(=O)Cn1c(=O)sc2cccc(Cl)c21,train
CC(C)OC(=O)C(C)N(C(=O)c1ccccc1)c1ccc(F)c(Cl)c1,train
O=S(=O)(Oc1ccc(Cl)cc1)c1ccccc1,train
CC12CCC(C1)C(C)(C)C2O,valid
CCCCC(CC)C(=O)OC(C)C,train
O=C(NC(=O)c1c(F)cccc1F)Nc1ccc(Oc2ccc(C(F)(F)F)cc2Cl)cc1F,train
COc1cc(C(=O)NCc2ccc(OCCN(C)C)cc2)cc(OC)c1OC,train
CC(C)NC(=N)NC(=N)Nc1ccc(Cl)cc1,train
CCCCOc1ccc(OCCCN2CCOCC2)cc1,test
CN1CCC(OC(c2ccccc2)c2ccccc2)CC1,train
CN1CCN(C(c2ccccc2)c2ccccc2)CC1,train
CC(O)CN(CCCN(C)C)CCCN(C)C,train
CN(C)CC/C=C1/c2ccccc2CSc2ccccc21,train
CCOc1ccc(Cc2nccc3cc(OCC)c(OCC)cc23)cc1OCC,valid
CN(C)CCCNCCCN(C)C,train
Fc1ccc(Br)cc1,train
c1c[nH]cn1,train
O=C([O-])[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,train
CCC[N+](CCC)(CCC)CCC,test
O=C(OCCOCCOCCOC(=O)c1ccccc1)c1ccccc1,train
C=C(C)C1CC=C(C)CC1,train
CN1C2=CC(=O)C(=NNC(N)=O)C=C2CC1S(=O)(=O)[O-],train
C[N+](C)(C)CCOC(N)=O,train
NC(=O)N1c2ccccc2C=Cc2ccccc21,valid
CCOC(=O)n1ccn(C)c1=S,train
CN(C)c1cccc(O)c1,train
CCN(CC)CCOCCOC(=O)C1(c2ccccc2)CCCC1,train
COc1ccc(C[C@H](C)NC[C@@H](O)c2ccc(O)c(NC=O)c2)cc1.COc1ccc(C[C@H](C)NC[C@@H](O)c2ccc(O)c(NC=O)c2)cc1,train
COC(=O)C[C@](O)(CCCC(C)(C)O)C(=O)O[C@@H]1C(OC)=CC23CCCN2CCc2cc4c(cc2[C@H]13)OCO4,test
CC(=O)CCC(=O)O,train
O=C(C1CCCCC1)N1CC(=O)N2CCc3ccccc3C2C1,train
CCCCCCCC=O,train
CCCCCCCCCCCCCCCCCC(=O)O,train
CCCCCCCCC(=O)O,valid
O=C(O)CCCCCCCC(=O)O,train
CCCC(=O)C(=O)O,train
O=C(O)CC(=O)C(=O)O,train
CCCCCCCC(=O)O,train
Cl/C(=C\n1cncn1)c1ccc(Cl)cc1Cl,test
CC(C)c1ccc2oc3nc(N)c(C(=O)O)cc3c(=O)c2c1,train
CC(=O)Nc1ccc(/C=N/NC(N)=S)cc1,train
Nc1ccc(C(=O)NCC(=O)O)cc1,train
Clc1ccc2c(c1)C(N1CCNCC1)=Nc1ccccc1O2,train
CCCCOC(=O)[C@@H](C)Oc1ccc(Oc2ccc(C#N)cc2F)cc1,valid
Cc1cccc(C)c1NC(=O)CN1CCCC1=O,train
Nc1cccc2ccccc12,train
Nc1ccc2ccccc2c1,train
CCCCN(CCCC)C(=S)S[Ni]SC(=S)N(CCCC)CCCC,train
[Ni+2],test
NC(=O)c1cccnc1,train
CCCCCCCCCCCCCCCC(=O)OC[C@@H](COP(=O)([O-])OCC[N+](C)(C)C)OC(=O)CCCCCCCCCCCCCCC,train
CCN(CC)c1ccc(C(=C2C=CC(=[N+](CC)CC)C=C2)c2ccccc2)cc1,train
c1ccc(CCNc2nc(-c3ccccc3)cs2)cc1,train
Nc1nnnn1-c1ccccc1,valid
CCCCCCCCCCCCCCCCCC(=O)O[AlH3](O)O,train
CCOc1ccc(NS(=O)(=O)c2ccc(N)cc2)nn1,train
CN1CCCN=C1COC(=O)C(O)(c1ccccc1)C1CCCCC1,train
CC(C)(C)C(=O)OCOC(=O)[C@@H]1N2C(=O)[C@@H](NC(=O)[C@H](N)c3ccccc3)[C@H]2SC1(C)C,train
CCn1cc(C(=O)O)c(=O)c2cc(F)c(N3CCNCC3)nc21,test
CO[C@H]1C[C@H](O[C@@H]2C(C)=CC[C@@H]3C[C@@H](C[C@]4(CC[C@H](C)[C@@H](C5CCCCC5)O4)O3)OC(=O)[C@@H]3C=C(C)/C(=N/O)[C@H]4OCC(=CC=C[C@@H]2C)[C@@]34O)O[C@@H](C)[C@@H]1O,train
CC[C@@H](C(=O)[C@@H](C)[C@@H](O)[C@H](C)[C@@H]1O[C@@H]([C@@H](CC)C(=O)[O-])CC[C@@H]1C)[C@H]1O[C@]2(C=C[C@@H](O)[C@]3(CC[C@@](C)([C@H]4CC[C@](O)(CC)[C@H](C)O4)O3)O2)[C@H](C)C[C@@H]1C,train
CC(=O)Oc1cccc(O)c1,train
CNc1ccc(C(=O)CC(O)CCC(C)C2OC(=O)CC(O)CC(=O)CC(O)CC(O)CC(O)CC(O)CC3(O)CC(O)C(C(=O)OC)C(CC(O[C@@H]4O[C@H](C)[C@@H](O)[C@H](N)[C@@H]4O)C=CC=CC=CC=CC=CC=CC=CC2C)O3)cc1,train
CCC(=O)O[C@]1(C(=O)CCl)[C@@H](C)C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,valid
Cc1c(NC(C)C)c(=O)n(-c2ccccc2)n1C,train
CC(C)(C)C(=O)OCOC(=O)[C@@H]1N2C(=O)[C@@H](N=CN3CCCCCC3)[C@H]2SC1(C)C,train
COc1ccc2cc([C@H](C)CO)ccc2c1,train
CO[Si](CCCNc1ccccc1)(OC)OC,train
C#CCN(C)Cc1ccccc1,test
CN1C(=O)CC(c2ccccc2)C1=O,train
NC(Cc1ccccc1)C(=O)O,train
Cc1ccc(N(CC2=NCCN2)c2cccc(O)c2)cc1,train
CCCCCC,train
CCCCCC(C)=O,valid
CCOC(=O)CCCCCCCCC(=O)OCC,train
CC(=O)CCC(C)C,train
CC1(C)COP(Oc2ccccc2)OC1,train
O=C([O-])CN(CCN(CC(=O)[O-])CC(=O)[O-])CC(=O)[O-].[Fe+3],train
CCCCCCC(C)Nc1ccc(Nc2ccccc2)cc1,test
C=CC(=O)NC(C)(C)CS(=O)(=O)O,train
C=CC(=O)OCC(CC)(COC(=O)C=C)COC(=O)C=C,train
CC(CN)CCCN,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,train
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)C=C[C@@]43C)[C@@H]1CCC(=O)O2,valid
CN(C/C=C/C#CC(C)(C)C)Cc1cccc2ccccc12,train
CC1(O)CCC(C(C)(C)O)CC1,train
CC(C)(C)c1ccc(C(O)CCCN2CCC(C(O)(c3ccccc3)c3ccccc3)CC2)cc1,train
OCCOc1cccc(OCCO)c1,train
O=C1c2ccccc2C(=O)C1c1nc2ccccc2c(Br)c1O,test
O=[N+]([O-])O[Cd]O[N+](=O)[O-],train
CC(=O)OCCc1ccccc1,train
CNCCCC12CCC(c3ccccc31)c1ccccc12,train
CC(C)N1C(=O)c2ccccc2C1=O,train
CCCCN(CCCCO)N=O,valid
CC(C)(C)c1cc(CO)cc(C(C)(C)C)c1O,train
Cc1ccc(O)c(C(C)(C)C)c1,train
CCCCN(N=O)C(=N)N[N+](=O)[O-],train
COc1ccc(O)c(C(C)(C)C)c1,train
Cc1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,test
COC(=O)C1CC(=O)C(C(=O)OC)CC1=O,train
CC(Cl)COP(=O)(OCC(C)Cl)OCC(C)Cl,train
O=C(O)Cc1ccc(Cl)cc1Cl,train
OCc1ccc(Cl)cc1Cl,train
Nc1c(Cl)cc(Cl)cc1[N+](=O)[O-],valid
N#CSc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],train
COc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],train
C[N+](C)(C)CC1CO1,train
CCC1(C)NC(=O)NC1=O,train
CCCCCCCCN1C=CN(C)C1,test
CCC[N+]1(C)CCCCC1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,train
CCOC(=O)C(Cl)C(C)=O,train
O=C1Nc2ccc([N+](=O)[O-])cc2C1=O,train
O=c1[nH]c2ccccc2o1,train
CCCC[n+]1cccc(C)c1.F[B-](F)(F)F,valid
COC(=O)C(C)=O,train
CCOC(=O)c1ccc(N)cc1,train
CC(=O)OCC(COC(C)=O)OC(C)=O,train
CCCCC(CC)COC(C)=O,train
CCCCC(CC)COC(=O)CCCCCCCC(=O)OCC(CC)CCCC,test
C1CN(SSN2CCOCC2)CCO1,train
CC(COc1ccccc1)NC(C)C(O)c1ccc(O)cc1,train
CC(=O)C=Cc1ccc2c(c1)OCO2,train
CC(=O)OCc1ccc2c(c1)OCO2,train
CC1CCC2CC1C2(C)C,valid
CCNCC(O)c1cccc(O)c1,train
Cc1ccc(O)c([N+](=O)[O-])c1,train
O=C(OCCOCCOC(=O)c1ccccc1)c1ccccc1,train
CC(CNC1CCCCC1)OC(=O)c1ccccc1,train
Cc1cc(C)c(NC(=O)C[C@H](CC(=O)[O-])c2cccc3ccccc23)c(C(=O)N2CCC3(CCCC3)CC2)c1,test
O=C(NC(=O)c1c(F)cccc1F)Nc1cc(Cl)c(F)c(Cl)c1F,train
CC(=O)CC(=O)Nc1ccccc1C,train
N[C@@H](COP(=O)(O)O)C(=O)O,train
COCC(=O)O,train
CC(C)c1ncc([N+](=O)[O-])n1C,valid
CBr.CCN(CC)CCOC(=O)C(O)(c1ccccc1)c1ccccc1,train
CN/C(=N\C#N)NCCSCc1csc(N=C(N)N)n1,train
ClCc1ccccc1.O=C1C[C@@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@H]3C[C@H]46,train
O=C(O)c1cc2ccccc2cc1O,train
CNC(C)C(O)c1ccc(O)cc1,test
NC(=O)CC[C@@H](N)C(=O)O,train
CCCCCCCCCCCCCC[n+]1ccc(C)cc1,train
CC(C)C[C@@H](N)C(=O)O,train
NNC(N)=S,train
C=CC(=O)OCC1CCCO1,valid
O=C(N=NC(=O)OCc1ccccc1)OCc1ccccc1,train
O=C1OC(=O)c2cc(-c3ccc4c(c3)C(=O)OC4=O)ccc21,train
COC(=O)NCCC[Si](OC)(OC)OC,train
CCCOCCOCCOCCO,train
CCCCS(=O)(=O)Cl,test
C=C(C)C(=O)OCCOCCOC(=O)C(=C)C,train
CCCCC(CC)C(=O)[O-].CCCCC(CC)C(=O)[O-].[Ba+2],train
NC(N)=S,train
CCC(C)(O)CCCC(C)C,train
c1ccc(-c2cccc(-c3ccccc3)c2)cc1,valid
Cc1cc(Cl)ccc1OCCCC(=O)O,train
Cc1cc(Cl)ccc1OC(C)C(=O)O,train
Cc1cc(Cl)ccc1OCC(=O)O,train
O=C=Nc1ccc(Cc2ccc(N=C=O)cc2)cc1,train
CCC1(c2ccccc2)C(=O)NC(=O)NC1=O,test
CCC1(c2ccccc2)C(=O)N=C([O-])NC1=O,train
S=C=NCCc1ccccc1,train
C=CCOC(=O)c1ccccc1N,train
c1ccc2c(c1)Nc1ccccc1S2,train
Oc1ccccc1,valid
O=C1OC(c2ccc(O)cc2)(c2ccc(O)cc2)c2ccccc21,train
COc1ccc(CCC(=O)c2c(O)cc(O[C@@H]3O[C@H](CO)[C@@H](O)[C@H](O)[C@H]3O[C@@H]3O[C@@H](C)[C@H](O)[C@@H](O)[C@H]3O)cc2O)cc1O,train
CN(C)N=Nc1ccccc1,train
S=C=Nc1ccccc1,train
CC(=O)OC(C)OC(C)=O,test
C/C=C1\C(=O)C[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]12C,train
C/C=C1/C(=O)C[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]12C,train
CC(C)Cc1ccccc1,train
CCOC(=O)Cl,train
C[Si]1(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O1,valid
C[Si]1(C)O[Si](C)(C)O[Si](C)(C)O1,train
c1ccc2cc3ccccc3cc2c1,train
CCOC(=O)[C@H](CCc1ccccc1)N[C@@H](C)C(=O)N1Cc2cc(OC)c(OC)cc2C[C@H]1C(=O)O,train
CNCC[C@@H](Oc1ccccc1C)c1ccccc1,train
O=C(O)COCCN1CCN(C(c2ccccc2)c2ccc(Cl)cc2)CC1,test
Nc1ccncc1,train
Cc1ccc(/N=C/N(C)/C=N/c2ccc(C)cc2C)c(C)c1,train
NS(=O)(=O)[O-],train
COCCl,train
O=C(CCCN1CCC(O)(c2ccc(Br)cc2)CC1)c1ccc(F)cc1,valid
Cc1nnc2n1-c1sc(Br)cc1C(c1ccccc1Cl)=NC2,train
CCCCNc1cc(C(=O)O)cc(S(N)(=O)=O)c1Oc1ccccc1,train
C[N+](C)(C)CCCl,train
CC(C)(C)c1ccc(-c2ccc(C(C)(C)C)cc2)cc1,train
CCCCCCC(=O)c1ccccc1,test
CCC(C)(C)C1CCC(OC)CC1,train
Cc1ccc(S)cc1,train
O=C(O)Cc1cc(I)c(Oc2cc(I)c(O)c(I)c2)c(I)c1,train
CCc1cc(Cc2cc(CC)c(N)c(CC)c2)cc(CC)c1N,train
CCCCc1ccc(Cl)cc1,valid
CCCCCCCCCCCCOC(=O)c1cc(O)c(O)c(O)c1,train
COC(=O)[C@]1(C)CCC[C@]2(C)[C@H]3CCC(C(C)C)=CC3=CC[C@@H]12,train
ClC(Cl)(Cl)c1ccc(C(Cl)(Cl)Cl)cc1,train
O=C(O)c1ccccc1O,train
CCOP(=S)(OCC)SCCl,test
CCNS(=O)(=O)c1ccc(C)cc1,train
CCN1CCOCC1,train
CCN(Cc1ccccc1)c1ccccc1,train
C[C@]12C[C@H](O)[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@@H]2C(=O)CO,train
CCc1ccccc1O,valid
C=CC(=O)CC,train
CCC1(c2ccccc2)C(=O)NC(=O)N(C)C1=O,train
Cc1ccccc1OCC(O)CO,train
COc1ccccc1OCC1CNC(=O)O1,train
CC(C)[C@H](C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,test
CC(CC(C#N)(c1ccccc1)c1ccccc1)N(C)C,train
CCC(=O)O[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]12C,train
CC1=C(/C=C/C(C)=C/C=C\C(C)=C\C(=O)Nc2ccc(O)cc2)C(C)(C)CCC1,train
CC[Ge](Cl)(CC)CC,train
CCN1C=CN(C)C1C,valid
CCCC[N+]1(C)CCCCC1.O=S(=O)([O-])C(F)(F)F,train
O=Cc1cccc([N+](=O)[O-])c1,train
Cc1c(C)n(Cc2ccccc2)c2ccc(C(=O)OCCN(C)C)cc12,train
C[C@@H](CSC(=O)c1ccccc1)C(=O)N1C[C@@H](Sc2ccccc2)C[C@H]1C(=O)O,train
CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)[O-])=CCS[C@H]12)c1csc(N)n1,test
CCc1oc2ccccc2c1C(=O)c1cc(I)c(O)c(I)c1,train
CCC[N+]1(C)CCCC1.O=S(=O)([O-])C(F)(F)F,train
CCN(CC)CCOc1ccccc1OC,train
CCC1(c2ccccc2)C(=O)NC(=O)N(C(=O)c2ccccc2)C1=O,train
C=C1/C(=C\C=C2/CCC[C@@]3(C)[C@H]2CC[C@@H]3[C@H](C)/C=C/[C@@H](O)C2CC2)C[C@@H](O)C[C@@H]1O,valid
COP(=O)(OC)OC(Br)C(Cl)(Cl)Br,train
N#CC(C#N)=NNc1cccc(Cl)c1,train
Cc1cccc(O)c1,train
CSc1nnc(C(C)(C)C)c(=O)n1N,train
CCSC(=O)N1CCCCCC1,test
CCO,train
CC(C)=CCC/C(C)=C/COC=O,train
CN(C)C(=O)NC1(c2ccccc2)CCN(CCC[C@@]2(c3ccc(Cl)c(Cl)c3)CCCN(C(=O)c3ccccc3)C2)CC1,train
CCN(CC)CCNc1ccc(CNC=O)c2sc3ccc(OC)cc3c(=O)c12,train
N#C[C@@H]1CC(F)(F)CN1C(=O)CNC1CC2CCC(C1)N2c1ncccn1,valid
CC(C)(CO)C(O)C(=O)NCCCO,train
CCOP(=O)(Cl)Cl,train
CCCC[Sn](Cl)(CCCC)CCCC,train
COC(=O)c1cccc(C(=O)OC)c1,train
COC(C)(OC)OC,test
CC(C)CCC[C@@H](C)[C@H]1CC(=O)C2=C3CC[C@H]4C[C@@H](O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
CCOCC(=O)O,train
CC(=O)C1(C)CC2=C(CCCC2(C)C)CC1C,train
O=C1OC(OC(=O)c2cccnc2Nc2cccc(C(F)(F)F)c2)c2ccccc21,train
CCCCCCCCCOC(=O)CCCCC(=O)OCCCCCCC,valid
CCCC(=O)OCc1ccccc1,train
CCCCCCCCCCCCCCCC(=O)OCC,train
O=S1(=O)c2cccc3cccc(c23)N1CCCN1CCN(c2ccc(F)cc2)CC1,train
c1ccc([C@H]2CN3CCSC3=N2)cc1,train
CCCCCCOC(=O)C(C)C,test
COC(C)(C)CC(C)=O,train
CC(C)CCCCCCOC(=O)c1ccc(C(=O)OCCCCCCC(C)C)c(C(=O)OCCCCCCC(C)C)c1,train
COc1ccccc1/C=C/C=O,train
CC1(C)CC(OC(=O)CCCCCCCCC(=O)OC2CC(C)(C)NC(C)(C)C2)CC(C)(C)N1,train
COc1ccc(C(=O)Nc2ccccc2)cc1[N+](=O)[O-],valid
CC1=NN(c2cc(S(=O)(=O)[O-])ccc2Cl)C(=O)C1/N=N/c1ccccc1,train
Cc1ccc(C)c(S(=O)(=O)O)c1,train
O=Cc1ccccc1S(=O)(=O)[O-],train
COc1ccc2c3c1O[C@H]1C(=O)CC[C@H]4[C@@H](C2)N(C)CC[C@]314.COc1ccc2c3c1O[C@H]1C(=O)CC[C@H]4[C@@H](C2)N(C)CC[C@]314,train
CN(C)CCOc1ccccc1Cc1ccccc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,test
Cc1c(N)nc([C@H](CC(N)=O)NC[C@H](N)C(N)=O)nc1C(=O)N[C@H](C(=O)N[C@H](C)[C@@H](O)[C@H](C)C(=O)N[C@H](C(=O)NCCc1nc(-c2nc(C(=O)NCCCCNC(=N)N)cs2)cs1)[C@@H](C)O)[C@@H](O[C@@H]1O[C@@H](CO)[C@@H](O)[C@H](O)[C@@H]1O[C@H]1O[C@H](CO)[C@@H](O)[C@H](OC(N)=O)[C@@H]1O)c1cnc[nH]1,train
CC(=O)[C@]1(O)Cc2c(O)c3c(c(O)c2[C@@H](O[C@H]2C[C@H](N)[C@H](O)[C@H](C)O2)C1)C(=O)c1ccccc1C3=O,train
O=C(CCCN1CCC(O)(c2ccc(Cl)cc2)CC1)c1ccc(F)cc1,train
c1ccc2c(c1)CCNC2,train
c1ccc2c(c1)CCCC2,valid
Cc1cc(C)c(C)c(C)c1,train
COC(C)COC(C)COC(C)CO,train
COC(=O)CCCC(=O)OC,train
CCCCC(CC)COC(=O)c1ccc(N(C)C)cc1,train
CCCCCC1C(=O)CCC1CC(=O)OC,test
C=CCOc1c(Br)cc(C(C)(C)c2cc(Br)c(OCC=C)c(Br)c2)cc1Br,train
OCCOCC#CCOCCO,train
NCCOCCN,train
CCCCOC(=O)C(=O)OCCCC,train
OCCN(CCO)CCN(CCO)CCO,valid
CCC1COC(Cn2cncn2)(c2ccc(Cl)cc2Cl)O1,train
CCCCCCC=O,train
CCN1CCN(c2cc3c(cc2F)c(=O)c(C(=O)O)cn3C2CC2)CC1,train
CCCCN1C[C@H](O)[C@@H](O)[C@H](O)[C@H]1CO,train
Fc1ccc(C(c2ccc(F)cc2)N2CCN(C/C=C/c3ccccc3)CC2)cc1,test
O=S(=O)(c1cccc2cnccc12)N1CCCNCC1,train
CN1C[C@H](C(=O)N[C@]2(C)O[C@@]3(O)[C@@H]4CCCN4C(=O)[C@H](Cc4ccccc4)N3C2=O)C[C@@H]2c3cccc4[nH]cc(c34)C[C@H]21,train
CC(C)[C@@H]1CC[C@@H](C)CC1=O,train
COc1ccc2c(c1OC)C(=O)O[C@H]2[C@@H]1c2cc3c(cc2CCN1C)OCO3,train
CCOc1nc(F)cc2nc(S(=O)(=O)Nc3c(Cl)cccc3Cl)nn12,valid
CO[C@H]1C=CO[C@@]2(C)Oc3c(C)c(O)c4c(O)c(cc([O-])c4c3C2=O)NC(=O)C(C)=CC=C[C@H](C)[C@H](O)[C@@H](C)[C@@H](O)[C@@H](C)[C@H](OC(C)=O)[C@@H]1C,train
Cc1ncsc1CCO,train
c1ccc(Sc2ccccc2)cc1,train
CCCCCCCCCCCC(=O)O,train
O=C(O)CCl,test
C[C@H]1C(=O)O[C@@H]2CCN3CC=C(COC(=O)[C@](C)(O)[C@]1(C)O)[C@H]23,train
CN(C)CCN1C(=O)c2ccccc2N(C)c2ccccc21,train
O=C(Cl)c1ccc(C(=O)Cl)cc1,train
COc1c(N2C[C@@H]3CCCN[C@@H]3C2)c(F)cc2c(=O)c(C(=O)O)cn(C3CC3)c12,train
CCCc1nc(C(C)(C)O)c(C(=O)OCc2oc(=O)oc2C)n1Cc1ccc(-c2ccccc2-c2nn[nH]n2)cc1,valid
CC(C)CC(N(C)C)C1(c2ccc(Cl)cc2)CCC1,train
C[C@@H]1O[C@@H](OC[C@H]2O[C@@H](Oc3c(-c4ccc(OCCO)c(OCCO)c4)oc4cc(OCCO)cc(O)c4c3=O)[C@H](O)[C@@H](O)[C@@H]2O)[C@H](O)[C@H](O)[C@H]1O,train
CC1(C)CC(N)CC(C)(C)N1,train
CC(O)[C@@H]1NC(=O)[C@H](CCCCN)NC(=O)[C@@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](Cc2ccccc2)NC(=O)[C@@H](NC(=O)[C@H](N)Cc2ccccc2)CSSC[C@@H](C(=O)N[C@H](CO)[C@@H](C)O)NC1=O,train
CCC(C)SSC(C)CC,test
Cc1cccc(OCCOc2cccc(C)c2)c1,train
CC(C)c1ccc2sc3ccccc3c(=O)c2c1,train
O=C(CCN1CC1)OCC(CO)(COC(=O)CCN1CC1)COC(=O)CCN1CC1,train
C#CC(O)C(CC)CCCC,train
CCCCCCCCCCCCCC=O,valid
O=CC(Br)(Br)Br,train
CCCCCCCCCCCCCC(=O)O,train
ClC1=C(Cl)C1(Cl)Cl,train
O=C(O)C(Br)(Br)Br,train
C[C@H]1CCC[C@H](O)CCCCCc2cc(O)cc(O)c2C(=O)O1,test
O=c1c(-c2ccc(O)cc2)coc2cc(O)ccc12,train
CCCCCCCCc1ccc(O)cc1,train
CCCCCCCO,train
N#CCCCCC#N,train
CCCCCCO,valid
CCCCCCN,train
OCCNCCO,train
C=C(C)CNCC,train
CCCCC(CC)C(=O)OCCOCCOCCOCCOC(=O)C(CC)CCCC,train
N#Cc1nc(Cl)c(Cl)c(Cl)c1Cl,test
O=C1c2ccccc2C(=O)N1SC1CCCCC1,train
CC[Si](OC(C)=O)(OC(C)=O)OC(C)=O,train
CCCOCC(C)OCC(C)O,train
O=C1c2ccccc2C(=O)N1CCl,train
O=C1Nc2ccccc2C1(c1ccc(O)cc1)c1ccc(O)cc1,valid
CCOc1ccc(NC(=O)CC(C)=O)cc1,train
O=C(CS)OCCOC(=O)CS,train
CC(O)C(=O)O[Hg]c1ccccc1,train
CCN(CC)C(=O)N(CC)CC,train
Cc1ccc(N)cc1S(=O)(=O)O,test
CCOC(=O)c1ccccc1C(=C1C=C(Br)C(=O)C(Br)=C1)c1cc(Br)c(O)c(Br)c1,train
CC[C@]1(O)C[C@H]2CN(CCc3c([nH]c4ccccc34)[C@@](C(=O)OC)(c3cc4c(cc3OC)N(C=O)[C@H]3[C@@](O)(C(=O)OC)[C@H](OC(C)=O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)C2)C1,train
COC(=O)COc1ccc(Cl)cc1Cl,train
O=[N+]([O-])c1ccc(Cl)c([N+](=O)[O-])c1,train
C=C(C)CCl,valid
O=C1OC(O)C(C(Cl)Cl)=C1Cl,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C=C(Cl)C4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
Nc1ccc(Oc2ccc(Cl)cc2)cc1,train
Cc1cc(O)ccc1N,train
Nc1ccc2c3c(cccc13)-c1ccccc1-2,test
Cc1cccc(O)c1N,train
CCCCCCCCCCCCCCCCn1cc[n+](C)c1.F[B-](F)(F)F,train
CCCCCCCCCCl,train
N#Cc1cc2ccccc2c2ccccc12,train
Oc1cc(Cl)ccc1Oc1ccc(Cl)cc1Cl,valid
CC(C)(C)C(O)C(Oc1ccc(Cl)cc1)n1cncn1,train
O=C(O)COc1nc(Cl)c(Cl)cc1Cl,train
Nc1ccc(N=Nc2ccccc2)c(N)n1,train
Cc1cccc(S(=O)(=O)[O-])c1C,train
CCCCCCCCCCCCOCC1CO1,test
CCCCCCCCCCCCCl,train
CCCCCCCCCCCC(=O)N(CCO)CCO,train
C=Cc1c(C)c2cc3[nH]c(cc4nc(cc5[nH]c(cc1n2)c(C)c5CCC(=O)[O-])c(CCC(=O)[O-])c4C)c(C)c3C=C,train
CC(C)CCOC(=O)c1ccc(N(C)C)cc1,train
Oc1cc(O)cc(O)c1,valid
O=c1[nH]c(=O)c2ccccc2o1,train
O=C(O)c1ccc(OCCCOc2ccc(C(=O)O)cc2)cc1,train
C=CC(C)(CCC=C(C)C)OC(C)=O,train
OCC(CO)(CO)CO,train
O=C(O)C(=O)Nc1cccc(-c2nnn[nH]2)c1,test
C1CCC(C(CC2CCCCN2)C2CCCCC2)CC1,train
Cc1ccc(Nc2nccc(N(C)c3ccc4c(C)n(C)nc4c3)n2)cc1S(N)(=O)=O,train
CC(C)OC(=O)OCOP(=O)(CO[C@H](C)Cn1cnc2c(N)ncnc21)OCOC(=O)OC(C)C,train
CC(=O)N[C@H]([C@@H](O)CC(=O)C(=O)O)[C@@H](O)[C@H](O)[C@H](O)CO,train
O=C(C=Cc1ccc(O)c(O)c1)O[C@@H]1C[C@@](OC(=O)C=Cc2ccc(O)c(O)c2)(C(=O)O)C[C@@H](O)[C@@H]1O,valid
CO[C@H](C(=O)[C@@H](O)[C@@H](C)O)[C@@H]1Cc2cc3cc(O[C@H]4C[C@@H](O[C@@H]5C[C@@H](O)[C@@H](OC)[C@@H](C)O5)[C@@H](OC(=O)O)[C@@H](C)O4)c(C)c(O)c3c(O)c2C(=O)[C@H]1O[C@H]1C[C@@H](O[C@H]2C[C@@H](O[C@H]3C[C@](C)(O)[C@@H](OC(C)=O)[C@H](C)O3)[C@H](O)[C@@H](C)O2)[C@H](O)[C@@H](C)O1,train
CCCCCCC(=O)CCCCCC/C=C/C[C@@H](O)[C@H](O)[C@@](N)(CO)C(=O)O,train
CCC1OC(=O)C[C@@H](O)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@@H](CC=O)C[C@@H](C)C(=O)C=C[C@]2(C)OC2C1C,train
CC(C)CCOC(=O)c1ccccc1,train
Cc1cc(C)c(C)c(O)c1,test
CCC(=O)OCCC(C)CCC=C(C)C,train
CC(C)COC(=O)c1ccccc1O,train
CC(C)CCCCCCCOC(=O)c1ccc(C(=O)OCCCCCCCC(C)C)c(C(=O)OCCCCCCCC(C)C)c1,train
CCCCCCCCCCCC(=O)O.OCCN(CCO)CCO,train
CCCCCCCCC(=O)OCC(C)OC(=O)CCCCCCCC,valid
CCCCCCCCCCOC(C)=O,train
CC(=O)OCCC1CCCCC1,train
COP(=O)(OC)OC(=CCl)c1cc(Cl)c(Cl)cc1Cl,train
CCN(CC)C(=S)SSC(=S)N(CC)CC,train
CCCCCc1cc(O)c2c(c1)OC(C)(C)[C@@H]1CCC(C)=C[C@@H]21,test
C1CCOC1,train
CN1C(C)(C)CCCC1(C)C,train
CC1=CCC(C(C)(C)O)CC1,train
CCN(Cc1cccc(S(=O)(=O)O)c1)c1ccccc1,train
CC(=O)c1ccc(O)cc1,valid
Cc1cc(C(C)(C)C)cc(C)c1CC1=NCCN1,train
F[Sn](c1ccccc1)(c1ccccc1)c1ccccc1,train
CCOc1ccc(O)cc1,train
O=S1(=O)OCC2C(CO1)C1(Cl)C(Cl)=C(Cl)C2(Cl)C1(Cl)Cl,train
CCSCc1ccccc1OC(=O)NC,test
Brc1nc2ccccc2s1,train
CCC(C)Br,train
N#CCc1ccccc1,train
O=C(O)c1ccc(CO)o1,train
N#Cc1ccccc1,valid
N#CCBr,train
O=C(O)CBr,train
CN1CC(O)C2=C/C(=N/NC(N)=O)C(=O)C=C21,train
Cc1ccc(C2(O)CCN(CCCC(=O)c3ccc(F)cc3)CC2)cc1,train
O=S(=O)(O)F,test
C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
N=C(N)c1ccc(OCCCOc2ccc(C(=N)N)cc2Br)c(Br)c1,train
CN1[C@H]2CCC[C@@H]1C[C@H](NC(=O)c1nn(C)c3ccccc13)C2,train
O=C(Cl)/C=C/C(=O)Cl,train
Cc1nc2n(c(=O)c1CCN1CCC(c3noc4cc(F)ccc34)CC1)CCC[C@H]2O,valid
COc1cc(C(C)=O)ccc1OCCCN1CCC(c2noc3cc(F)ccc23)CC1,train
CN(C(=O)CO)c1c(I)c(C(=O)NCC(O)CO)c(I)c(C(=O)NCC(O)CO)c1I,train
O=C1OC2(c3ccccc31)c1cc(I)c(O)c(I)c1Oc1c2cc(I)c(O)c1I,train
Nc1c(CC(=O)[O-])cccc1C(=O)c1ccc(Br)cc1.Nc1c(CC(=O)[O-])cccc1C(=O)c1ccc(Br)cc1,train
COCCCOc1ccnc(CS(=O)c2nc3ccccc3[n-]2)c1C,test
COCC(=O)O[C@]1(CCN(C)CCCc2nc3ccccc3[nH]2)CCc2cc(F)ccc2[C@@H]1C(C)C,train
CCc1ccc(CCOc2ccc(CC3SC(=O)NC3=O)cc2)nc1,train
CCCCCCCCOC(C)=O,train
OCCOCCN1CCN(C2=Nc3ccccc3Sc3ccccc32)CC1.OCCOCCN1CCN(C2=Nc3ccccc3Sc3ccccc32)CC1,train
O=C(O)CCSCCC(=O)O,valid
Cc1nc[nH]c1CN1CCc2c(c3ccccc3n2C)C1=O,train
O=C(O)CCCC[C@@H]1SC[C@@H]2NC(=O)N[C@H]12,train
C[C@](O)(CS(=O)(=O)c1ccc(F)cc1)C(=O)Nc1ccc(C#N)c(C(F)(F)F)c1,train
CC(C[N+](C)(C)C)OC(N)=O,train
CC(C)NCC(O)COc1ccc(CCOCC2CC2)cc1,test
CCCCC(=O)O[C@]1(C(=O)CO)[C@@H](C)C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
CCCC(=O)O[C@]1(C(=O)COC(=O)CC)[C@@H](C)C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,train
O=C(CBr)OCc1ccccc1,train
CCCCCc1ccc(Cl)cc1,train
COC(=O)c1ccccc1C,valid
CC(C)(C)C(=O)c1ccccc1,train
O=C(Oc1ccccc1)c1ccccc1,train
CCOC(=O)c1ccc(C)cc1,train
Cc1cc(C)cc(N)c1,train
CC(C)c1c(C(=O)Nc2ccccc2)c(-c2ccccc2)c(-c2ccc(F)cc2)n1CC[C@@H](O)C[C@@H](O)CC(=O)O,test
Cc1cc(C2(c3cc(C)c(O)cc3C)OS(=O)(=O)c3ccccc32)c(C)cc1O,train
Cc1ccc(S(=O)(=O)[O-])cc1C,train
Cc1cccc(C)c1N,train
Cc1ccc(C)c(N)c1,train
Cc1cccc(N)c1C,valid
CC=Cc1ccc(O)c(OC)c1,train
FC(F)(F)C(Cl)Br,train
O=C(O)c1ccc(S(=O)(=O)N(Cl)Cl)cc1,train
CCCCOC(=O)c1ccc(N)cc1,train
Cc1ccc2c(C)cccc2c1,test
CCN(CC)C(=O)[C@@H]1C=C2c3cccc4[nH]cc(c34)C[C@H]2N(C)C1,train
OC[C@@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,train
CN1CCN(CCCN2c3ccccc3Sc3ccc(S(=O)(=O)N(C)C)cc32)CC1,train
CCNCC.O=S(=O)(O)c1cc(O)ccc1O,train
Cc1nn(-c2cc(NS(C)(=O)=O)c(Cl)cc2Cl)c(=O)n1C(F)F,valid
OC(c1ccc(Cl)cc1)(c1cncnc1)c1ccccc1Cl,train
CCOC(=O)C(C)Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1,train
CCOC(=O)NCCOc1ccc(Oc2ccccc2)cc1,train
COc1c(O)cc2c(c1O)[C@@H]1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1OC2=O,train
Cc1onc(-c2ccccc2Cl)c1C(=O)N[C@@H]1C(=O)N2[C@@H](C(=O)[O-])C(C)(C)S[C@H]12,test
CC(=O)O[C@H]1C[C@@]2(C)[C@@H](C[C@@H](O)[C@H]3[C@@]4(C)CC[C@@H](O)[C@@H](C)[C@@H]4CC[C@@]32C)/C1=C(\CCC=C(C)C)C(=O)[O-],train
N#Cc1c[nH]cc1-c1cccc2c1OC(F)(F)O2,train
CCN(CC)C(=O)N1CCN(C)CC1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
CCN(CC)CCc1nc(-c2ccccc2)no1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
CN1[C@H]2CC[C@@H]1C[C@H](OC1c3ccccc3CCc3ccccc31)C2.O=C(O)CC(O)(CC(=O)O)C(=O)O,valid
COc1ccc(C=O)cc1,train
COP(=S)(OC)Oc1c(Cl)cc(C)cc1Cl,train
O=C(NC(=O)c1c(F)cccc1F)Nc1ccc(OC(F)(F)C(F)OC(F)(F)F)c(Cl)c1,train
CCCOC(=O)c1ccc(C(=O)OCCC)nc1,train
CC(O)CCO,test
O=C(O)CCS,train
CCCC[n+]1ccc(C)cc1.O=S(=O)([O-])C(F)(F)F,train
CCC[N+]1(C)CCCCC1.F[B-](F)(F)F,train
CC(=O)C1CCCCC1=O,train
CCCC[n+]1cccc(C)c1.O=S(=O)([O-])C(F)(F)F,valid
CCCCN1C=CN(C)C1,train
CC1(C(=O)Nc2ccc(O)c(Cl)c2Cl)CCCCC1,train
CCC[N+]1(C)CCCCC1,train
CCCC[N+]1(C)CCCCC1.F[B-](F)(F)F,train
N=C(N)N[N+](=O)[O-],test
CC(C)(C)OOC(C)(C)C,train
CCC(C)Nc1ccc(NC(C)CC)cc1,train
CCCCNCCCC,train
CCCCN(CCO)CCCC,train
O=C(O)c1ccc2[nH]cc(CCCCN3CC=C(c4ccccc4)CC3)c2c1,valid
CN1C2CCC1CC(OC(c1ccccc1)c1ccc(Cl)cc1)C2,train
Oc1ccc(C2CNCCc3c2cc(O)c(O)c3Cl)cc1,train
Oc1ccc2[nH]cc(CCCCN3CC=C(c4ccccc4)CC3)c2c1,train
C[C@@H](/C=C/[C@@H](C)[C@H]1CC[C@H]2/C(=C/C=C3C[C@@H](O)C[C@H](O)C3)CCC[C@@]21C)C(C)(C)O,train
CCOC(=O)NNc1nncc2ccccc12,test
CC(C)(C)NCCNC(C)(C)C,train
CCCCC(F)(F)[C@@]1(O)CC[C@H]2[C@@H](CC(=O)[C@@H]2CCCCCCC(=O)O)O1,train
CC(C)=CCC/C(C)=C/CC/C(C)=C/CCC(=O)OC/C=C(\C)CCC=C(C)C,train
CCN(CC)Cc1nccn1-c1ccc([N+](=O)[O-])cc1C(=O)c1ccccc1Cl,train
OC1CCCCCCCCCCC1,valid
CCCCS(=O)(=O)N[C@@H](Cc1ccc(OCCCCC2CCNCC2)cc1)C(=O)O,train
COC(=O)c1ccc(C(=O)O)cc1,train
CN(C)CCOCCO,train
CC(C)[N+](C)(C)CC(O)COc1cccc2ccccc12,train
Cc1ccc(S(=O)(=O)NC(=O)N[C@@H](C)Cc2ccccc2)cc1,test
Nc1ccc(S(=O)(=O)NCc2ccccc2)cc1,train
CC=CC=NNC(=O)c1ccncc1,train
O=C1C(=O)c2ncccc2-c2cccnc21,train
CC12CCC(C[N+](C)(CCC[N+](C)(C)C)C1)C2(C)C.COS(=O)(=O)[O-].COS(=O)(=O)[O-],train
Nc1ccc(C(=O)Nc2ccccc2N)cc1,valid
C[N+](C)(C)CCOC(=O)C(O)(c1ccccc1)c1ccccc1,train
O=C(O)C=CC(=O)Nc1ccc(S(=O)(=O)Nc2nccs2)cc1,train
FC(F)(F)C(F)(F)C(F)(F)C(F)(F)CCI,train
CC/C=C\CCOC(=O)CC,train
CO[PH](=O)OC,test
C=CC(C)(CCC=C(C)C)OC(=O)CC,train
CN1C(=O)CC[C@H]1c1cccnc1,train
C[C@H]1C[C@@H](O)CC(C)(C)C1,train
ClP(Cl)c1ccccc1,train
C1COCO1,valid
Cc1c(O)cccc1O,train
CCOC(=O)CC([O-])C(=O)OCC,train
c1ccc(CCc2ccccc2)cc1,train
COC(=O)c1ccccc1,train
C=CC(=O)OCC(C)OCC(C)OCC(C)OC(=O)C=C,test
Cc1ccc(CO)cc1,train
C/C=C(/C)C#N,train
C=CC(=O)OCC[N+](C)(C)C,train
O=C(C=Cc1ccccc1)OCc1ccccc1,train
N#Cc1ccccc1O,valid
CCCCCCCCCCCCCCCC[N+](C)(C)Cc1ccccc1,train
CC(N)C12CC3CC(CC(C3)C1)C2,train
CC(C)(N)Cc1ccc(Cl)cc1,train
CCCCC(CC)COC(=O)COc1ccc(Cl)cc1C,train
CN(C)CC[C@@H](c1ccc(Br)cc1)c1ccccn1,test
O[C@@H](c1cc(C(F)(F)F)nc2c(C(F)(F)F)cccc12)[C@H]1CCCCN1,train
O=C(O)CCC(=O)O.c1ccc(C2(c3ccccc3)CC2C2=NCCN2)cc1,train
CC1NCCOC1c1ccccc1,train
CCCCCC(CO)CCC,train
Cc1cccc2[nH]nnc12,valid
CCCCCCCCCCCC(=O)OC[C@@H](O)[C@H]1OC[C@H](OCCO)[C@H]1OCCO,train
CS(=O)(=O)OCC(O)C(O)COS(C)(=O)=O,train
C=CCN(CC=C)CC=C,train
C=CCn1c(=O)n(CC=C)c(=O)n(CC=C)c1=O,train
O=C(NCO)NCO,test
Cc1cccc(O)c1C,train
Cc1ccc(C)c(O)c1,train
Cc1cc(C)cc(O)c1,train
COc1cc2c(cc1O)CCN[C@]21CS[C@@H]2c3c(OC(C)=O)c(C)c4c(c3[C@H](COC1=O)N1[C@@H](O)[C@@H]3Cc5cc(C)c(OC)c(O)c5[C@H]([C@H]21)N3C)OCO4,train
Nc1cc(=O)nc(N)[nH]1,valid
Nc1c[n+](N2CCOCC2)no1,train
CC(C)(Cl)Cl,train
O=C(O)Cc1ccc(OCCNC[C@H](O)c2ccccc2)cc1,train
CN1CCCN(C(c2ccccc2)c2ccc(Cl)cc2)CC1,train
Cn1cc(NC(=O)c2cc(NC(=O)c3cc(NC=O)cn3C)cn2C)cc1C(=O)NCCC(=N)N,test
CC(=O)O[C@H]1CC[C@@]2(C)C(=CC[C@H]3[C@@H]4CC=C(c5cccnc5)[C@@]4(C)CC[C@@H]32)C1,train
c1ccc(-c2ccc(C(c3ccccc3)n3ccnc3)cc2)cc1,train
CCCCC(=O)O[C@]1(C(=O)CO)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3[C@@H](O)C[C@@]21C,train
CC[N+](C)(CC)CCOC(=O)C(O)(c1ccccc1)C1CCCCC1,train
O=C(O)c1ccc(OCCn2ccnc2)cc1,valid
CCCCCCCCNC(C)C(O)c1ccc(SC(C)C)cc1,train
COc1cc(OC)nc(NC(=O)[N-]S(=O)(=O)c2ncccc2OCC(F)(F)F)n1,train
CN/C(=N/[N+](=O)[O-])NCC1CCOC1,train
CC1(C)OC[C@@H]2O[C@@]3(C(=O)[O-])OC(C)(C)O[C@H]3[C@@H]2O1,train
O=Cc1ccccc1Cl,test
Nc1cccc(Cl)c1,train
CCOc1ccc(N)c([N+](=O)[O-])c1,train
Cc1ccc(-c2nc3ccc(C)cn3c2CC(=O)N(C)C)cc1,train
CN1CCCC(n2nc(Cc3ccc(Cl)cc3)c3ccccc3c2=O)CC1,train
O=C1C=C2C=C[C@@H]3C[C@@]2(O1)[C@H]1CCCCN31,valid
Clc1ccc(CO/N=C(\Cn2ccnc2)c2ccc(Cl)cc2Cl)c(Cl)c1,train
CCCCCCCCCCCCCCCCOP(=O)([O-])OCC[N+](C)(C)C,train
CC(=O)Nc1ccccc1O,train
CCCc1cc(C(N)=S)ccn1,train
CC(=O)Nc1cccc(O)c1,test
CO/N=C1\CN(c2nc3c(cc2F)c(=O)c(C(=O)O)cn3C2CC2)CC1CN,train
C=CCCC#N,train
C=CC/C=C/C,train
CCCCOC(=O)CC,train
CC1=CCC(=C(C)C)CC1,valid
CCCCOC(=O)COC(=O)c1ccccc1C(=O)OCCCC,train
Fc1ccc([C@@H]2CCNC[C@H]2COc2ccc3c(c2)OCO3)cc1,train
CCSC(=O)N(CC(C)C)CC(C)C,train
NC1=NC(=O)C(c2ccccc2)O1,train
CCC(=O)c1ccc(O)cc1,test
CCCCCCCC(=O)Oc1c(Br)cc(C#N)cc1Br,train
CC(=O)OCC(=O)[C@@]1(O)[C@H](C)C[C@H]2[C@@H]3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@H]3[C@@H](O)C[C@@]21C,train
CCCCCCCCCCC,train
CCCCCCCCCCC=O,train
CC1=CC(=O)CC(C)(C)C1=O,valid
CCCCCCCCCCCCC(=O)O,train
O=C(O)CCCCCCCCCCCC(=O)O,train
O=C(CCl)C(Cl)Cl,train
CCC(C)c1ccc(O)cc1,train
CCCCCCCC(=O)N(C)C,test
CCC(C)c1ccccc1O,train
CN(C)CCCCCCN(C)C,train
O=C(O)c1nn(Cc2ccc(Cl)cc2Cl)c2ccccc12,train
CC[C@H](C)C(=O)O[C@H]1C[C@@H](C)C=C2C=C[C@H](C)[C@H](CC[C@@H]3C[C@@H](O)CC(=O)O3)[C@H]21,train
O=C(Nc1cccc([N+](=O)[O-])c1)c1cc2ccccc2cc1O,valid
CN(C)c1ccc(O)c2c1C[C@H]1C[C@H]3[C@H](N(C)C)C(O)=C(C(N)=O)C(=O)[C@@]3(O)C(O)=C1C2=O,train
CCCCCCCC1CCCC1=O,train
CC1=CC(C)C(C=O)C(C)C1,train
O=S(=O)([O-])c1ccc2cc(O)c(S(=O)(=O)[O-])cc2c1,train
NC[C@H]1O[C@H](O[C@@H]2[C@@H](N)C[C@@H](N)[C@H](O[C@H]3O[C@H](CO)[C@@H](O)[C@H](N)[C@H]3O)[C@H]2O)[C@H](N)C[C@@H]1O,test
Cc1ccc(C(=O)c2cc(O)c(O)c([N+](=O)[O-])c2)cc1,train
N#C[Au-]C#N,train
Cc1ccc(O)c([C@H](CCN(C(C)C)C(C)C)c2ccccc2)c1,train
CC(C)N1CCN(c2ccc(OC[C@H]3CO[C@](Cn4cncn4)(c4ccc(Cl)cc4Cl)O3)cc2)CC1,train
CCCCCCCCCCCCn1cc[n+](C)c1.O=S(=O)([N-]S(=O)(=O)C(F)(F)F)C(F)(F)F,valid
CCCCCCCCCCn1cc[n+](C)c1.O=S(=O)([O-])C(F)(F)F,train
Clc1ccc(CSC(Cn2ccnc2)c2ccc(Cl)cc2Cl)cc1,train
COc1cc2nc(N3CCN(C(=O)C4CCCO4)CC3)nc(N)c2cc1OC,train
OCCOc1ccccc1,train
CNCC(O)c1ccc(OC(=O)C(C)(C)C)c(OC(=O)C(C)(C)C)c1,test
O=C(OCC(O)CO)c1ccccc1Nc1ccnc2cc(Cl)ccc12,train
CN(C/C=C/c1ccccc1)Cc1cccc2ccccc12,train
O=NN1CCN(N=O)CC1,train
C=C(C)[C@H]1CC=C(C)C(=O)C1,train
Oc1ccccc1O,valid
ClC(Cl)(Cl)Cl,train
O=[N+]([O-])c1ccc(O)c([N+](=O)[O-])c1,train
CCC(Br)(CC)C(=O)NC(N)=O,train
CC1=C(/C=C/C(C)=C/C=C/C(C)=C/C=C\C=C(C)\C=C\C=C(C)\C=C\C2=C(C)CCCC2(C)C)C(C)(C)CCC1,train
Cc1c([N+](=O)[O-])cccc1[N+](=O)[O-],test
Cc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],train
CC1=C(C(=O)O)N2C(=O)[C@@H](NC(=O)[C@H](N)C3=CCC=CC3)[C@H]2SC1,train
CCCCC(CC)COC(=O)COc1cc(Cl)c(Cl)cc1Cl,train
CCCCOCCOC(=O)COc1cc(Cl)c(Cl)cc1Cl,train
O=C(O)c1cccnc1C(=O)O,valid
CC(O)C(C)O,train
CCCC[N+]1(C)CCCC1.F[B-](F)(F)F,train
CCC(=O)c1cccc(Cl)c1,train
CCOS(=O)(=O)[O-].CCn1cc[n+](C)c1,train
CCCC[n+]1ccc(C)cc1.F[B-](F)(F)F,test
CCCCCCCCCCCCCCCCCBr,train
O=[N+]([O-])c1ccc(F)c(Cl)c1,train
CC[n+]1ccccc1.F[B-](F)(F)F,train
CCCCn1cc[n+](C)c1.Cl[Fe-](Cl)(Cl)Cl,train
O=C(Nc1ccc(F)cc1F)c1cccnc1Oc1cccc(C(F)(F)F)c1,valid
OCC=Cc1ccccc1,train
C/C=C(\C)C(C)=O,train
CCCC(CC)c1ccccc1,train
CCCCC(CC)C(=O)OCCOCCOCCOC(=O)C(CC)CCCC,train
Cc1cc(C(C)(C)c2cc(C)c(O)c(C)c2)cc(C)c1O,test
CCCSC(=O)Cl,train
O=C(O)C(Cc1cc(I)c(O)c(I)c1)c1ccccc1,train
CC(=O)/C=C/c1ccccc1,train
FC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)Br,train
NCc1ccccc1Sc1ccccc1CO,valid
CCCc1ccc(C(=O)O)cc1,train
O=C(O)c1ccccc1-c1ccc(C(F)(F)F)cc1,train
C=CCCCCCCCCC(=O)OC,train
CC(=O)Oc1ccc(C)cc1,train
O=C([O-])c1nc(O)nc(O)n1,test
[O-][n+]1cccc(CO)c1,train
OC[C@H]1O[C@@H](O)[C@H](O)[C@@H](O)[C@@H]1O[C@@H]1O[C@H](CO)[C@H](O)[C@H](O)[C@H]1O,train
CNc1c2c(nn1C)CCCC2,train
CC(O)C(=O)O,train
Nc1nnc(-c2cccc(Cl)c2Cl)c(N)n1,valid
O=C(O)c1cc(C(=O)O)cc([N+](=O)[O-])c1,train
CCOCN(C(=O)CCl)c1c(C)cccc1CC,train
COc1cc2c(cc1OC)C(C(C(=O)N(CCc1ccc(OC)c(OC)c1OC)CCc1ccc(OC)c(OC)c1OC)c1ccccc1)=NCC2,train
COC(=O)C(CN)c1c[nH]c2ccc(OC)cc12,train
CN1CCC(Nc2ncc3ncnc(Nc4ccc(F)c(Cl)c4)c3n2)CC1,test
Cc1cccc(C)c1NC(=O)c1ccccc1C(=O)O,train
OB(O)O[Hg]c1ccccc1,train
CC(=O)c1sc(C)nc1C,train
CCC(=O)C(=O)CC,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](N)c3ccccc3)C(=O)N2[C@H]1C(=O)OCOC(=O)[C@@H]1N2C(=O)C[C@H]2S(=O)(=O)C1(C)C,valid
CN[C@@H]1[C@H](O[C@H]2[C@H](O[C@@H]3[C@@H](NC(=N)N)[C@H](O)[C@@H](NC(=N)N)[C@H](O)[C@H]3O)O[C@@H](C)[C@]2(O)CO)O[C@@H](CO)[C@H](O)[C@H]1O.CN[C@@H]1[C@H](O[C@H]2[C@H](O[C@@H]3[C@@H](NC(=N)N)[C@H](O)[C@@H](NC(=N)N)[C@H](O)[C@H]3O)O[C@@H](C)[C@]2(O)CO)O[C@@H](CO)[C@H](O)[C@H]1O,train
CN[C@@H](C)[C@H](O)c1ccccc1,train
CN1C(=O)C(O)N=C(c2ccccc2)c2cc(Cl)ccc21,train
CCC(C)(CC)c1cc(NC(=O)c2c(OC)cccc2OC)on1,train
CC(C)NC(=O)N1CC(=O)N(c2cc(Cl)cc(Cl)c2)C1=O,test
CC/C(=C(\c1ccccc1)c1ccc(OCCN(C)C)cc1)c1ccccc1.O=C(O)CC(O)(CC(=O)O)C(=O)O,train
ClC=CCCl,train
NCCS(=O)(=O)O,train
O=c1[nH]c(=O)n(C2CCCO2)cc1F,train
CC(C)(C)c1cc(CCC(=O)OCC(COC(=O)CCc2cc(C(C)(C)C)c(O)c(C(C)(C)C)c2)(COC(=O)CCc2cc(C(C)(C)C)c(O)c(C(C)(C)C)c2)COC(=O)CCc2cc(C(C)(C)C)c(O)c(C(C)(C)C)c2)cc(C(C)(C)C)c1O,valid
CC(C)C(=O)OCC(C)(C)C(OC(=O)C(C)C)C(C)C,train
CCCCCCCN,train
Cc1cccc2sc(N)nc12,train
COc1ccc2nc(N)sc2c1,train
CCOc1ccc2nc(N)sc2c1,test
ClC1C(Cl)C(Cl)C(Cl)C(Cl)C1Cl,train
Cc1ccc(O)c(N)c1,train
Cc1ccc(N)cc1O,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@@H](Cl)[C@@H]1Cl,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H](Cl)[C@H]1Cl,valid
CCOP(O)OCC,train
C[Si](C)(C)O[Si](O)(O)O,train
OCCc1ccncc1,train
C=C(C)C(=O)OCCOP(=O)(O)O,train
O=C(c1cc(O)c(O)c(O)c1)c1ccc(O)c(O)c1O,test
O=C1[C@H](CC[C@H](O)c2ccc(F)cc2)[C@@H](c2ccc(O)cc2)N1c1ccc(F)cc1,train
O=[N+]([O-])c1c(Cl)c(Cl)cc(Cl)c1Cl,train
FC(Cl)(Cl)C(F)(Cl)Cl,train
O=[N+]([O-])c1cc(Cl)c(Cl)c(Cl)c1Cl,train
NS(=O)(=O)c1cc2c(cc1Cl)N=C(CSCc1ccccc1)NS2(=O)=O,valid
COc1c(C(C)(C)C)cc([N+](=O)[O-])c(C)c1[N+](=O)[O-],train
C1COCCN1,train
CCCCC(CC)COC(=O)c1ccccc1C(=O)O,train
CCCCNc1ccc(C(=O)OCCOCCOCCOCCOCCOCCOCCOCCOCCOC)cc1,train
COc1ccc(NC(C)=O)cc1,test
BrCc1ccc(Br)cc1,train
O=C(O)c1ccc2ccccc2c1O.OCc1cc(C(O)CNCCCCCCOCCCCc2ccccc2)ccc1O,train
CCCC(C(=O)OCCN(CC)CC)(c1ccccc1)c1ccccc1,train
CN1CCN(C2Cc3ccccc3Sc3ccc(Cl)cc32)CC1,train
CCCN1CCC[C@@H](c2cccc(O)c2)C1,valid
CCC(CC)C(C)CCOC(C)=O,train
CC(C)NCC(O)COc1ccc(COCCOC(C)C)cc1.CC(C)NCC(O)COc1ccc(COCCOC(C)C)cc1,train
C[C@@H]1C(=O)N=C2Nc3cccc(Cl)c3CN21,train
O=CCCCC=O,train
CC(Cc1ccc(O)c(O)c1)C(C)Cc1ccc(O)c(O)c1,test
N#C[Fe-2](C#N)(C#N)(C#N)(C#N)N=O,train
c1ccc2c(c1)ccc1c3ccccc3ccc21,train
O=C(O)c1ccccc1C(c1ccc(O)cc1)c1ccc(O)cc1,train
Cn1sc(Cl)cc1=O,train
CCCCCCCC1CCC(=O)O1,valid
O=C(O)COc1ccc(Cl)cc1,train
CCCCCCCCCCCCn1cc[n+](C)c1.O=S(=O)([O-])C(F)(F)F,train
COc1cccc(C(=O)NN(C(=O)c2cc(C)cc(C)c2)C(C)(C)C)c1C,train
C#CCOC(=O)/C=C(C)/C=C/CC(C)CCCC(C)C,train
COC(C)(C)CCCC(C)C/C=C/C(C)=C/C(=O)OC(C)C,test
Cc1cc(=O)[nH]o1,train
O=C(O)CCCc1c[nH]c2ccccc12,train
O=C(O)[C@H]1[C@@H](O)CC[C@H]2CN3CCc4c([nH]c5ccccc45)[C@@H]3C[C@@H]21,train
COc1ccc2cc([C@H](C)C(=O)[O-])ccc2c1,train
CC(=O)OCC1=C(C(=O)[O-])N2C(=O)[C@@H](NC(=O)CSc3ccncc3)[C@H]2SC1,valid
CC1(C)[C@@H](OC(=O)CCC(=O)[O-])CC[C@@]2(C)[C@H]1CC[C@]1(C)[C@@H]2C(=O)C=C2[C@@H]3C[C@@](C)(C(=O)[O-])CC[C@]3(C)CC[C@]21C,train
CC[C@@]1([C@@H]2O[C@@H]([C@H]3O[C@@](O)(CO)[C@H](C)C[C@@H]3C)C[C@@H]2C)CC[C@H]([C@]2(C)CC[C@]3(C[C@H](O)[C@@H](C)[C@@H]([C@@H](C)[C@@H](OC)[C@H](C)C(=O)[O-])O3)O2)O1,train
CCOc1ccc2ccccc2c1C(=O)N[C@@H]1C(=O)N2[C@@H](C(=O)[O-])C(C)(C)S[C@H]12,train
CCC(NC(C)C)C(O)c1ccc(O)c(O)c1,train
O=S(=O)([O-])c1ccc(N=Nc2c(O)ccc3ccccc23)cc1,test
O=C1OC2(c3ccc(O)cc3Oc3cc(O)ccc32)c2ccccc21,train
CCOP(=O)(OCC)Oc1nc(Cl)c(Cl)cc1Cl,train
CCNc1ccc(C(=C2C=CC(=[N+](CC)CC)C=C2)c2ccc(N(CC)CC)cc2)c2ccccc12,train
CCCCCCCCCCC(CO)CCCCCCCC,train
CN(C)C(=S)[S-],valid
CC(C)(C)c1cccc(C(C)(C)C)c1O,train
Cc1cccc(C)c1NC(=O)c1cc(S(N)(=O)=O)c(Cl)cc1O,train
OC1OC[C@@H](O)[C@H](O)[C@H]1O,train
COc1cc(C(=O)NS(=O)(=O)c2ccccc2C)ccc1Cc1cn(C)c2ccc(NC(=O)OC3CCCC3)cc12,train
Nc1ccn([C@H]2CC[C@@H](CO)O2)c(=O)n1,test
N[C@@H](Cc1cc(I)c(Oc2ccc(O)c(I)c2)c(I)c1)C(=O)O,train
N[C@@H](Cc1cc(I)c(Oc2cc(I)c(O)c(I)c2)c(I)c1)C(=O)O,train
CN(CCO)CC(O)Cn1cnc2c1c(=O)n(C)c(=O)n2C.O=C(O)c1cccnc1,train
CCN(C(C)=O)c1cccc(-c2ccnc3c(C#N)cnn23)c1,train
CC(=O)N[C@@H]1[C@@H](NC(=N)N)C=C(C(=O)O)O[C@H]1[C@H](O)[C@H](O)CO,valid
CC[N+]1(CCOC(=O)C(O)(c2ccccc2)c2ccccc2)CCCCC1,train
c1ccc2c(c1)CCc1ccccc1N2,train
O=C(O)c1cc(Cl)ccc1Cl,train
O=C(O)c1c(Cl)cccc1Cl,train
O=C(O)c1cccc(Cl)c1Cl,test
O=C1C=CC(=O)N1,train
O=Cc1c(Cl)cccc1Cl,train
O=Cc1ccc(Cl)c(Cl)c1,train
CCC(=O)O[C@H]1[C@H](O[C@@H]2[C@@H](C)[C@H](O[C@H]3C[C@@](C)(OC)[C@@H](O)[C@H](C)O3)[C@@H](C)C(=O)O[C@H](CC)[C@@](C)(O)[C@H](O)[C@@H](C)C(=O)[C@H](C)C[C@@]2(C)O)O[C@H](C)C[C@@H]1N(C)C.CCCCCCCCCCCCOS(=O)(=O)O,train
C[C@H]1CC[C@@H]2[C@@H](C)[C@H]3[C@H](C[C@H]4[C@@H]5CC=C6CC(O[C@@H]7O[C@H](CO)[C@H](O)[C@H](O[C@@H]8O[C@H](CO)[C@@H](O)[C@H](O)[C@H]8O)[C@H]7O[C@H]7O[C@@H](C)[C@H](O)[C@@H](O)[C@H]7O)CC[C@]6(C)[C@H]5CC[C@@]43C)N2C1,valid
C=CC(=O)NCOCCCC,train
CCCCCCCCOc1ccc(C(=O)c2ccccc2)c(O)c1,train
CCCCCCCCCC#N,train
O=C1c2ccccc2CN2C(=O)c3ccccc3CN12,train
ClCCCCBr,test
CCCCCCCCCCCBr,train
COc1nccnc1NS(=O)(=O)c1ccc(N)cc1,train
CCCOc1ccc2nc(NC(=O)OC)sc2c1,train
CN1CCCC(CN2c3ccccc3Sc3ccccc32)C1,train
O=C(CCN1CCN(CCO)CC1)N1c2ccccc2Sc2ccc(C(F)(F)F)cc21,valid
CCCCCCCCCBr,train
O=C(Cl)c1ccc(F)c(Cl)c1,train
O=S(=O)(Cl)c1c(Cl)cccc1Cl,train
CCCCCCOC(=O)CCCCC,train
O=C(O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,test
CCCCC1CCCCC(=O)O1,train
CC=C(C)C(=O)OCCC(C)CCC=C(C)C,train
CC/C=C\CCOC(=O)c1ccccc1,train
C=C(C)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
CCCCCCOC(=O)CCC,valid
ClC1=C(Cl)C2(Cl)C(CBr)CC1(Cl)C2(Cl)Cl,train
O=[N+]([O-])c1ccc(O/N=C/c2cc(Br)c(O)c(Br)c2)c([N+](=O)[O-])c1,train
O=S1(=O)OC(c2cc(Br)c(O)c(Br)c2)(c2cc(Br)c(O)c(Br)c2)c2ccccc21,train
CCOP(=S)(OCC)Oc1cc(Cl)c(Br)cc1Cl,train
CN(C)c1ccc(C(=C2C=CC(=[N+](C)C)C=C2)c2ccccc2)cc1.CN(C)c1ccc(C(=C2C=CC(=[N+](C)C)C=C2)c2ccccc2)cc1,test
CCOC(=O)c1cnc2cc(OCC(C)C)c(OCC(C)C)cc2c1O,train
C#CCOS(=O)OC1CCCCC1Oc1ccc(C(C)(C)C)cc1,train
CC(C)(C)NCC(O)c1cc(Cl)c(N)c(C(F)(F)F)c1,train
C[C@]12CC[C@H]3[C@@H](CCC4=C(O)C(=O)CC[C@@]43C)[C@@H]1CCC2=O,train
C[C@@H](NCCCc1cccc(C(F)(F)F)c1)c1cccc2ccccc12,valid
CCCCCc1ccc(C)cc1O,train
CCCOC(=O)c1ccccc1O,train
CO/N=C(\C(=O)N[C@@H]1C(=O)N2C(C(=O)[O-])=C(C[n+]3ccc(N)n3CCO)CS[C@H]12)c1csc(N)n1,train
CCCCCCCCCCCCSC#N,train
CC1(C)[C@@H](C(=O)OCc2coc(Cc3ccccc3)c2)[C@H]1/C=C1\CCSC1=O,test
CCOP(=S)(OCC)Oc1cc(-c2ccccc2)on1,train
CCCCCCCCCCCCN1CCCCCC1=O,train
CN(C)C(=O)Nc1cccc(OC(=O)NC(C)(C)C)c1,train
Cl[Sn](Cl)(Cl)Cl,train
NCc1ccc(S(N)(=O)=O)cc1,valid
CC1CC(CC2CCC(N)C(C)C2)CCC1N,train
CC(O)(P(=O)([O-])O)P(=O)([O-])O,train
O=[Si]=O,train
CO[Si](C)(OC)c1ccccc1,train
Cc1ccc(S(=O)(=O)[N-]Cl)cc1,test
[O-]c1ccccc1-c1ccccc1,train
O=[N+]([O-])c1cccc2cccc([N+](=O)[O-])c12,train
O=[N+]([O-])c1cc([N+](=O)[O-])c2ccc(S(=O)(=O)O)cc2c1O,train
O=[N+]([O-])c1cc([N+](=O)[O-])c2ccccc2c1,train
O=[N+]([O-])c1cccc2c([N+](=O)[O-])cccc12,valid
Nc1ccc(Cc2ccc(N)c(Cl)c2)cc1Cl,train
CCCCOC(=O)[C@H](C)O,train
CCCOC(=O)CC,train
CC(COC(C)(C)C)OCCCO,train
CC1=C(CC(=O)O)c2cc(F)ccc2/C1=C\c1ccc(S(C)(=O)=O)cc1,test
OC[C@H]1O[C@H](O[C@]2(CCl)O[C@H](CCl)[C@@H](O)[C@@H]2O)[C@H](O)[C@@H](O)[C@H]1Cl,train
CC[C@H](C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](CC=C(C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H]([NH2+]C)[C@H](C)O4)[C@H](C)O3)[C@@H](C)C=CC=C3CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.O=C([O-])c1ccccc1,train
CN[C@H]1CC[C@@H](c2ccc(Cl)c(Cl)c2)c2ccccc21,train
O=C(Nc1ccc(/C=C/c2ccc(NC(=O)c3cc(S(=O)(=O)O)c4cccnc4c3O)cc2S(=O)(=O)O)c(S(=O)(=O)O)c1)c1cc(S(=O)(=O)O)c2cccnc2c1O,train
CCOP(=S)(OCC)SCSC(C)(C)C,valid
CN(CC(=O)N1CCOCC1)Cc1c(Cl)cccc1NC(=O)c1ccccc1,train
CN1CCN(C2=Nc3ccccc3Cc3ccccc32)CC1,train
O=C(Cn1c(=O)sc2ccc(Cl)cc21)N1CCN(CCO)CC1,train
COc1cc(C[C@@H]2NCCc3cc(O)c(O)cc32)cc(OC)c1OC,train
O=C1CCC(N2C(=O)c3ccccc3C2=O)C(=O)N1,test
CCCCOCCOCCO,train
O=C(O)/C=C/C(=O)O,train
O=C(O)/C=C\C(=O)O,train
CCC(C)=O,train
CCCC(=O)O,valid
CCCC=O,train
CC(C)=CC1C(C(=O)OCc2coc(Cc3ccccc3)c2)C1(C)C,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O,train
CCC(=O)OC,train
c1cc(-c2ccncc2)ccn1,test
C[Si]1(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O1,train
CN=C=S,train
CC(C)=CCO,train
COP(=O)(OC)O/C(C)=C/C(=O)N(C)C,train
ClCCOCOCCCl,valid
CC(C)=CCl,train
O=C1c2c(O)ccc(Nc3ccc(CCO)cc3)c2C(=O)c2c([N+](=O)[O-])ccc(O)c21,train
O=C1C(O)=C([C@H]2CC[C@H](c3ccc(Cl)cc3)CC2)C(=O)c2ccccc21,train
Oc1ccc(-c2ccccc2)c(Cl)c1,train
O=C1CCCCCCCCCCC(=O)OCCO1,test
O=[PH]1Oc2ccccc2-c2ccccc21,train
O=[N+]([O-])C1(Br)COCOC1,train
CC(NC(C)(C)C)C(=O)c1cccc(Cl)c1,train
CC#CCC(C)[C@H](O)/C=C/C1[C@H]2C/C(=C/CCCC(=O)O)C[C@H]2C[C@H]1O,train
CC(C)Cn1cnc2c(N)nc3ccccc3c21,valid
CN(C)CCCN(C)CCCN(C)C,train
NCCCOCCOCCOCCCN,train
N#CSc1ccc(N)c([N+](=O)[O-])c1,train
CC(=O)N[C@H]1[C@@H](O[C@@H]2C(C(=O)[O-])C[C@@H](O[C@@H]3[C@@H](CO)O[C@H](OC4C(C(=O)[O-])O[C@@H](OC5[C@H]6CO[C@H](O6)[C@H](NS(=O)(=O)[O-])[C@H]5O)[C@H](O)[C@H]4O)[C@H](NC(C)=O)[C@H]3C)[C@H](O)[C@H]2O)O[C@H](COS(=O)(=O)O)[C@@H](O[C@@H]2OC(C(=O)[O-])=C[C@H](O)[C@H]2O)[C@@H]1O,train
C=C[C@H]1CN2CC[C@H]1C[C@H]2[C@H](OC(=O)OCC)c1ccnc2ccc(OC)cc12,test
O=C1c2c(O)cccc2[C@H]([C@@H]2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)c2cc(CO)cc(O)c21,train
CC(N)Cc1ccc(O)cc1,train
O=C1c2ccccc2C(=O)c2c1ccc(O)c2O,train
COc1cc(OC)cc(OC)c1,train
CCC(=O)C(CC(C)N(C)C)(c1ccccc1)c1ccccc1,valid
Cc1c(N(C)C)c(=O)n(-c2ccccc2)n1C,train
CN(C)CCO,train
CN(C)c1ccccc1,train
C[As](C)(=O)O,train
CC1(C)C(=O)NC(=O)NC1=O,test
O=C(c1ccc(O)cc1O)c1ccc(O)cc1O,train
C=C(C)C(=O)OCCOC1CC2CC1C1C=CCC21,train
OCC(c1ccc(Cl)cc1)c1ccc(Cl)cc1,train
CCN(CC)CCOC(=O)c1ccc(N)cc1Cl,train
CCN(CC)C(=O)c1ccc(O)c(OC)c1,valid
C1CNCCN1.C[C@]12CC[C@@H]3c4ccc(OS(=O)(=O)O)cc4CC[C@H]3[C@@H]1CCC2=O,train
CCCCC(=O)O[C@H]1CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@]12C,train
CC(=O)OC(C)OC(=O)C(C)c1ccc(-c2ccccc2)c(F)c1,train
CCCCCCC(=O)O[C@H]1CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@]12C,train
CCC(=O)Oc1ccc2c(c1)CC[C@@H]1[C@@H]2CC[C@]2(C)[C@@H](OC(=O)CC)CC[C@@H]12,test
CCN(CC)CCNC(=O)c1cc(S(C)(=O)=O)ccc1OC,train
FC(F)(F)c1ccccc1-n1ccnc1,train
N#CC(C#N)=Cc1cc(O)c(O)c(O)c1,train
N#CC(C#N)=Cc1ccc(O)c(O)c1,train
O=C(O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,valid
O=C(O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
O=C(O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
O=C(O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
O=S(=O)(O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
CC(C)CC(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CC(C)C)[C@@H](O)CC(=O)N[C@@H](C)C(=O)N[C@@H](CC(C)C)[C@@H](O)CC(=O)O)C(C)C)C(C)C,test
CN(C)CCc1ccc(O)cc1,train
CC1(C)S[C@@H]2[C@H](N)C(=O)N2[C@H]1C(=O)O,train
c1cc2c(c(N3CCNCC3)c1)OCCO2,train
CC1(CN2CCC(n3c(=O)[nH]c4ccccc43)CC2)OCc2ccccc2-n2cccc21,train
CN1C(=O)[C@@H](NC(=O)c2cc3ccccc3[nH]2)N=C(c2ccccc2)c2ccccc21,valid
O=c1[nH]c2cc(C(F)(F)F)c(N3CCOCC3)cc2n(CP(=O)(O)O)c1=O,train
COC1=CC=C2[C@H]3Cc4ccc(OC)c5c4[C@@]2(CCN3C)[C@H]1O5,train
FC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,train
Cc1ccc(N(SC(F)(Cl)Cl)S(=O)(=O)N(C)C)cc1,train
CC(=O)Nc1ccc(S(=O)(=O)Cl)cc1,test
Cc1ccc([N+](=O)[O-])cc1Cl,train
CC(C)[C@@]1(NC(=O)[C@@H]2C[C@@H]3c4cccc5[nH]cc(c45)C[C@H]3N(C)C2)O[C@@]2(O)[C@@H]3CCCN3C(=O)[C@H](Cc3ccccc3)N2C1=O,train
C[C@@H](CO)NC(=O)[C@@H]1C=C2c3cccc4[nH]cc(c34)C[C@H]2N(C)C1,train
CCN(CC)C(=O)N[C@@H]1C=C2c3cccc4[nH]cc(c34)C[C@@H]2N(C)C1,train
C=CCN1CCc2nc(N)sc2CC1,valid
CN(C(=O)Cc1ccccc1)[C@H]1CC[C@@]2(CCCO2)C[C@@H]1N1CCCC1,train
CN1C[C@@H](NS(=O)(=O)N(C)C)C[C@@H]2c3cccc4c3c(cn4C)C[C@H]21,train
C=CCN1CC[C@]23c4c5ccc(O)c4O[C@H]2C(=O)CC[C@@]3(O)[C@H]1C5.CI,train
CC(C)(C)NSc1nc2ccccc2s1,train
C=CCC1=C(C)C(OC(=O)C2C(C=C(C)C)C2(C)C)CC1=O,test
CC1=CC(=O)CCC1,train
CC(C)c1ccccc1C(C)C,train
O=C=NC1CCC(CC2CCC(N=C=O)CC2)CC1,train
CCCCOCC(C)O,train
C=C(C)C(=O)OCC[N+](C)(C)C,valid
c1ccc2sc(SN(C3CCCCC3)C3CCCCC3)nc2c1,train
CC(C)C(=O)OCC(C)(C)C(O)C(C)C,train
Cc1cccc(Cl)c1,train
C1=CCCC=CCCC=CCC1,train
C=C(C)C#N,test
COP(N)(=O)SC,train
CCCCSP(=O)(SCCCC)SCCCC,train
COCC(=O)N(c1c(C)cccc1C)C(C)C(=O)OC,train
C[N+]1(C)CCCCC1,train
Cn1cc(C2=C(c3cn(C4CCN(Cc5ccccn5)CC4)c4ccccc34)C(=O)NC2=O)c2ccccc21,valid
O=C(NOC[C@H](O)CO)c1ccc(F)c(F)c1Nc1ccc(I)cc1F,train
O=C1CN=C(c2ccccc2)c2cc(Cl)ccc2N1CC1CC1,train
Nc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl,train
CCOC(=O)CCC(C)=O,train
C[C@]12C[C@H](O)[C@H]3[C@@H](CCC4=CC(=O)C=C[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)CO,test
C[C@]12CC(=O)[C@H]3[C@@H](CCC4=CC(=O)C=C[C@@]43C)[C@@H]1CC[C@]2(O)C(=O)CO,train
CNC(=O)CSP(=O)(OC)OC,train
CCCN(CCC)S(=O)(=O)c1ccc(C(=O)O)cc1,train
C=C(C)[C@@H]1CCC2=CCC[C@@H](C)[C@]2(C)C1,train
C=CC(C)=CCC=C(C)C,valid
C=C(C)[C@@H]1CCC2=CC(=O)C[C@@H](C)[C@]2(C)C1,train
CCOC(=O)CC(C)C,train
CCCCCCCCCCCCN(CCCCCCCCCCCC)CCCCCCCCCCCC,train
CC1=CCCC(C)(C)C12CCC(C)O2,train
c1cc(C(c2ccc(OCC3CO3)cc2)c2ccc(OCC3CO3)cc2)ccc1OCC1CO1,test
CC1(CO)CN(c2ccccc2)NC1=O,train
C=C(C)C(=O)OCCCCCCCCCCOC(=O)C(=C)C,train
CCC(=O)OCc1ccccc1,train
Cc1cccc(Nc2ccccc2)c1,train
O=S(=O)([O-])CC(O)CCl,valid
C=CCOCC(CC)(CO)COCC=C,train
NS(=O)(=O)c1ccccc1Cl,train
CCC(C)(CCC(C)C)C(=O)O[Sn](C)(C)OC(=O)C(C)(CC)CCC(C)C,train
CC(C)(C)C1CCCCC1O,train
CC(C)CP(=S)([S-])CC(C)C,test
COc1ccc(CC2c3cc(OC)c(OC)cc3CC[N+]2(C)CCC(=O)OCCCCCOC(=O)CC[N+]2(C)CCc3cc(OC)c(OC)cc3C2Cc2ccc(OC)c(OC)c2)cc1OC.O=S(=O)([O-])c1ccccc1.O=S(=O)([O-])c1ccccc1,train
CN1CCC(=C2c3ccccc3CCc3cccnc32)CC1,train
CCOC(=O)[C@H](CCc1ccccc1)N[C@@H](C)C(=O)N1CCC[C@H]1C(=O)O,train
CN(C)C(=O)Nc1ccc(Cl)cc1,train
Cc1ccc(S(=O)(=O)N=C=O)cc1,valid
NC(=O)c1ccccc1O,train
O=C1C[C@@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@H]3C[C@H]46.O=C1C[C@@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@H]3C[C@H]46,train
O=C1NCC2(CCN(CCc3ccccc3)CC2)O1,train
CCOC(=O)Nc1ccc(NCc2ccc(F)cc2)nc1N,train
CC(C)[N+]1(C)C2CCC1CC(OC(=O)C(CO)c1ccccc1)C2,test
N=C(N)NCc1cccc(I)c1.N=C(N)NCc1cccc(I)c1,train
CC(C)NC(C)Cc1ccc(I)cc1,train
Clc1cc(C(Cl)(Cl)Cl)cnc1Cl,train
Cc1cn[nH]c1,train
C#CCN(C)CCCOc1ccc(Cl)cc1Cl,valid
CCCCCCCCCCCC#N,train
N#Cc1c(Cl)c(Cl)c(Cl)c(C#N)c1Cl,train
Cc1ccc(C)c(O)c1C,train
C=C(C)C(=O)OCCOCC,train
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1O,test
COP(=S)(OC)Oc1nc(Cl)c(Cl)cc1Cl,train
C#CCOC(=O)[C@@H](C)Oc1ccc(Oc2ncc(Cl)cc2F)cc1,train
CC1(C)CON(Cc2ccccc2Cl)C1=O,train
CCSC(=O)N(CC)C1CCCCC1,train
COC(=O)C(C)Oc1ccc(Oc2ccc(Cl)cc2Cl)cc1,valid
CCNC(=O)NC(=O)/C(C#N)=N/OC,train
Cc1cc(C2CC2)nc(Nc2ccccc2)n1,train
COc1cc2nc(N(C)CCCNC(=O)C3CCCO3)nc(N)c2cc1OC,train
CN(C)C(=O)Oc1cc(OC(=O)N(C)C)cc(C(O)CNC(C)(C)C)c1,train
CCC(=O)O[C@]1(C(=O)SCF)[C@H](C)C[C@H]2C3C[C@H](F)C4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@@]21C,test
C[C@@]12CCC[C@H]1[C@@H]1CC[C@H]3C[C@@H](O)CC[C@]3(C)[C@H]1CC2,train
CC(=O)SC[C@@H](Cc1ccccc1)C(=O)NCC(=O)OCc1ccccc1,train
CCCN1CCC[C@@H]2Cc3n[nH]cc3C[C@H]21,train
CCC[C@@]1(CCc2ccccc2)CC(O)=C([C@H](CC)c2cccc(NS(=O)(=O)c3ccc(C(F)(F)F)cn3)c2)C(=O)O1,train
CO[C@H]1C[C@@H]2CC[C@@H](C)[C@@](O)(O2)C(=O)C(=O)N2CCCC[C@H]2C(=O)O[C@H]([C@H](C)C[C@@H]2CC[C@@H](O)[C@H](OC)C2)CC(=O)[C@H](C)C=C(C)[C@@H](O)[C@@H](OC)C(=O)[C@H](C)C[C@H](C)C=CC=CC=C1C,valid
CCC(C)(C)C(=O)O[C@H]1C[C@@H](C)C=C2C=C[C@H](C)[C@H](CC[C@@H]3C[C@@H](O)CC(=O)O3)[C@H]21,train
CC(C)=CCOc1ccc(/C=C/C(=O)c2ccc(OCC=C(C)C)cc2OCC(=O)O)cc1,train
O=C1NC(=O)[C@@]2(CCOc3ccc(F)cc32)N1,train
C/C=C(/CC[C@@H](C)[C@H]1CC[C@H]2C3=CC[C@H]4[C@H](C)[C@@H](O)CC[C@]4(C)[C@H]3CC[C@@]21C)C(C)C,train
OC[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,test
CCCCCCCCCCCCOS(=O)(=O)O.OCCN(CCO)CCO,train
[O-]c1ccccc1,train
c1ccc2c(c1)c1cccc3ccc4cccc2c4c31,train
C=CCNCC=C,train
CCCCC/C=C/C=C/C=O,valid
C1CCC2CCCCC2C1,train
CCCCCCCCCC,train
C=C(C)C(=O)OCCCCCCCCCC,train
C=CC(=O)NC(C)(C)CC(C)=O,train
CC(=O)CC(C)(C)O,test
CN(C)C(=O)C1(N2CCCCC2)CCN(CCC2(c3ccc(Cl)c(Cl)c3)CN(C(=O)c3ccccc3)CCO2)CC1,train
O=C([O-])C(Cl)Cl,train
O=C([O-])Cc1ccccc1Nc1c(Cl)cccc1Cl,train
NNC(=S)NN,train
NC(=O)Nc1ccc(Cl)cc1,valid
[O-][n+]1ccc(Cl)cc1,train
CCCCC/C=C\CCC=O,train
CN1C(=O)CC(=O)N(c2ccccc2)c2cc(Cl)ccc21,train
CCc1occc(=O)c1O,train
CC1=C(O)C(=O)C(C)O1,test
COc1ccc(CCO)cc1,train
CCCC(C)C(O)CC,train
Oc1ccc(/C=C/c2cc(O)cc(O)c2)cc1,train
O=C1C[N+]([O-])=C(c2ccccc2)c2cc(Cl)ccc2N1,train
O=C(/C=C/c1cccc(F)c1)NC1CC1,valid
O=C(O)Cc1csc(-c2ccc(Cl)cc2)n1,train
O=C(Nc1ccc(Br)cc1)c1cc(Br)c(Br)s1,train
O=C(O)Cc1cn(-c2ccccc2)nc1-c1ccc(Cl)cc1,train
Cc1cccc(C)c1NC(=O)CN1CCCC1,train
NS(=O)(=O)c1ccc(Cl)c(S(N)(=O)=O)c1,test
CCCCCCC(=O)O[C@H]1CC[C@H]2[C@@H]3CC[C@H]4CC(=O)C=C(C)[C@]4(C)[C@H]3CC[C@]12C,train
C=C/C(C)=C/C/C=C(\C)CCC=C(C)C,train
CCC(=O)OCCOc1ccccc1,train
CC(=O)CCC1=C(C)CCCC1(C)C,train
COc1cc(C=O)ccc1OC(=O)C(C)C,valid
CC[C@@]1(O)C(=O)OCc2c1cc1n(c2=O)Cc2cc3c([N+](=O)[O-])cccc3nc2-1,train
CCCCCCCCCCCCCCOS(=O)(=O)[O-],train
S=c1[nH]c2ccccc2[nH]1,train
CC(=O)N[C@@H]1C(O[C@H]2O[C@H](C(=O)O)C(O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](CO)O[C@H]1O[C@@H]1C(C(=O)O)O[C@@H](O[C@H]2[C@H](O)[C@@H](CO)OC(O)[C@@H]2NC(C)=O)[C@H](O)[C@H]1O,train
S=C1NC=NC2N=CNC12,test
CCCCCCOC(=O)CCCCC(=O)OCCCCCC,train
CCCCCCCCCC(=O)OC,train
OC/C=C/CO,train
Cc1c(Cl)cccc1Nc1ncccc1C(=O)O,train
OCc1c[nH]c2ccccc12,valid
Cc1cccc(Nc2cc(Cl)nc(SCC(=O)O)n2)c1C,train
CNCC(O)c1ccc(O)cc1,train
O=CCCl,train
O=C(CCl)c1ccccc1,train
COP(=S)(OC)Oc1ccc([N+](=O)[O-])cc1Cl,test
COC(=O)c1ccccc1C(=O)O,train
CC(=O)Nc1ccc(C(=O)CCl)cc1,train
CCCCOC(=O)c1ccccc1C(=O)O,train
Nc1ccc(Cl)cc1,train
c1ccc(CCCc2ccccn2)cc1,valid
CCOC(=O)c1cn2nc(OP(=S)(OCC)OCC)cc2nc1C,train
CCC(C)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1OC(=O)C=C(C)C,train
CCC(C)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1OC(=O)OC(C)C,train
CCN(CC)c1c([N+](=O)[O-])cc(C(F)(F)F)c(N)c1[N+](=O)[O-],train
CC1(C)[C@@H]2CC[C@@]1(C)[C@H](OC(=O)CSC#N)C2,test
CC(C)N(C(=O)SCC(Cl)=CCl)C(C)C,train
CC(=O)OCC1=C(C(=O)O)N2C(=O)[C@@H](N)[C@H]2SC1,train
COC(=O)Nc1nc2ccccc2[nH]1,train
O=C1CCCCC1Cl,train
CC1(C)[C@@H]2CC[C@@]1(C)C(=O)C2,valid
NC(N)=O.OO,train
CCCCCCCC(=O)Cl,train
CCCCCOC(C)=O,train
CCCCCCCCCCCCCC,train
CCCCCCCCCCCCC,test
OCCCCCCO,train
C=CCCCCCCCCCCCCCC,train
CCCCCCCCCCCCCCC,train
CSc1ccc(Cl)cc1,train
Clc1ccc2ccccc2c1,valid
Cc1ccccc1Cl,train
CS(=O)(=O)c1ccc(Cl)cc1,train
Oc1ccc2ccc3cccc4ccc1c2c34,train
CC(C)COC(=O)c1ccc(O)cc1,train
NC(=O)CI,test
IC(I)I,train
COc1ccc2c(c1)c(CC(=O)O)c(C)n2C(=O)c1ccc(Cl)cc1,train
C=CCOCC(CO)(COCC=C)COCC=C,train
CCc1ccc(O)c(C(C)(C)C)c1,train
Cc1c(C)c([N+](=O)[O-])c(C(C)(C)C)c([N+](=O)[O-])c1C,valid
C#CCN(C)[C@H](C)Cc1ccccc1,train
O=C1CN(N=Cc2ccc(-c3ccc([N+](=O)[O-])cc3)o2)C(=O)[N-]1,train
CCN(CCC#N)c1ccccc1,train
COC(=O)CCCC(=O)O,train
OC[C@H](Cc1cccc(O)c1)[C@H](CO)Cc1cccc(O)c1,test
COc1cc(Cl)ccc1Oc1ccc(Cl)cc1Cl,train
Cc1ccc(O)c(C(=O)c2ccccc2)c1,train
O=C(c1ccccc1)c1ccccc1O,train
CN1CCc2cc3c(cc2C1O)OCO3,train
O=C(Oc1ccccc1)c1ccc(O)cc1,valid
CCCC(c1cc(C(C)(C)C)c(O)cc1C)c1cc(C(C)(C)C)c(O)cc1C,train
C=C1CC[C@H]2[C@@](C)(CC[C@@H](O)[C@@]2(C)CO)[C@@H]1C/C=C1/C(=O)OC[C@H]1O,train
CC1(C)O[C@H]2CC(=O)OC[C@@]23[C@H]1CC(=O)[C@]1(C)[C@@H]3CC[C@@]2(C)[C@H](c3ccoc3)OC(=O)[C@H]3O[C@]321,train
OC[C@H]1O[C@@H](O[C@@H]2[C@@H](CO)O[C@@H](O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,train
CN(C)CCN(Cc1ccccc1)c1ccccn1.O=C(O)CC(O)(CC(=O)O)C(=O)O,test
C=CCC1(C(C)CCC)C(=O)N=C([S-])NC1=O,train
Clc1ccccc1,train
CCOC(=O)C(O)(c1ccc(Cl)cc1)c1ccc(Cl)cc1,train
CCCCCCCCCCC[C@@H](C[C@@H]1OC(=O)[C@H]1CCCCCC)OC(=O)C(CC(C)C)NC=O,train
CCC1CO1,valid
FC(F)OC(F)(F)C(F)Cl,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=C(CCC(=O)C4)[C@H]3CC[C@@]21C,train
O=S1OCC2C(CO1)C1(Cl)C(Cl)=C(Cl)C2(Cl)C1(Cl)Cl,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@@H]4C[C@H]3[C@H]3O[C@@H]43)[C@@]1(Cl)C2(Cl)Cl,train
ClCC1CO1,test
O=C(O[C@@H]1Cc2c(O)cc(O)cc2O[C@@H]1c1cc(O)c(O)c(O)c1)c1cc(O)c(O)c(O)c1,train
CC(C)NCC1CCc2cc(CO)c([N+](=O)[O-])cc2N1,train
CCCn1cnc2c1c(=O)n(CCCCC(C)=O)c(=O)n2C,train
c1cnc(N2CCN(Cc3ccc4c(c3)OCO4)CC2)nc1,train
Cc1ccc(=O)n(-c2ccccc2)c1,valid
Cc1nc2ccccn2c(=O)c1CCN1CCC(C(=O)c2ccc(F)cc2)CC1,train
O=c1[nH]c2ccccc2n1CCCN1CCN(C(c2ccccc2)c2ccccc2)CC1,train
NC(=O)CN1CC(O)CC1=O,train
CCC(=O)c1ccc2c(c1)N(CCCN(C)C)c1ccccc1S2,train
CCCn1c(=O)[nH]c(=O)c2[nH]cnc21,test
CCCNCC(O)COc1ccccc1C(=O)CCc1ccccc1,train
CC(=O)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]12C,train
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@@H]2O,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@H]2O,train
COc1ccc2c(c1OC)CN1CCc3cc4c(cc3C1C2)OCO4,valid
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@@]21C,train
CN(C)[C@@H]1C(=O)C(C(N)=O)=C(O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O,train
NC[C@@H]1O[C@H](O[C@H]2[C@@H](O)[C@H](O[C@@H]3[C@@H](O)[C@H](N)C[C@H](N)[C@H]3O[C@H]3O[C@H](CN)[C@@H](O)[C@H](O)[C@H]3N)O[C@@H]2CO)[C@H](N)[C@@H](O)[C@@H]1O,test
COc1ccc([C@@H]2Sc3ccccc3N(CCN(C)C)C(=O)[C@@H]2OC(C)=O)cc1,train
CN[C@H]1CC[C@@H](c2ccc(Cl)c(Cl)c2)c2ccccc21,train
CCC(C)(C)C(=O)O[C@H]1C[C@@H](C)C=C2C=C[C@H](C)[C@H](CC[C@@H]3C[C@@H](O)CC(=O)O3)[C@H]21,train
C=C1C(=O)N[C@H](C)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](C(=O)O)[C@H](C)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](/C=C/C(C)=C/[C@H](C)[C@H](Cc2ccccc2)OC)[C@H](C)C(=O)N[C@@H](C(=O)O)CCC(=O)N1C,train
C=C1C(=O)N[C@H](C)C(=O)N[C@@H](Cc2ccc(O)cc2)C(=O)N[C@@H](C(=O)O)[C@H](C)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](/C=C/C(C)=C/[C@H](C)[C@H](Cc2ccccc2)OC)[C@H](C)C(=O)N[C@@H](C(=O)O)CCC(=O)N1C,valid
C=C1C(=O)N[C@H](C)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](C(=O)O)[C@H](C)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](/C=C/C(C)=C/[C@H](C)[C@H](Cc2ccccc2)OC)[C@H](C)C(=O)N[C@@H](C(=O)O)CCC(=O)N1C,train
C[C@@H]1CO[C@](Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,train
C[C@H]1CO[C@@](Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,train
C[C@@H]1CO[C@@](Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,train
C[C@H]1CO[C@](Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,test
CN1CCC[C@H]1c1cccnc1,train
O=C(N[C@H](CO)[C@H](O)c1ccc([N+](=O)[O-])cc1)C(Cl)Cl,train
O=C1C[C@@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@H]3C[C@H]46,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)ccc(Cl)c4[C@@](C)(O)[C@H]3C[C@@H]12,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H]1Cl,valid
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H]4C[C@@H]([C@H]5O[C@@H]45)[C@@H]3[C@@]1(Cl)C2(Cl)Cl,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,train
C[C@@H]1C[C@@H]([C@H](O)CC2CC(=O)NC(=O)C2)C(=O)[C@@H](C)C1,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@H]4C[C@H]([C@@H]5O[C@H]45)[C@@H]3[C@@]1(Cl)C2(Cl)Cl,test
C[C@H](O)C(=O)O,train
C=C(C)[C@H]1Cc2c(ccc3c2O[C@@H]2COc4cc(OC)c(OC)cc4[C@@H]2C3=O)O1,train
O[C@H]1[C@H](O)[C@H](O)OC[C@H]1O,train
CCOC(=O)[C@H](O)[C@@H](O)C(=O)OCC,train
CC1(C)[C@@H]2CC[C@@]1(C)[C@H](OC(=O)CSC#N)C2,valid
C=C(C)[C@@H]1[C@H]2OC(=O)[C@@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C.CC(C)(O)C1C2OC(=O)C1C1(O)CC3OC34C(=O)OC2C14C,train
C=C[C@@]1(C)C=C2CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@@H]4C=C[C@H]3C4)[C@@]1(Cl)C2(Cl)Cl,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H](Cl)[C@H]1Cl,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@@H](Cl)[C@@H]1Cl,test
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@@H](Cl)[C@H]1Cl,train
COc1cc2c(cc1OC)[C@@]13CCN4CC5=CCO[C@H]6CC(=O)N2[C@H]1[C@H]6[C@H]5C[C@H]43,train
CC1(C)[C@H]2CC[C@]1(C)[C@H](O)C2,train
CC1(C)[C@H]2CC[C@]1(C)C(=O)C2,train
CC1(C)[C@@H]2CC[C@@]1(C)C(=O)C2,valid
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@H]4C=C[C@H]3C4)[C@@]1(Cl)C2(Cl)Cl,train
C=C[C@]1(C)C=C2CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1,train
CN1CCc2cc3c(cc2[C@H]1[C@@H]1OC(=O)c2c1ccc1c2OCO1)OCO3,train
O[C@@H]1C[C@H]2CC[C@@H]1C2,train
CC(C)C1=CC2=CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1,test
CC1=C2C(=CO[C@@H](C)[C@@H]2C)C(O)=C(C(=O)O)C1=O,train
C=C[C@@](C)(O)CC[C@H]1C(=C)CC[C@H]2C(C)(C)CCC[C@]12C,train
C=C[C@H]1C[N@@]2CC[C@H]1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,train
Cl[C@@H]1CCCC[C@H]1Cl,train
CNC(=O)ON=C1[C@@H](Cl)[C@@H]2C[C@H](C#N)[C@H]1C2,valid
O=S1OC[C@@H]2[C@H](CO1)[C@]1(Cl)C(Cl)=C(Cl)[C@@]2(Cl)C1(Cl)Cl,train
COc1cc2c(c3oc(=O)c4c(c13)CCC4=O)[C@@H]1C=CO[C@@H]1O2,train
NC[C@@H]1O[C@H](O[C@H]2[C@@H](O)[C@H](O[C@@H]3[C@@H](O)[C@H](N)C[C@H](N)[C@H]3O[C@H]3O[C@H](CN)[C@@H](O)[C@H](O)[C@H]3N)O[C@@H]2CO)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@H]4O[C@H](CN)[C@@H](O)[C@H](O)[C@H]4N)[C@H]3O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O,train
C=C[C@@]1(C)C=C2CC[C@H]3[C@](C)(CO)CCC[C@]3(C)[C@H]2CC1,train
CN[C@@H]1[C@H](O)[C@H](NC)[C@H]2O[C@@]3(O)C(=O)C[C@@H](C)O[C@H]3O[C@@H]2[C@H]1O,test
CC(C)c1ccc2c(c1)CC[C@H]1[C@](C)(C(=O)O)CCC[C@]21C,train
C[C@@H]1CCCC[C@@H]1C,train
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1O,train
O[C@@H]1CCCC[C@H]1c1ccccc1,train
O=C1[C@H]2CC=CC[C@H]2C(=O)N1SC(Cl)(Cl)C(Cl)Cl,valid
CC(C)c1ccc2c(c1)CC[C@H]1[C@](C)(CO)CCC[C@]21C,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl,train
C=C[C@@]1(C)CC[C@H]2C(=CC[C@@H]3[C@]2(C)CCC[C@@]3(C)C(=O)O)C1,train
C=C(C)[C@H]1CC=C(C)CC1,test
CC1=CC[C@@H]2C[C@H]1C2(C)C,train
CC1(C(Cl)Cl)[C@H]2[C@@H](Cl)[C@H](Cl)[C@]1(C(Cl)Cl)[C@@H](Cl)[C@@H]2Cl.ClCC1(C(Cl)Cl)[C@@H]2C[C@H](Cl)[C@@]1(C(Cl)Cl)CC2(Cl)Cl.ClCC1(C(Cl)Cl)[C@@H]2[C@@H](Cl)[C@H](Cl)[C@@]1(C(Cl)Cl)C[C@@H]2Cl.ClCC1(C(Cl)Cl)[C@H]2CC(Cl)(Cl)[C@]1(C(Cl)Cl)CC2(Cl)Cl.ClCC1(CCl)[C@H]2[C@@H](Cl)[C@H](Cl)[C@]1(C(Cl)Cl)[C@@H](Cl)[C@@H]2Cl.ClC[C@@]1(C(Cl)Cl)C2[C@@H](Cl)[C@H](Cl)C1(C(Cl)Cl)[C@@H](Cl)[C@@H]2Cl,train
C/C=C\CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](/C=C(\C)C(=O)OC)C2(C)C)CC1=O.C/C=C\CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O.C=C/C=C\CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](/C=C(\C)C(=O)OC)C2(C)C)CC1=O.C=C/C=C\CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,train
C[C@H]1CC[C@H]2C[C@@H]1C2(C)C,train
CC1(C)[C@@H]2CC[C@@]1(C)C(=O)[C@H]2Br,valid
C/C=C(\C)C(=O)O[C@H]1C[C@@H](OC(C)=O)[C@@]2(C(=O)OC)CO[C@H]3[C@@H](O)[C@@](C)([C@]45O[C@@]4(C)[C@H]4C[C@@H]5O[C@@H]5OC=C[C@@]54O)[C@H]4[C@]1(CO[C@]4(O)C(=O)OC)[C@@H]32,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)C=C[C@@H]3[C@@]1(Cl)C2(Cl)Cl.ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl.ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl.ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@H](Cl)[C@@H](Cl)[C@@H]3[C@@]1(Cl)C2(Cl)Cl,train
Cl[C@@H]1C2(Cl)[C@H]3C4C=CC3[C@H]3[C@@H]4[C@]1(Cl)C(Cl)(Cl)[C@]32Cl,train
CNC(=O)O/N=C1\[C@@H](Cl)[C@@H]2C[C@H](C#N)[C@H]1C2,train
COC(=O)C1=CC[C@H]([C@H](C)CC(=O)C=C(C)C)CC1,test
COC(=O)C1=CC[C@H]([C@H](C)CC(=O)CC(C)C)CC1,train
C=C1CC[C@H]2C[C@@H]1C2(C)C,train
CN(C)C(=O)N[C@@H]1C[C@@H]2C[C@H]1[C@@H]1CCC[C@H]21,train
CC1(C)[C@H](C=C2CCCC2)[C@H]1C(=O)OCc1coc(Cc2ccccc2)c1,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@H](Cl)[C@]4(Cl)O[C@H]34)[C@@]1(Cl)C2(Cl)Cl,valid
CC(C)=C[C@@H]1[C@@H](C(=O)OCc2coc(Cc3ccccc3)c2)C1(C)C,train
O=S1OC[C@@H]2[C@H](CO1)[C@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,train
CCOC(=O)[C@H](C)N(C(=O)c1ccccc1)c1ccc(Cl)c(Cl)c1,train
C[C@H](NC(=O)[C@@H](C)NC(=O)[C@@H](N)CCP(C)(=O)O)C(=O)O,train
CC[C@H]1O[C@]2(CC[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)C[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,test
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1C=C(Br)Br,train
CC1(C)[C@H](C(=O)OCc2cccc(Oc3ccccc3)c2)[C@@H]1C=C(Cl)Cl,train
CC1(C)[C@H](C=C(Cl)Cl)[C@H]1C(=O)OCc1cccc(Oc2ccccc2)c1,train
CC(C)[C@H](C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,train
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1C(Br)C(Br)(Br)Br,valid
ClC[C@@]1(C(Cl)Cl)C2[C@@H](Cl)[C@H](Cl)C1(C(Cl)Cl)[C@@H](Cl)[C@@H]2Cl,train
CC1(C)[C@H](C=C(Cl)Cl)[C@@H]1C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,train
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@@H](C)O[C@@H](C)C1,train
CC(C)[C@H](C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1)c1ccc(OC(F)F)cc1,train
COCC(=O)N(c1c(C)cccc1C)[C@H](C)C(=O)OC,test
CCOC(=O)[C@@H](C)Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1,train
C#CCN1CC(=O)N(COC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)C1=O,train
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1/C=C(\Cl)C(F)(F)F,train
CC(C)(C)[C@@H](O)[C@@H](Cc1ccc(Cl)cc1)n1cncn1,train
C[C@H]1[C@H](c2ccc(Cl)cc2)SC(=O)N1C(=O)NC1CCCCC1,valid
Cc1c(COC(=O)[C@@H]2[C@H](/C=C(\Cl)C(F)(F)F)C2(C)C)cccc1-c1ccccc1,train
CCc1cccc(C)c1N(C(=O)CCl)[C@@H](C)COC,train
CCOC(=O)[C@@H](C)Oc1ccc(Oc2cnc3cc(Cl)ccc3n2)cc1,train
CC(C)[C@@H](Nc1ccc(C(F)(F)F)cc1Cl)C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1,train
C#CCOC(=O)[C@@H](C)Oc1ccc(Oc2ncc(Cl)cc2F)cc1,test
CCC(C)C(=O)O[C@H]1C[C@@H](OC(C)=O)[C@@]2(C(=O)OC)CO[C@H]3[C@@H](O)[C@@](C)([C@]45O[C@@]4(C)[C@H]4CC5O[C@@H]5OCC[C@@]54O)[C@H]4[C@]1(CO[C@]4(O)C(=O)OC)[C@@H]32,train
C[C@@H](Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1)C(=O)O,train
CCCCOC(=O)[C@@H](C)Oc1ccc(Oc2ccc(C#N)cc2F)cc1,train
CSC1=N[C@@](C)(c2ccccc2)C(=O)N1Nc1ccccc1,train
COC[C@H](C)N(C(=O)CCl)c1c(C)csc1C,valid
COC(=O)N(C(=O)N1CO[C@@]2(C(=O)OC)Cc3cc(Cl)ccc3C2=N1)c1ccc(OC(F)(F)F)cc1,train
CCOC(=O)OC1=C(c2cc(C)ccc2C)C(=O)N[C@]12CC[C@@H](OC)CC2,train
C/C=C\[C@@H]1[C@@H](C(=O)OCc2c(F)c(F)c(COC)c(F)c2F)C1(C)C,train
N#Cc1nn(-c2c(Cl)cc(C(F)(F)F)cc2Cl)c(N)c1[S@](=O)C(F)(F)F,train
N#Cc1nn(-c2c(Cl)cc(C(F)(F)F)cc2Cl)c(N)c1[S@@](=O)C(F)(F)F,test
Cc1ccc2c(c1)[C@H](Nc1nc(N)nc(C(C)F)n1)[C@@H](C)C2,train
CC(C)=C1C=C2CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1,train
C=C1[C@H]2CC[C@@H]3[C@H]2C(C)(C)CCC[C@]13C,train
CC(C)C1=CC2=CC[C@H]3[C@](C)(CO)CCC[C@]3(C)[C@H]2CC1,train
O=C(OC[C@H]1O[C@@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@@H]1OC(=O)c1cc(O)c(O)c(OC(=O)c2cc(O)c(O)c(O)c2)c1)c1cc(O)c(O)c(OC(=O)c2cc(O)c(O)c(O)c2)c1,valid
CC(C)C1=CC2=C(CC1)[C@@]1(C)CCC[C@@](C)(C(=O)O)[C@@H]1CC2,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O.O=[P](=O)(O)O,train
CCCCCCCCCC[C@H]1O[C@H]1CCCCC(C)C,train
COC(=O)[C@@]1(Cl)CCC[C@]2(C)c3ccc(C(C)C)cc3CC[C@@H]12,train
COC(=O)[C@@]1(Cl)C(Cl)CC[C@]2(C)c3ccc(C(C)C)cc3CC[C@@H]12,test
CC(C)c1c(Cl)cc2c(c1Cl)CC[C@H]1[C@](C)(C(=O)O)CCC[C@]21C,train
CC(C)[C@H](C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,train
CC(C)[C@@H](C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,train
CC(C)[C@@H](C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,train
CC[C@H](C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H](O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.CO[C@H]1C[C@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3/C(C)=C/C[C@@H]4C[C@@H](C[C@]5(C=C[C@H](C)[C@@H](C(C)C)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OC/C(=C\C=C\[C@@H]3C)[C@]54O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,valid
CC[C@H](C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@H](NC)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.CN[C@@H]1[C@H](C)O[C@@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3/C(C)=C/C[C@@H]4C[C@@H](C[C@]5(C=C[C@H](C)[C@@H](C(C)C)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OC/C(=C\C=C\[C@@H]3C)[C@]54O)C[C@@H]2OC)C[C@@H]1OC,train
CCO[C@@H]1[C@@H](OC)[C@H](C)O[C@@H](O[C@@H]2C[C@H]3CC[C@@H]4[C@@H](C=C5C(=O)[C@H](C)[C@@H](O[C@H]6CC[C@H](N(C)C)[C@@H](C)O6)CCC[C@H](CC)OC(=O)C[C@H]54)[C@@H]3C2)[C@@H]1OC,train
C=C(C)[C@H]1Cc2c(ccc3c2OC2COc4cc(OC)c(OC)cc4C2C3=O)O1,train
C=CCC1=C(C)C(OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,train
CC(C)c1ccc2c(c1)CCC1[C@](C)(C(=O)O)CCC[C@]21C,test
CC(C)C1=CC2=CCC3[C@](C)(CCC[C@@]3(C)C(=O)O)C2CC1,train
CC(C)=C1C=C2CCC3[C@](C)(CCC[C@@]3(C)C(=O)O)C2CC1,train
C=C[C@@]1(C)CCC2C(=CCC3[C@]2(C)CCC[C@@]3(C)C(=O)O)C1,train
CC1=CC[C@H]2C[C@H]1C2(C)C,train
ClC1CCCC[C@H]1Cl,valid
O=c1nc(NO)ccn1[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,train
Oc1ccc(O)c2c1[C@@H](O)[C@H]1O[C@H]1C21Oc2cccc3cccc(c23)O1,train
CC1(C)O[C@]2(C)CC[C@H]1CC2,train
CCOP(=O)(O)N[C@@H](CC(C)C)C(=O)O,train
CC(=O)OP(=O)(O)N(C[C@H]1CN(c2cc(F)c(N3C=CC(=O)CC3)c(F)c2F)C(=O)O1)c1ccon1,test
O[C@@H]1C=C[C@H](O)[C@@]23O[C@@]12C1(Oc2cccc4cccc(c24)O1)[C@@H]1O[C@@H]1[C@@H]3O,train
N[C@@H](C(=O)N1CCCC1)[C@@H](O)c1ccncc1,train
CCCCCCCC/C=C\CCCCCCCC(=O)N[C@@H]1C[C@H]1O,train
CC[C@]1(O)C[C@@H]2C[C@H](c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)c3[nH]c4ccccc4c3CCN(C2)C1,train
OC[C@H]1NC[C@H](O)[C@H]1O,valid
Nc1ncnc2c1ncn2[C@H]1CC[C@@H](COP(=O)(O)OP(=O)(O)OP(=O)(O)O)O1,train
CC1(C)CC[C@]2(C(=O)O)CC[C@]3(C)C(=CC[C@@H]4[C@@]5(C)CC[C@H](OC(=O)/C=C/c6ccc(O)cc6)C(C)(C)[C@@H]5CC[C@]43C)[C@@H]2C1,train
CC1(C)O[C@@H]2CC3C4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1.CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,train
CC(C)CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=NO)CC[C@]4(C)[C@H]3CC[C@]12C,train
C[C@@H]1CCC2C(C)(C)C3CC21CC[C@@]3(C)OC(=O)Cc1ccncc1,test
CCCC[C@@H](O)[C@@H](N)CC.CCCC[C@@H](O)[C@H](N)CC.CCCC[C@H](O)[C@@H](N)CC.CCCC[C@H](O)[C@H](N)CC,train
CC1=CC[C@H]2C[C@@H]1C2(C)C,train
COc1cc(Br)c2c3c1O[C@@H]1CC(=O)C=C[C@]31CCN(C)C2,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,train
C[C@H]1C(C)(C)C2=C(c3ncncc3CC2)C1(C)C,valid
CC(=O)OCC1O[C@@H](OC2[C@H](OC(C)CCCCCC/C=C\CCCCCCC(=O)O)OC(COC(C)=O)[C@@H](O)[C@@H]2O)C(O)[C@@H](O)[C@@H]1O.CC(=O)OCC1O[C@H]2OC(C)CCCCCC/C=C\CCCCCCC(=O)O[C@@H]3C(COC(C)=O)O[C@@H](OC2[C@@H](O)[C@@H]1O)C(O)[C@H]3O,train
C/C(=C\c1csc(C)n1)[C@H]1OC(=O)C[C@H](O)C(C)(C)C(=O)[C@H](C)[C@@H](O)[C@@H](C)CCC/C=C\[C@@H]1O,train
N[C@@H](CSS(=O)(=O)O)C(=O)O,train
COC(=O)OC1C/C=C\CCCC1.COC(=O)OC1C2CCCC1CC2.COC(=O)OC1CC/C=C\CCC1.COC(=O)O[C@@H]1CC[C@@H]2CCC[C@@H]21.COC(=O)O[C@H]1CC[C@@H]2CCC[C@@H]21,train
CC(C)(C)OC(=O)N[C@@H](CO)c1ccccc1,test
COC1=C[C@@H]2[C@@H]3Cc4ccc(OC)c(OCCCCCCOc5c(OC)ccc6c5[C@@]57CCN(C)[C@@H](C6)[C@H]5C=C(OC)C(=O)C7)c4[C@]2(CCN3C)CC1=O,train
CC1(C)[C@H](C(=O)OC(C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1/C=C(\Cl)C(F)(F)F,train
CCCCCCCCCCCCCCCCCC(=O)N1C[C@H](OS(=O)(=O)O)C[C@H]1C(=O)NCCCCCCCCCCCCCC,train
CCCCCC[C@H]1C(=O)O[C@H](C)[C@H](NC(=O)c2cccc(NC=O)c2O)C(=O)O[C@@H](C)[C@@H]1OC(=O)CC(C)C,train
O=C1O[C@H]([C@@H](O)CO)C(O)=C1O,valid
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@H]4C=C[C@H]3C4)[C@]1(Cl)C2(Cl)Cl,train
C=C(C)[C@@H]1[C@@H]2OC(=O)[C@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C.CC(C)(O)[C@@H]1[C@@H]2OC(=O)[C@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C,train
CCC(C)[C@H]1O[C@]2(CC[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H](O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.CO[C@H]1C[C@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3/C(C)=C/C[C@@H]4C[C@@H](C[C@]5(CC[C@H](C)[C@@H](C(C)C)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OC/C(=C\C=C\[C@@H]3C)[C@]54O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC(F)F,train
O=C(O)[C@@H]1[C@H](C(=O)O)[C@@H]2C=C[C@H]1O2,test
Ic1cncc([C@H]2C[C@H]3CC[C@H]2N3)c1,train
CC(C)(O)CC[C@@H](O)[C@](C)(O)[C@H]1CC[C@@]2(O)C3=CC(=O)[C@@H]4C[C@@H](O)[C@@H](O)C[C@]4(C)[C@H]3CC[C@]12C,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@@H](Cc2c[nH]cn2)NC1=O,train
C=C[C@H]1CN2CCC1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,train
CN(C)[C@@H]1C(=O)/C(=C(\N)O)C(=O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12.[H+],valid
CC1(C)[C@@H](C=C(Cl)Cl)[C@@H]1C(=O)OCc1cccc(Oc2ccccc2)c1,train
CN(C)[C@@H]1C(=O)/C(=C(\N)O)C(=O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)C3C[C@@H]12.[H+],train
NCCCCNCC(=O)NCCCNCCCNCCCCNC(=O)[C@H](CC(N)=O)NC(=O)c1cc2ccc(O)cc2oc1=O,train
CC1(C)[C@H](C=C(Cl)Cl)[C@H]1C(=O)O[C@H](C#N)c1ccc(F)c(Oc2ccccc2)c1,train
COC(=O)N[C@H]1[C@H](O[C@H]2[C@H](O)[C@@H](N)[C@H](O[C@H]3[C@H](O)[C@@H](N)[C@H](O)O[C@@H]3CO)O[C@@H]2CO)O[C@H](CO)[C@@H](O[C@@H]2O[C@H](CO)[C@@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@@H]4O[C@H](CO)[C@@H](O[C@@H]5O[C@H](CO)[C@@H](O[C@@H]6O[C@H](CO)[C@@H](O[C@@H]7O[C@H](CO)[C@@H](O)[C@H](O)[C@H]7N)[C@H](O)[C@H]6N)[C@H](O)[C@H]5N)[C@H](O)[C@H]4N)[C@H](O)[C@H]3N)[C@H](O)[C@H]2N)[C@@H]1O,test
CC#CCn1c(N2CCC[C@@H](N)C2)nc2c1c(=O)n(Cc1nc3ccccc3s1)c(=O)n2C,train
CCCCCCCCCCc1ccc2oc(NC[C@H]3CCCN3)nc2c1,train
O=C(O)c1cccc(/C=C2/C[C@@H]3[C@@H](/C=C/[C@H](O)C4CCCCC4)[C@H](O)C[C@@H]3O2)c1,train
C=C/C=C/CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,train
C=C(C)[C@H](CC=C(C)C)Cc1c(O)cc(O)c2c1O[C@H](c1ccc(O)cc1O)CC2=O,valid
CO[C@H]1[C@H](O)[C@@H](O)[C@H](n2c3ccccc3c3c4c(c5c6ccccc6[nH]c5c32)C(=O)N(C)C4)O[C@@H]1CO.CO[C@H]1[C@H](O)[C@@H](O)[C@H](n2c3ccccc3c3c4c(c5c6ccccc6[nH]c5c32)CN(C)C4=O)O[C@@H]1CO,train
C[Se]CC[C@H](N)C(=O)O,train
CCC(C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H](O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.CO[C@H]1C[C@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3/C(C)=C/C[C@@H]4C[C@@H](C[C@]5(C=C[C@H](C)[C@@H](C(C)C)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OC/C(=C\C=C\[C@@H]3C)[C@]54O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,train
CC12CCC(=O)C=C1CCC1C2[C@@H](O)CC2(C)C1CC[C@]2(O)C(=O)COP(=O)(O)O,train
CN(CC[C@H](N)CC(=O)N[C@@H]1C=C[C@@H](n2ccc(N)nc2=O)O[C@H]1C(=O)O)C(=N)N,test
C=C1C(C)(C)[C@@]2(Cl)C(Cl)[C@]1(Cl)C(Cl)(Cl)C2(Cl)Cl,train
Cc1cc(Cl)ccc1O[C@@H](C)C(=O)ON(C)C,train
COC(=O)[C@@H](N)Cc1ccc(O)cc1,train
CCC1O[C@]2(CCC1C)C[C@@H]1C[C@@H](C/C=C(\C)CC(C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,train
N=C(N)NCCC[C@H]1N[C@@H](CNC(=O)c2cnc3ccccc3c2)CCN(CC2CCCCC2)C1=O,valid
CC(C)=CC1[C@@H](C(=O)OCc2cccc(Oc3ccccc3)c2)C1(C)C,train
O=C(O)[C@@H]1[C@H](C(=O)O)[C@H]2CC[C@@H]1O2,train
CCCCCCCCC1CC(=O)N[C@@H](CC(N)=O)C(=O)N[C@H](Cc2ccc(O)cc2)C(=O)N[C@H](CC(N)=O)C(=O)N2CCC[C@H]2C(=O)N[C@@H](CCC(=O)O)C(=O)N[C@H](CO)C(=O)N[C@@H]([C@@H](C)O)C(=O)N1,train
C[C@H](NC(=O)[C@H](C)NC(=O)[C@@H](N)CCP(C)(=O)O)C(=O)O,train
CC1(C)[C@@H]([C@@H](Br)C(Br)(Br)Br)[C@H]1C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1,test
ClC1=C(Cl)[C@]2(Cl)[C@H]3C[C@H](Cl)[C@H](Cl)[C@H]3[C@@]1(Cl)C2(Cl)Cl,train
CC1=CCC[C@H]2[C@](C)(Cc3cc(O)ccc3O)[C@@H](C)CC[C@]12C,train
COc1cc2c(cc1OC)[C@@]13CCN4CC5=CCOC6CC(=O)N2C1[C@H]6[C@H]5C[C@H]43,train
CN([C@@H]1CCc2c(CC(=O)O)c3cc(S(C)(=O)=O)ccc3n2C1)S(=O)(=O)c1ccc(F)cc1,train
COC(=O)[C@@H]1c2cc3c(c(O)c2[C@@H](OC)C[C@@]1(C)O)C(=O)c1c(O)cc2c(c1C3=O)O[C@@H]1O[C@@]2(C)[C@H](O)[C@@H](N(C)C)[C@@H]1O,valid
C[C@H](CCC(=O)O)[C@H]1CC[C@H]2[C@@H]3CC[C@@H]4C[C@H](O)CC[C@]4(C)[C@H]3C[C@H](O)[C@]12C,train
CC(=O)NC(C(=O)N[C@H](Cc1cc2ccccc2n1C(C)=O)C(=O)NC(Cc1ccccc1)C(=O)N(C)Cc1ccccc1)C(C)O,train
CC1(C)OC[C@@H]2O[C@@]3(C(=O)O)OC(C)(C)O[C@H]3[C@@H]2O1,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](CO[C@H]2[C@H](O)[C@@H](O)[C@@H](OC(C(=O)O)C(O)C(OP(=O)(O)O)C(O)C(=O)O)O[C@@H]2CO)[C@@H](O)[C@H]1O,train
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3OC1CC(/N=c2/c(O)c(O)/c2=N\CCCCCC/N=c2\c(O)c(O)\c2=N\C2CC(O[C@H]3C[C@](O)(C(=O)CO)Cc4c(O)c5c(c(O)c43)C(=O)c3c(OC)cccc3C5=O)OC(C)C2O)C(O)C(C)O1,test
CC(C)(C)[C@H](O)[C@H](Cc1ccc(Cl)cc1)n1cncn1,train
CC(C)[C@@]1(O)[C@@H](OC(=O)c2ccc[nH]2)[C@@]2(O)[C@@]3(C)CC4(O)O[C@@]5([C@H](O)[C@@H](C)CC[C@]35O)[C@@]2(O)C41C,train
O=C1C[C@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@@H]3C[C@H]46,train
CC(C)(C)NCC(O)COc1cccc2c1C[C@H](O)[C@H](O)C2,train
C=CCC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,valid
C#CCC1=C(C)[C@H](OC(=O)[C@H]2[C@H](C=C(C)C)C2(C)C)CC1=O,train
COc1ccc([C@@H]2Sc3ccccc3N(CCN(C)C)C(=O)[C@@H]2OC(C)=O)cc1.O=C(O)CC(O)C(=O)O,train
CC(C)C[C@H](NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCCN)NC(=O)C(C)C)C(=O)N[C@@H](Cc1ccccc1)C(=O)NCC(=O)O,train
C/C=C(\C)C(=O)O[C@H]1[C@H](O)[C@@H](O)[C@H](O[C@@H]2[C@H]3OC(=O)CCCCCCCCC[C@H](CCCCC)O[C@@H]4O[C@H](C)[C@@H](O)[C@H](O)[C@H]4O[C@@H]4O[C@H](COC(=O)C(C)C)[C@@H](O)[C@H](O)[C@H]4O[C@H](O[C@H]2C)[C@@H]3OC(=O)[C@H](C)[C@@H](C)O)O[C@@H]1C,train
C[C@@H](Oc1ccc(Cl)cc1Cl)C(=O)O,test
CNCC[C@@H](Oc1ccc(C(F)(F)F)cc1)c1ccccc1,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3C[C@@H](Cl)[C@H](Cl)[C@H]3[C@@]1(Cl)C2(Cl)Cl,train
C[C@H]1COc2c(N3CCN(C)CC3)c(F)cc3c(=O)c(C(=O)O)cn1c23,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCCC(=O)NCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCS(C)(=O)=O)NC1=O,train
CCO[C@@H]1[C@@H](OC)[C@H](C)O[C@@H](O[C@@H]2C[C@H]3CC[C@@H]4[C@@H](C=C5C(=O)[C@H](C)[C@@H](O[C@H]6CC[C@H](N(C)C)[C@@H](C)O6)CCC[C@H](CC)OC(=O)C[C@H]54)[C@@H]3C2)[C@@H]1OC.CCO[C@@H]1[C@@H](OC)[C@H](C)O[C@@H](O[C@H]2C[C@H]3[C@@H]4C=C5C(=O)[C@H](C)[C@@H](O[C@H]6CC[C@H](N(C)C)[C@@H](C)O6)CCC[C@H](CC)OC(=O)C[C@H]5[C@@H]4C=C(C)[C@@H]3C2)[C@@H]1OC,valid
CC[C@H](C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@H](NC)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C(C)(C)C)C1(C)CCOCC1)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
CC(C)=C[C@@H]1[C@@H](C(=O)OCN2C(=O)C3=C(CCCC3)C2=O)C1(C)C,train
CC[C@H](C)[C@H](NC(=O)[C@@H](NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H]1CCCN1C(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](CCC(=O)O)NC(=O)[C@H](CCCCN)NC(=O)[C@H](C)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@@H](NC(=O)[C@H](CO)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](N)CCCCN)C(C)C)C(C)C)C(=O)NCC(=O)N[C@H](C(=O)N[C@@H](CO)C(=O)N[C@H](C(=O)N1CCC[C@H]1C(=O)N[C@@H](Cc1ccccc1)C(=O)O)[C@@H](C)CC)C(C)C,train
NCCCC[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@@H](N)CCCN=C(N)N)C(N)=O,test
CC=C[C@@H]1[C@@H](C(=O)OCc2c(F)c(F)c(COC)c(F)c2F)C1(C)C,train
CN(C)[C@@H]1C(=O)/C(=C(\N)O)C(=O)[C@@]2(O)C(=O)C3=C(O)c4c(O)ccc(Cl)c4[C@@](C)(O)[C@H]3C[C@@H]12,train
CCCCC(CC)COC(=O)[C@@H](C)Oc1ccc(Cl)cc1Cl,train
CC[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)C(NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(CC)CC,train
COc1ccc(C(=O)O[C@H]2CC[C@@]3(C)[C@@H]4CC[C@H]5[C@]6(O)C[C@H](O)[C@@]7(O)[C@@H](CN8C[C@@H](C)CC[C@H]8[C@@]7(C)O)[C@]6(O)C[C@@]53O[C@]24O)cc1OC,valid
C=C(C)O[C@@]12CO[C@@H]1C[C@@H]1C[C@@]13C(=O)[C@H](OC(C)=O)C1=C(C)[C@@H](OC(=O)[C@H](O)[C@@H](NC(=O)OC(C)(C)C)c4ccccc4)C[C@@](O)([C@H](OC(=O)c4ccccc4)[C@H]23)C1(C)C,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](N)c3ccc(O)cc3)C(=O)N2[C@H]1C(=O)O,train
CNCC[C@H](Oc1ccc(C(F)(F)F)cc1)c1ccccc1,train
CO[C@H]1C[C@H](O[C@H]2[C@H](C)OC(O[C@@H]3/C(C)=C\CC4CC(CC5(CC[C@H](C)CO5)O4)OC(=O)C4C=C(C)[C@@H](O)C5OC/C(=C/C=C\C3C)[C@@]45O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,train
CC1=C2C(=CO[C@H](C)[C@H]2C)C(=O)C(=C(O)O)C1=O,test
CN[C@@H]1[C@H](O[C@H]2[C@H](O[C@H]3[C@H](O)[C@@H](O)[C@H](NC(=N)N)[C@@H](O)[C@@H]3NC(=N)N)O[C@@H](C)[C@]2(O)C=O)O[C@@H](CO)[C@H](O)[C@H]1O,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](N)c3ccccc3)C(=O)N2[C@H]1C(=O)O,train
O=S(=O)(O)[C@H](O)CC[C@H](O)S(=O)(=O)O,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@](C)(Cc2ccccc2)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,train
OCC1=C[C@H](N[C@H]2C[C@H](CO)[C@@H](O[C@@H]3O[C@H](CO)[C@@H](O)[C@H](O)[C@H]3O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,valid
CCNC(=O)[C@H](C)OC(=O)Nc1ccccc1,train
CC1(C)[C@H](/C=C(\Cl)C(F)(F)F)[C@@H]1C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1,train
CNC.C[C@@H](Oc1ccc(Cl)cc1Cl)C(=O)O,train
C=C1C[C@]23C[C@@]1(O)CC[C@H]2[C@@]12C=C[C@H](O)[C@@](C)(C(=O)O1)[C@H]2[C@@H]3C(=O)O,train
CC[C@H](C)[C@@H]1NC(=O)[C@@H](Cc2ccc(OC)cc2)NC(=O)[C@H](N)C(C)(C)SSC[C@H](C(=O)N2CCC[C@H]2C(=O)N[C@@H](CCCN=C(N)N)C(=O)NCC(N)=O)NC(=O)[C@@H](CC(N)=O)NC(=O)[C@H](C(C)C)NC1=O,test
CC(=O)O[C@H]1C[C@H]2[C@H]([C@@H]3[C@@H](O)[C@@H]4[C@H]([C@H](C)C=C5OC(=O)[C@@](C)(O)[C@@]54C)[C@]31C)[C@@H](O)C(=O)[C@H]1C[C@@H]3O[C@@H]3[C@H](O)[C@@]12C,train
CN(C)[C@@H]1C(=O)/C(=C(\N)O)C(=O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12,train
C=CC[C@H]1OC(=O)CCNC(=O)[C@H](C)N(C)C(=O)[C@H](C(C)C)N(C)C(=O)[C@H]([C@@H](C)CC)NC(=O)[C@@H]2CCCN2C1=O,train
CNC(C)[C@@H]1CC[C@@H](N)[C@@H](OC2[C@@H](N)C[C@@H](N)[C@H](OC3OC[C@](C)(O)[C@H](NC)[C@H]3O)[C@H]2O)O1,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H]([C@H]4C=C[C@@H]3C4)[C@@]1(Cl)C2(Cl)Cl,valid
CC(C)(C)c1cc(C(C)(C)C)c2[nH]c(C(C)(C)C)c(C[C@H](NC(=O)[C@@H](N)CCCNC(=N)N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)NCCc3ccccc3)c2c1,train
COCCOC[C@@H](NC(=O)Nc1cc2[nH]nc(-c3cc(C)on3)c2cn1)c1ccccc1,train
Cc1cc(Cl)ccc1O[C@H](C)C(=O)O,train
c1ccc([C@H]2CN3CCSC3=N2)cc1,train
CC(C)c1ccc2c(c1Cl)CC[C@H]1[C@](C)(C(=O)O)CCC[C@]21C,test
CCC(C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,train
CCCCC(CC)CO[C@H]1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,train
CC(C)NC[C@@H](O)COc1cccc2ccccc12.[H+],train
C[C@H]1CN2CCC[C@H]2CN1C(=O)N1Cc2c(NC(=O)c3cccc(C#N)c3)n[nH]c2C1(C)C,train
C[C@@H](NC(=O)[C@@H](N)CC[S+](C)O)P(=O)(O)O,valid
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)C(NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CCCl,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(OC)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O,train
C[C@H](N)C(=O)N[C@@H](C)C(=O)N[C@H]1[C@@H]2CN(c3nc4c(cc3F)c(=O)c(C(=O)O)cn4-c3ccc(F)cc3F)C[C@@H]21,train
OC[C@H]1O[C@@H](c2ccc(Cl)c(Cc3ccc(O[C@H]4C[C@@H]5C[C@@H]5C4)cc3)c2)[C@H](O)[C@@H](O)[C@@H]1O,train
CCCCC(CC)COC(=O)[C@H](C)O,test
CN[C@@H](C)[C@H](O)c1ccccc1.[H+],train
CC(C)=CCC[C@@H](C)CC=O,train
CC(C)=CCC[C@@H](C)CCO,train
NC[C@@H]1CCC[C@H](CN)C1,train
CC1=CC2CC1[C@H]1C(=O)OC(=O)[C@@H]21,valid
CC1CC[C@@]2(OC1)O[C@H]1C[C@H]3[C@@H]4CC[C@@H]5C[C@@H](O[C@@H]6O[C@H](CO)[C@H](O)[C@H](O[C@@H]7OC[C@@H](O)[C@H](O)[C@H]7O)[C@H]6O[C@@H]6O[C@H](CO)[C@@H](O)[C@H](O)[C@H]6O)[C@@H](O)C[C@]5(C)[C@H]4CC[C@]3(C)[C@H]1[C@@H]2C,train
CC(C)[C@H](N)C(=O)N1Cc2ccccc2C[C@H]1C(=O)N[C@H](C(=O)O)C(C)C,train
CCCCCC[C@@H](C)[C@H](O)CC(=O)NCC(=O)N[C@H](C(=O)C[C@@H](CC(C)C)C(=O)N[C@@H](C)C(=O)N[C@@H](Cc1ccccc1)C(=O)OC)C(C)C,train
CCCCCCOC1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,train
CC(CC(=O)C[C@@H](C)[C@H]1CC(=O)[C@@]2(C)C3=C(C(=O)[C@@H](O)[C@]12C)[C@@]1(C)CC[C@H](O)C(C)(C)[C@@H]1CC3=O)C(=O)O,test
CC(=O)OC[C@H]1O[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H](OC(C)=O)[C@@H]1OC(C)=O,train
OC[C@@H]1[C@@H](O)[C@H](O)C[S+]1C[C@@H](O)[C@@H](O)[C@H](O)[C@H](O)[C@@H](O)CO,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O,train
OC[C@H]1O[C@@H](OC/C=C/c2ccccc2)[C@H](O)[C@@H](O)[C@@H]1O,train
CC(C)C1=CC2=CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1.CC(C)C1=CC2=CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1.[Ca+2],valid
COC1=CCC2=C(CC[C@@H]3[C@@H]2CC[C@]2(C)[C@@H](O)CC[C@@H]32)C1,train
CCOC(=O)[C@H](C)O,train
N=C(N)NCCC[C@H](N)C(=O)O,train
COC(=O)[C@H](C)O,train
C=CC(=O)OC1C[C@@H]2CC[C@@]1(C)C2(C)C,test
N[C@@H](CCC(=O)C[C@@H](CSSC[C@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)C(=O)NCC(=O)O)C(=O)O,train
CC(=O)OCCC1=CC[C@H]2C[C@@H]1C2(C)C,train
CC1(C)CCC[C@]2(C)[C@H]3CCO[C@]3(C)CC[C@@H]12,train
NC(=NCCCCCCN=C(N)N=C(N)Nc1ccc(Cl)cc1)N=C(N)Nc1ccc(Cl)cc1.O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,train
CC(=O)O[C@@H]1CCCC[C@@H]1C(C)(C)C,valid
COC(=O)Nc1nc2cc([S@](=O)c3ccccc3)ccc2[nH]1,train
CC(C)(C)[C@@H](O)[C@H](Cc1ccc(Cl)cc1)n1cncn1.CC(C)(C)[C@H](O)[C@@H](Cc1ccc(Cl)cc1)n1cncn1,train
CC(C)[C@H](Nc1ccc(C(F)(F)F)cc1Cl)C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,train
C=C1C(C)(C)[C@]2(Cl)C(Cl)[C@]1(Cl)C(Cl)(Cl)C2(Cl)Cl,train
ClC1=C(Cl)[C@@]2(Cl)[C@H]3[C@H]([C@H]4C=C[C@@H]3C4)[C@@]1(Cl)C2(Cl)Cl,test
COC(=O)[C@@H](C)Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1,train
CCO[P@@](=S)(Oc1cnc(C(C)(C)C)nc1)OC(C)C,train
CCO[P@](=S)(NC(C)C)Oc1ccccc1C(=O)OC(C)C,train
C[C@H]1C(c2ccc(Cl)cc2)SC(=O)N1C(=O)NC1CCCCC1,train
C[C@@H](Oc1ccc(Oc2ncc(Cl)cc2F)cc1)C(=O)O,valid
CCSC(=O)/C=C(C)/C=C/C[C@H](C)CCCC(C)(C)OC,train
O=[S@]1OC[C@@H]2[C@H](CO1)[C@@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,train
CC1(C)C(C=C(Cl)Cl)C1C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,train
CO[P@](=S)(Oc1cc(Cl)c(Br)cc1Cl)c1ccccc1,train
C#CCC1=C(C)[C@@H](OC(=O)[C@H]2[C@H](C=C(C)C)C2(C)C)CC1=O,test
CCOC(=O)CCC(=O)CC1(O)C2(Cl)[C@]3(Cl)C4(Cl)C(Cl)(Cl)C5(Cl)[C@@](Cl)(C1(Cl)[C@@]53Cl)[C@]42Cl,train
C[C@@H](Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1)C(=O)N(C)c1ccccc1F,train
C[C@@H](COc1ccc(C(C)(C)C)cc1)O[S@](=O)OCCCl,train
COC(=O)[C@]1(O)c2ccccc2-c2ccc(Cl)cc21,train
C[C@@H](Oc1ccc(Oc2ncc(Cl)cc2Cl)cc1)C(=O)N1CCCO1,valid
CC(C)(C)[C@@H](O)/C(=C/c1ccc(Cl)cc1Cl)n1cncn1,train
CC(C)OC(=O)[C@@H](C)N(C(=O)c1ccccc1)c1ccc(F)c(Cl)c1,train
CCCCCCCCCCCCN1C[C@@H](C)O[C@H](C)C1,train
CCO[P@](=S)(N[C@H](C)CC)Oc1cc(C)ccc1[N+](=O)[O-],train
COC(=O)[C@@H](C)N(C(=O)Cc1ccccc1)c1c(C)cccc1C,test
CC1(C)O[C@H](c2ccco2)CN1C(=O)C(Cl)Cl,train
CC1=CC[C@H](C(C)(C)O)CC1,train
CC(C)[C@@H](NC(=O)O)C(=O)N[C@H](C)c1nc2ccc(F)cc2s1,train
CC(C)OC(=O)N[C@@H](C(=O)N[C@H](C)c1nc2ccc(F)cc2s1)C(C)C,train
C/C=C(\C)C(=O)O[C@H]1C[C@@H](OC(C)=O)[C@@]2(C(=O)OC)CO[C@H]3[C@@H](O)[C@@](C)([C@]45O[C@@]4(C)[C@H]4C[C@@H]5O[C@H]5OCC[C@@]54O)[C@H]4[C@]1(CO[C@]4(O)C(=O)OC)[C@@H]32,valid
C=C(C)[C@@H]1CC=C(C)C(=O)C1,train
C[C@@H](Oc1cccc(Cl)c1)C(=O)O,train
Cc1ccc([C@H](C)NC(=O)C(NC(=O)OC(C)C)C(C)C)cc1,train
CP(=O)(O)CC[C@H](N)C(=O)O,train
CCCS[P@@](=O)(OCC)N1CCN(CC)/C1=N/C#N,test
CCNC(=O)[C@@H](C)OC(=O)Nc1ccccc1,train
C[C@@H](Oc1ccc(Oc2cnc3cc(Cl)ccc3n2)cc1)C(=O)O,train
COC(=O)CC(NC(=O)[C@@H](NC(=O)OC(C)C)C(C)C)c1ccc(Cl)cc1,train
C=C1C[C@]23C[C@@H]1CC[C@H]2[C@@]12CC[C@H](O)[C@](C)(C(=O)O1)[C@H]2[C@@H]3C(=O)O,train
COCC(=O)N(c1c(C)cccc1C)[C@H](C)C(=O)O,valid
COCC(=O)N(c1c(C)cccc1C(=O)O)[C@H](C)C(=O)O,train
Cc1ccc(C(=O)O)c(C2=N[C@](C)(C(C)C)C(=O)N2)c1,train
C=C1C[C@]23CC1(O)CC[C@H]2[C@@]12C=C[C@H](O)C(C)(C(=O)O1)[C@H]2[C@@H]3C(=O)O,train
COC(=O)[C@@H](C)N(C(=O)c1ccccc1)c1ccc(F)c(Cl)c1,train
COc1cc(CCCS(=O)(=O)O)ccc1O[C@H](Cc1cccc(OC)c1O)CS(=O)(=O)O,test
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1C=C(Cl)Cl,train
C#C[C@@H](OC(=O)[C@@H]1[C@H](C=C(C)C)C1(C)C)/C(C)=C/CC,train
COC(=O)N[C@H]1[C@H](O[C@H]2[C@H](O)[C@@H](N)[C@H](O[C@H]3[C@@H](O)[C@@H](N)[C@H](O)O[C@@H]3CO)O[C@@H]2CO)O[C@H](CO)[C@@H](O[C@@H]2O[C@H](CO)[C@@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@@H]4O[C@H](CO)[C@@H](O[C@@H]5O[C@H](CO)[C@@H](O[C@@H]6O[C@H](CO)[C@@H](O[C@@H]7O[C@H](CO)[C@@H](O)[C@H](O)[C@H]7N)[C@H](O)[C@H]6N)[C@H](O)[C@H]5N)[C@H](O)[C@H]4N)[C@H](O)[C@H]3N)[C@H](O)[C@H]2N)[C@@H]1O,train
O=C1[C@H]2CC=CC[C@H]2C(=O)N1SC(Cl)(Cl)Cl,train
O=C(O)[C@]1(O)c2ccccc2-c2ccc(Cl)cc21,valid
CCCCOC(=O)[C@@H](C)Oc1ccc(Oc2ccc(C(F)(F)F)cn2)cc1,train
C=C1[C@@H](C)[C@H]2[C@H](Cc3ccccc3)NC(=O)[C@]23OC(=O)/C=C/[C@H](O)CCC[C@@H](C)C/C=C/[C@H]3[C@@H]1O,train
CC(C)(C)[C@H](O)/C(=C\c1ccc(Cl)cc1)n1cncn1,train
C[C@@H](Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1)C(=O)O,train
CC[C@H](Oc1ccccc1)C(=O)O,test
C[C@@H](Oc1ccc(Oc2ccc(C#N)cc2F)cc1)C(=O)O,train
C=C1CC[C@H](O)C/C1=C/C=C1\CCC[C@]2(C)[C@@H]([C@H](C)CCCC(C)C)CC[C@@H]12,train
ClC1=C(Cl)[C@@]2(Cl)[C@H]3C[C@@H](Cl)[C@H](Cl)[C@H]3[C@@]1(Cl)C2(Cl)Cl,train
CCC1O[C@]2(CCC1C)C[C@@H]1C[C@@H](CC=C(C)CC(C)C=CC=C3CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,train
CNC(=O)O/N=C1/[C@@H](Cl)[C@H]2C[C@@H](C#N)[C@@H]1C2,valid
CC(C)[C@@]1(O)[C@@H](OC(=O)c2ccc[nH]2)[C@@]2(O)[C@@]3(C)C[C@]4(O)O[C@@]5([C@H](O)[C@@H](C)CC[C@]35O)[C@@]2(O)[C@@]14C,train
Cc1cnc(C2=N[C@](C)(C(C)C)C(=O)N2)c(C(=O)O)c1,train
CC=C(C)C(=O)O[C@H]1CC[C@@]2(C)[C@@H]3CC[C@H]4[C@]5(O)C[C@H](O)[C@@]6(O)[C@@H](CN7C[C@@H](C)CC[C@H]7[C@@]6(C)O)[C@]5(O)C[C@@]42O[C@]13O,train
CCCCO[C@@H](C)COC(=O)[C@@H](C)Oc1cc(Cl)c(Cl)cc1Cl,train
C[C@H](c1ccccc1CNCCN)[N+](=O)[O-],test
O=S1OC[C@@H]2[C@H](CO1)[C@@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,train
O=[S@@]1OC[C@@H]2[C@H](CO1)[C@@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,train
CCCCCCCC[C@H](Cl)[C@H](Cl)CCCCCCCC(=O)O,train
CC1(C)[C@H](C(=O)O[C@@H](C#N)c2ccc(F)c(Oc3ccccc3)c2)[C@@H]1C=C(Cl)Cl,train
C/C=C/[C@H]1[C@H](C(=O)OCc2c(F)c(F)c(C)c(F)c2F)C1(C)C,valid
CCC[C@@H](C)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,train
Cl[C@H]1[C@H](Cl)[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H]1Cl,train
CO[P@@](=S)(NC(C)C)Oc1ccc(C)cc1[N+](=O)[O-],train
O=CN[C@H](Nc1ccc(Cl)c(Cl)c1)C(Cl)(Cl)Cl,train
CC(C)(NC(=O)[C@H](Br)C(C)(C)C)c1ccccc1,test
CC1=CC[C@H](C(C)C)CC1,train
Cc1cccc([C@H](C)c2c[nH]cn2)c1C,train
O=C(Nc1ccc(Cl)cc1[C@@H](O)c1ccccc1)c1ccncc1,train
C[C@H]1O[C@H](O[C@H]2[C@H](O)[C@@H](O)[C@@H](O)[C@@H](O)[C@@H]2O)[C@H](N)C[C@@H]1NC(=N)C(=O)O,train
C[C@H](CCl)O[C@H](C)CCl,valid
COCC1O[C@@H](O[C@@H]2C(COC)OC(OC)C(OC)C2OC)C(OC)C(OC)C1OC,train
C[C@H](O)CC(C)(C)O,train
CCC(CC)c1cccc(OC(=O)NC)c1.CCC[C@@H](C)c1cccc(OC(=O)NC)c1,train
CC(C)C[C@H](C)c1sccc1NC(=O)c1cn(C)nc1C(F)(F)F,train
C#C[C@@H](C)Oc1cc(N2C(=O)C3=C(CCCC3)C2=O)c(F)cc1Cl,test
CCOC(=O)[C@@H](Cl)Cc1cc(-n2nc(C)n(C(F)F)c2=O)c(F)cc1Cl,train
CCN(CC)C(=O)[C@@H](C)Oc1cccc2ccccc12,train
C[C@@H](NC(=O)[C@@H](C#N)C(C)(C)C)c1ccc(Cl)cc1Cl,train
COCC(=O)O[C@H](c1ncccc1S(=O)(=O)NC(=O)Nc1nc(OC)cc(OC)n1)[C@@H](C)F,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)CO,valid
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1C[C@@H](O)[C@@H]2O,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CCC2=O,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@@]21C,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@]2(C)O,test
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@@H]2O,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@@]21C,train
CC[C@H](CC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CC=C4C[C@@H](O)CC[C@]4(C)[C@H]3CC[C@]12C)C(C)C,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C=C(Cl)C4=CC(=O)[C@@H]5C[C@@H]5[C@]4(C)[C@H]3CC[C@@]21C,train
C[C@]12CCC(=O)C[C@@H]1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@]2(C)O,valid
C[C@]12CC[C@H]3[C@@H](CC[C@@]45O[C@@H]4C(O)=C(C#N)C[C@]35C)[C@@H]1CC[C@@H]2O,train
C[C@]12C=CC3=C4CCC(=O)C=C4CC[C@H]3[C@@H]1CC[C@@H]2O,train
COc1ccc2cc([C@H](C)C(=O)O)ccc2c1,train
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1C=C(Br)Br,train
C=C1C(=O)N[C@H](C)C(=O)N[C@H](CC(C)C)C(=O)N[C@@H](C(=O)O)[C@H](C)C(=O)N[C@@H](CCCCNC(=N)N)C(=O)N[C@@H](/C=C/C(C)=C/[C@H](C)[C@H](Cc2ccccc2)OC)[C@H](C)C(=O)N[C@@H](C(=O)O)CCC(=O)N1C,test
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H]1Cl,train
Cl[C@H]1CCCC[C@@H]1Cl,train
Cc1c(COC(=O)[C@@H]2[C@H](/C=C(\Cl)C(F)(F)F)C2(C)C)cccc1-c1ccccc1,train
COc1cccc([C@]2(O)CCCC[C@@H]2CN(C)C)c1,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@@]3(C#N)CC[C@@]21C,valid
Nc1ccn([C@H]2C[C@H](O)[C@@H](CO)S2)c(=O)n1,train
N[C@@H](C(=O)N1CCCC1)[C@@H](O)c1ccncc1,train
CCCC[C@@H](CC)COC(=O)c1ccccc1C(=O)OC[C@@H](CC)CCCC,train
Cl[C@@H]1CCCC[C@H]1Cl,train
C=C/C=C/CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,test
COc1cccc([C@@]2(O)CCCC[C@@H]2CN(C)C)c1,train
CCc1cccc(C)c1N(C(=O)CCl)[C@@H](C)COC,train
CCOC(=O)[C@@H](C)Oc1ccc(Oc2cnc3cc(Cl)ccc3n2)cc1,train
C[C@H]1CN(C2CCN(c3nc(N)n[nH]3)CC2)[C@@H](Cc2ccc(Cl)cc2)CO1,train
COC(=O)N(C(=O)N1CO[C@@]2(C(=O)OC)Cc3cc(Cl)ccc3C2=N1)c1ccc(OC(F)(F)F)cc1,valid
Cc1ccc2c(c1)[C@H](Nc1nc(N)nc(C(C)F)n1)[C@@H](C)C2,train
CN(C)[C@@H]1C(=O)C(C(N)=O)=C(O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12,train
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@@H](C)O[C@@H](C)C1,train
Cc1ccc([C@@H]2O[C@H](COC(=O)C(C)(C)C)[C@@H](OC(=O)C(C)(C)C)[C@H](OC(=O)C(C)(C)C)[C@H]2OC(=O)C(C)(C)C)cc1Cc1ccc(-c2ccc(F)cc2)s1,train
Cn1c(COc2ccc(CC3SC(=O)N([C@@H]4O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]4O)C3=O)cc2)nc2ccc(O)cc21,test
C[C@@H]1COc2ccccc2N1C(=O)C(Cl)Cl.C[C@H]1COc2ccccc2N1C(=O)C(Cl)Cl,train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nnn[nH]2)cc1)[C@H](C(=O)O)C(C)C,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,train
Cc1cc(Cl)ccc1O[C@H](C)C(=O)O,train
O=C1[C@H](CC[C@H](O)c2ccc(F)cc2)[C@@H](c2ccc(O)cc2)N1c1ccc(F)cc1,valid
COC(=O)[C@H](c1ccccc1Cl)N1CCc2sccc2C1,train
OCc1cc([C@@H](O)CNCCCCCCOCCOCc2c(Cl)cccc2Cl)ccc1O,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2C(=O)C[C@@]2(C)[C@H]1CC[C@]2(O)C(=O)CO,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@@]21C,test
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O,train
NC[C@@H]1O[C@H](O[C@H]2[C@@H](O)[C@H](O[C@@H]3[C@@H](O)[C@H](N)C[C@H](N)[C@H]3O[C@H]3O[C@H](CN)[C@@H](O)[C@H](O)[C@H]3N)O[C@@H]2CO)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@H]4O[C@H](CN)[C@@H](O)[C@H](O)[C@H]4N)[C@H]3O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12,valid
CC(C)(O)CC[C@@H](O)[C@](C)(O)[C@H]1CC[C@@]2(O)C3=CC(=O)[C@@H]4C[C@@H](O)[C@@H](O)C[C@]4(C)[C@H]3CC[C@]12C,train
CC(C)NC[C@@H](O)COc1cccc2ccccc12,train
CC(=O)N1CCN(c2ccc(OC[C@H]3CO[C@](Cn4ccnc4)(c4ccc(Cl)cc4Cl)O3)cc2)CC1,train
CCOC(=O)[C@@H](C)Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1,train
CN[C@H]1CC[C@@H](c2ccc(Cl)c(Cl)c2)c2ccccc21,test
Cc1c(COC(=O)[C@@H]2[C@H](/C=C(\Cl)C(F)(F)F)C2(C)C)cccc1-c1ccccc1,train
CNCC[C@H](Oc1ccc(C(F)(F)F)cc1)c1ccccc1,train
CNCC[C@@H](Oc1ccc(C(F)(F)F)cc1)c1ccccc1,train
Cc1ccc2c(c1)[C@H](Nc1nc(N)nc(C(C)F)n1)[C@@H](C)C2,train
CCCCCCCCCCCCCCCCCC(=O)OCC(O)[C@H]1OC[C@@H](O)[C@@H]1O,valid
COC1=C[C@@H]2[C@@H]3Cc4ccc(OC)c(OCCCCCCOc5c(OC)ccc6c5[C@@]57CCN(C)[C@@H](C6)[C@H]5C=C(OC)C(=O)C7)c4[C@]2(CCN3C)CC1=O,train
Cc1nc(/C=C(\CO)[C@@H]2C/C=C\CCC[C@H](C)[C@H](O)[C@@H](C)C(=O)C(C)(C)[C@@H](O)CC(=O)O2)cs1,train
OC[C@@H](O)[C@@H](O)[C@H](O)[C@@H](O)C(c1c[nH]c2ccccc12)c1c[nH]c2ccccc12,train
CC[C@]1(O)C[C@@H]2C[C@H](c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)c3[nH]c4ccccc4c3CCN(C2)C1,train
CC(C)/C(O)=C1/C(=O)O[C@@H](c2ccoc2)[C@]2(C)C(O)[C@@H](O)[C@@]34OC5(C)OC67CC(C)(C(O)[C@]6(O)[C@@H](O)[C@]3(O5)C12)[C@H](CC(=O)O)[C@]74C,test
CN[C@H](CC(=O)O)C(=O)O,train
CCC(C)C(N)C1=NCC(C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](CCC(=O)O)C(=O)N[C@H](C(=O)N[C@H]2CCCCNC(=O)[C@H](CC(N)=O)NC(=O)[C@@H](CC(=O)O)NC(=O)[C@H](Cc3cnc[nH]3)NC(=O)[C@@H](Cc3ccccc3)NC(=O)[C@H]([C@@H](C)CC)NC(=O)[C@@H](CCCN)NC2=O)[C@@H](C)CC)S1.[Zn+2],train
Cc1nn(C)cc1S(=O)(=O)NC(=O)c1ccc(-n2ccc(OCC(C)(C)C(F)(F)F)n2)nc1N1C[C@@H](C)CC1(C)C,train
CN(C)[C@]12C(=O)C(c3ccccc3)=C(c3ccccc3)[C@H]1C1CCC2C1,train
CCCCC(CC)COC(=O)[C@H](C)O,valid
CCCCCCCCCCCCC/C=C/[C@@H](O)[C@H](CO)NC(=O)CCCCCCC,train
CC[C@H](C)[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)CNC(=O)[C@@H](N)Cc1ccc(O)cc1)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CCCCN)C(N)=O,train
CC(C)n1c(/C=C/[C@@H](O)C[C@@H](O)CC(=O)OC(C)(C)C)c(-c2ccc(F)cc2)c2ccccc21,train
CC1(C)O[C@@H]2CC3C4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1.CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,train
CCCCCCCCCCCC(=O)NCCCC[C@H](N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(N)=O,test
NC[C@H]1O[C@H](O[C@H]2[C@H](OC(=O)Nc3ccccc3)[C@@H](OC(=O)Nc3ccccc3)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](OC(=O)Nc2ccccc2)[C@@H]1OC(=O)Nc1ccccc1.O=C(O)C(F)(F)F,train
Oc1ccc(O)c2c1[C@@H](O)[C@H]1O[C@H]1C21Oc2cccc3cccc(c23)O1,train
COC(=O)N[C@@H](C(=O)N1CCC[C@H]1C(=O)Nc1ccc(-c2ccc(NC(=O)[C@@H]3CCCN3C(=O)[C@H](NC(=O)OC)c3ccccc3)cc2C(F)(F)F)c(C(F)(F)F)c1)c1ccccc1,train
CCCCC(CC)/N=C1\N[C@H](CO)[C@H](O)[C@H](O)[C@H]1O,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O,valid
CCOP(=O)(O)N[C@@H](CC(C)C)C(=O)O,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,train
OCC(COC1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O)OC1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O.OCC(O)COC1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O.OC[C@H]1OC(OCC(COC2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)OC2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,train
CC(C)[C@H]1CC=C(CCC=O)CC1,train
C=CC(=O)O[C@H]1C[C@@H]2CC[C@@]1(C)C2(C)C,test
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3OC1CC(/N=c2/c(O)c(O)/c2=N\CCCCCC/N=c2\c(O)c(O)\c2=N\C2CC(O[C@H]3C[C@](O)(C(=O)CO)Cc4c(O)c5c(c(O)c43)C(=O)c3c(OC)cccc3C5=O)OC(C)C2O)C(O)C(C)O1,train
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@@H](C)O[C@@H](C)C1,train
C1CCC(NC2CCCCC2)CC1.CC(C)(O)c1ccccc1CC[C@@H](SCC1(CC(=O)O)CC1)c1cccc(/C=C/c2ccc3ccc(Cl)cc3n2)c1,train
CC(=O)C1=C(C)[C@@H]2C[C@@]3(C1)[C@H](C)CC[C@H]3C2(C)C,train
C[C@H](NC(=O)[C@@H](NC(=O)CNC(=O)[C@H](Cc1c[nH]cn1)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)[C@H](CC(N)=O)NC(=O)CN)[C@@H](C)O)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)O,valid
CCCC[C@H](N)C(=O)O,train
O=C(O[C@H]1OC[C@@H](OC(=O)c2cc(O)c(O)c(O)c2)[C@H](OC(=O)c2cc(O)c(O)c(O)c2)[C@H]1OC(=O)c1cc(O)c(O)c(O)c1)c1cc(O)c(O)c(O)c1,train
ClC[C@H]1CO1,train
CC(C)[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@@H](N)CC(=O)O)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@H]1CC/C=C/SC[C@@H](C(=O)N[C@@H](Cc2ccccc2)C(=O)O)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,train
CNC(=O)[C@H](Cc1ccc(OC)cc1)NC(=O)C1(CC(=O)NO)CCCCC1,test
CC(C)(C)C(=O)OC[C@H]1O[C@H](Br)[C@H](OC(=O)C(C)(C)C)[C@@H](OC(=O)C(C)(C)C)[C@@H]1OC(=O)C(C)(C)C,train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nnn[nH]2)cc1)[C@H](C(=O)O)C(C)C,train
C[C@H](c1ccccc1)c1cccc([C@@H](C)c2ccccc2)c1O,train
CC1(C)C[C@@H]1C(=O)N/C(=C\CCCCSC[C@H](N)C(=O)O)C(=O)O.C[C@@H](O)[C@H]1C(=O)N2C(C(=O)O)=C(SCC/N=C\N)C[C@H]12,train
CCCCCCCCCCC/C=C/CCCCC(=O)N(C)C[C@H](C)[C@@H](C)[C@H](C)[C@H](C)CO.CCCCCCCCCCCCCCCCCC(=O)N(C)C[C@H](C)[C@@H](C)[C@H](C)[C@H](C)CO,valid
C[C@]12CCC(=O)C=C1C=C[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@@]2(O)CCC(=O)O,train
CC1=CCC2CCCCC2C1N1C(=O)[C@@H]2[C@@H]3C[C@@H]4[C@@]5(C)CCC[C@@](C)(C(=O)O)[C@@H]5CC[C@]4(C=C3C(C)C)[C@@H]2C1=O,train
COc1ccc2c3c1O[C@H]1C(=O)CC[C@@]4(O)[C@@H](C2)N(C)CC[C@]314.COc1ccc2c3c1O[C@H]1C(=O)CC[C@@]4(O)[C@@H](C2)N(C)CC[C@]314.O=C(O)c1ccc(C(=O)O)cc1,train
CC(OC(=O)OC1CCCCC1)OC(=O)C1=C(CSc2nnnn2CCN(C)C)CS[C@@H]2[C@H](NC(=O)Cc3csc(N)n3)C(=O)N12,train
Cc1ccc(C2OC[C@@H]3OC(c4ccc(C)cc4)O[C@H]([C@H](O)CO)[C@@H]3O2)cc1,test
CCC1(CC[C@@H]2[C@H](C)CCCC2(C)C)CCCO1.CC[C@@]12CCC3C(C)(C)CCC[C@]3(C)[C@@H]1CCO2.CC[C@@]12CCC3C(C)(C)CCC[C@]3(C)[C@@H]1CCO2,train
CC1(C)[C@H]2CC=C(CCO)[C@@H]1C2,train
O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.[Zn+2],train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nn[nH]n2)cc1)[C@H](C(=O)OC)C(C)C,train
C[C@H](OC(C)(C)COC(=O)C1CC1)[C@@H]1CCCC(C)(C)C1.C[C@H](OC(C)(C)COC(=O)C1CC1)[C@H]1CCCC(C)(C)C1,valid
CN1[C@@H]2C[C@H](OC(=O)Nc3ccc(F)cc3-c3ccccc3)C[C@H]1[C@@H]1O[C@@H]12,train
O=C1N[C@H]2[C@H](OP(=O)(O)O)[C@H](O)[C@@H](O)[C@H](O)[C@@H]2c2cc3c(c(O)c21)OCO3.[Zn+2],train
C=C(C)[C@@H]1CC[C@]2(C(=O)NCC(=O)N3CCC[C@H]3C(=O)O)CC[C@]3(C)[C@H](CC[C@@H]4[C@@]5(C)CC[C@H](O)[C@@](C)(CO)[C@@H]5CC[C@]43C)[C@@H]12,train
Cc1ccccc1NC(=O)Nc1ccc(CC(=O)N(C)[C@@H](CC(C)C)C(=O)NCC[C@H](NC(=O)[C@@H]2CCCN2S(=O)(=O)c2cc(Cl)cc(Cl)c2)C(=O)O)cc1,train
CCCCCCCCO[C@H]1O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]1O[C@H]1O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]2O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]3O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]4O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]4OC(C)=O)[C@@H]3OC(C)=O)[C@@H]2OC(C)=O)[C@@H]1OC(C)=O,test
Cc1ccc(C#Cc2ccc(S(=O)(=O)N[C@H](Cc3c[nH]c4ccccc34)C(=O)O)s2)cc1,train
COC1[C@H](NC(=O)CC2OC(COCc3ccccc3)C(OCc3ccccc3)C(OCc3ccccc3)C2OCc2ccccc2)CC[C@H]2[C@H]3Cc4ccc(O)cc4[C@@]12CCN3C,train
C[C@@H](N)COc1ccc(-c2cnc3ccc(N[C@H](C)c4cccc(F)c4)nn23)cc1.O=C(O)CCCCC(=O)O,train
CCCCOC(=O)[C@H]1CCCC[C@H]1C(=O)OCc1ccccc1,train
CC(=O)N[C@@H](C)C(=O)N[C@@H](CO)C(=O)N[C@@H](CSSC[C@@H](NC(=O)[C@H](CO)NC(=O)[C@H](C)NC(C)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1ccc(O)cc1)C(N)=O)[C@@H](C)O)C(C)C)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1ccc(O)cc1)C(N)=O)[C@@H](C)O)C(C)C,valid
CO[C@@H]1C/C=C/[C@H](C)[C@@H]2O[C@](O)([C@H](C)C(=O)O[C@H](c3ccccc3)CCCC1)[C@H](OC)[C@@H](O)[C@@H]2C,train
C[C@H]1CN(C2CCN(c3nc(N)n[nH]3)CC2)[C@@H](Cc2ccc(Cl)cc2)CO1,train
CC(CC(=O)C[C@@H](C)[C@H]1CC(=O)[C@@]2(C)C3=C(C(=O)[C@@H](O)[C@]12C)[C@@]1(C)CC[C@H](O)C(C)(C)[C@@H]1CC3=O)C(=O)O,train
CC1([C@H]2CC[C@H]3C4=CC=C5CC6(CC[C@]5(C)[C@H]4CC[C@]23C)OCCO6)OCCO1,train
CSCC[C@H](NC(=O)[C@H](CO)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@@H](N)CO)C(=O)N[C@@H](CCC(=O)O)C(=O)N[C@@H](Cc1c[nH]cn1)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)NCC(=O)N[C@@H](CCCCN)C(=O)N1CCC[C@H]1C(=O)N[C@H](C(=O)NCC(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N1CCC[C@H]1C(=O)N[C@H](C(=O)N[C@@H](CCCCN)C(=O)N[C@H](C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](C)C(=O)NCC(=O)N[C@@H](CCC(=O)O)C(=O)O)C(C)C)C(C)C)C(C)C,test
CCC(O)CCCCCCC[C@H](O)[C@H]1CC[C@H]([C@H]2CC[C@H]([C@H](O)CCCCCCCCCCC3CC(CC(C)=O)C(=O)O3)O2)O1,train
C[C@@H]1C/C=C\CCCCCCCCCC(=O)C1,train
CCOC(=O)[C@]1(C)CCC[C@@]2(C)[C@H]1CC=C1C=C(C(C)C)CC[C@@H]12,train
C#C[C@]1(OC(C)=O)CC[C@H]2[C@@H]3CCC4=C[C@@H](OC(C)=O)CC[C@@H]4[C@H]3CC[C@@]21C,train
CC(=O)N[C@@H](CCCCN)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](CCCNC(=N)N)C(N)=O,valid
N[C@@H](CCC(=O)C[C@@H](CSSC[C@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)C(=O)NCC(=O)O)C(=O)O,train
O=c1nc(NO)ccn1[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,train
Br[C@H]1CC[C@H](Br)[C@@H](Br)CC[C@H](Br)[C@@H](Br)CC[C@@H]1Br,train
COC(=O)O[C@@]12CO[C@@H]1C[C@H](O)[C@@]1(C)C(=O)[C@H](OC(C)=O)C3=C(C)[C@@H](OC(=O)[C@H](O)[C@@H](NC(=O)c4ccccc4)c4ccccc4)C[C@@](O)([C@@H](OC(=O)c4ccccc4)[C@H]21)C3(C)C,train
N[C@@H]1CC2OC1c1ccccc12,test
CCCC[C@@H](NC(=O)[C@@H](N)CC)B(O)O,train
C=C(C)[C@H]1CC=C(C)CC1,train
CCO[C@@H]1[C@]2(CC)C=CCN3CC[C@@]4(c5ccc(OC)cc5N(C)[C@H]4[C@@]1(O)C(=O)OC)[C@@H]32,train
CC(C)C[C@H](NC(=O)CNC(=O)[C@@H]1CCCN1C(=O)[C@@H]1CCCN1C(=O)CNC(=O)[C@H](CO)NC(=O)[C@@H](N)CCCN=C(N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)NCC(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CCC(N)=O)C(=O)O,train
CO[C@]12C[C@@H](CO)CN(C)[C@@H]1Cc1cn(C)c3cccc2c13,valid
COC1[C@H](NC(=O)CC2OC(COCc3ccccc3)C(OCc3ccccc3)C(OCc3ccccc3)C2OCc2ccccc2)CC[C@H]2[C@H]3Cc4ccc(O)cc4[C@@]12CCN3C,train
Cc1nc(/C=C(\CO)[C@@H]2C/C=C\CCC[C@H](C)[C@H](O)[C@@H](C)C(=O)C(C)(C)[C@@H](O)CC(=O)O2)cs1,train
Nc1ccc(CCNC[C@H](O)c2ccccc2)cc1,train
C[C@]12C[C@H]3O[C@H]3C[C@@H]1CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)[C@@H](N3CCCC3)C[C@@H]12,train
CCOc1ccc(Cc2cc([C@@H]3O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@H]3OC(C)=O)ccc2Cl)cc1,test
CCCC[C@H](N)C(=O)O,train
CC1(C)O[C@@H]2CC3C4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1.CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,train
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1OC(=O)[C@@H]1O[C@@H](O)CS1,train
Fc1ccc2c(c1)CC[C@@H]([C@H]1CO1)O2,train
Cn1c(COc2ccc(CC3SC(=O)N([C@@H]4O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]4O)C3=O)cc2)nc2ccc(O)cc21,valid
OC[C@@H](O)[C@@H](O)[C@H](O)[C@@H](O)C(c1c[nH]c2ccccc12)c1c[nH]c2ccccc12,train
CC(=O)O[C@H]1C(C)(C)[C@H]2CC[C@]1(C)C2,train
CN1CCN(C(=O)Cc2ccc(Cl)c(Cl)c2)[C@H]2[C@@H]1CCC[C@@H]2N1CCCC1,train
F[C@@H]([C@H](F)C(F)(F)C(F)(F)F)C(F)(F)F.F[C@H]([C@@H](F)C(F)(F)C(F)(F)F)C(F)(F)F,train
COC(=O)[C@@H](C)Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1,test
CC(C)C[C@@H](NC(=O)[C@H](O)COS(=O)(=O)O)C(=O)N[C@@H]1C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@H]2CC[C@H](O)N(C2=O)[C@@H](CC(C)C)C(=O)N(C)[C@H](Cc2ccc(O)cc2)C(=O)N[C@H](C(C)C)C(=O)O[C@@H]1C,train
CCOc1ccc(C[C@H](N)CNCCN)cc1,train
Fc1ccc2c(c1)CC[C@@H]([C@@H]1CO1)O2,train
CC[C@]1(O)C[C@@H]2C[C@H](c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)c3[nH]c4ccccc4c3CCN(C2)C1,train
C[C@]12CC=C3[C@@H](CC[C@@]45CC6(CC[C@@]34O5)OCCO6)[C@@H]1CCC2=O,valid
CN[C@H](CC(=O)O)C(=O)O,train
C1CCC(NC2CCCCC2)CC1.CC(C)(F)C[C@H](N[C@@H](c1ccc(-c2ccc(S(C)(=O)=O)cc2)cc1)C(F)(F)F)C(=O)O,train
O[C@H]1CNC[C@@H]1O,train
COc1ccc(C(OC[C@H]2O[C@@H](n3cnc4c(=O)[nH]c(NC(=O)C(C)C)nc43)C[C@@H]2O)(c2ccccc2)c2ccc(OC)cc2)cc1,train
C[C@]12CC[C@H]3[C@@H](C=C[C@]4(O)C[C@@H](O)CC[C@]34C)[C@@H]1[C@@H]1C[C@@H]1C2=O,test
CCN1CCN(C(=O)Cc2ccc(Cl)c(Cl)c2)[C@H]2[C@@H]1CCC[C@@H]2N1CCCC1,train
CC(C)[C@H](N)C(=O)OCc1ccccc1.Cc1ccc(S(=O)(=O)O)cc1,train
CCCCOC(=O)[C@H]1CCCC[C@H]1C(=O)OCc1ccccc1,train
CN(C)[C@]12C(=O)C(c3ccccc3)=C(c3ccccc3)[C@H]1C1CCC2C1,train
N[C@@H](C(=O)N1CCCC1)[C@@H](O)c1ccncc1,valid
CC1=CC[C@@H](C(C)CC=O)CC1,train
CC[C@H](C)[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)CNC(=O)[C@@H](N)Cc1ccc(O)cc1)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CCCCN)C(N)=O,train
C[C@]12CC[C@H]3[C@@H]([C@H]4C[C@H]4[C@]4(O)C[C@@H](O)CC[C@]34C)[C@@H]1[C@@H]1C[C@@H]1C2=O,train
NC(=O)CC[C@@H](C(=O)O)N(CCNC(=S)Nc1ccc2c(c1)C(=O)OC21c2ccc(O)cc2Oc2cc(O)ccc21)C(=O)c1ccc(NCc2cnc3nc(N)[nH]c(=O)c3n2)cc1,train
CN1CCN(c2ncccc2CO)[C@@H](c2ccccc2)C1.CN1CCN(c2ncccc2CO)[C@H](c2ccccc2)C1,test
C[C@@H](N)c1ccccc1,train
CNC[C@H](O)c1cccc(O)c1,train
Fc1c(Cl)cc([C@]2(C(F)(F)F)CC(c3ccc4c(c3)COC43CNC3)=NO2)cc1Cl.O=S(=O)(O)c1ccccc1,train
CC(C)CCC[C@H](C)CCO,train
O=C(O)[C@H](O)[C@@H](O)C(=O)O,valid
O=c1nc(NO)ccn1[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,train
CCCCCCCCCCCC(=O)NCCCC[C@H](N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(N)=O,train
CC(=O)N[C@@H](C)C(=O)N[C@@H](CO)C(=O)N[C@@H](CSSC[C@@H](NC(=O)[C@H](CO)NC(=O)[C@H](C)NC(C)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1ccc(O)cc1)C(N)=O)[C@@H](C)O)C(C)C)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1ccc(O)cc1)C(N)=O)[C@@H](C)O)C(C)C,train
COc1ccc2c(c1)CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,train
O[C@@H]1CO[C@H]2[C@@H]1OC[C@@H]2O,test
C[C@H](N[C@@H]1CCc2[nH]c3ccccc3c2C1)c1ccccc1,train
Oc1ccc(O)c2c1[C@@H](O)[C@H]1O[C@H]1C21Oc2cccc3cccc(c23)O1,train
CC(=O)N[C@@H](CCC(=O)O)C(=O)O.CC(C)C[C@H](N)c1ccccc1N1CCCCC1,train
CCCN(CCc1cccs1)[C@H]1CCc2c(O)cccc2C1,train
CNS(=O)(=O)C[C@H]1CC[C@H](N(C)c2ncnc3[nH]ccc23)CC1,valid
CC(=O)O[C@@H]1C[C@H](C)CC[C@H]1C(C)C,train
C[C@H](O)C(=O)O,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O,train
O=C(O)CC[C@H](NP(=O)(O)OCCCCc1ccccc1)C(=O)O,train
C[C@H]1CC[C@@]2(O)C(C)(C)[C@@H]3CC[C@@]2(C)[C@H]1C3,test
CO[C@@H]1C/C=C/[C@H](C)[C@@H]2O[C@](O)([C@H](C)C(=O)O[C@H](c3ccccc3)CCCC1)[C@H](OC)[C@@H](O)[C@@H]2C,train
CSCC[C@H](N)C(=O)O,train
CCOC(=O)[C@@]12CCC[C@@H]1[C@@H]1CC[C@H]2C1,train
CC(=C/C(=O)O)/C=C/[C@@]1(O)[C@@H](C)CCCC1(C)C,train
C=C(C)[C@H]1CC=C(C)CC1,valid
CCOP(=O)(O)N[C@@H](CC(C)C)C(=O)O,train
CCCN[C@H]1CCc2c(cccc2OC)C1,train
C=C(C)[C@@H]1CC[C@@H](C)C[C@H]1O,train
CC1(C)[C@@H]2CC[C@@]3(C2)[C@@H]1C(=O)CCC3(C)C,train
CC1(C)[C@@H](O)CC[C@]2(C)[C@H]3C(=O)C=C4[C@@H]5C[C@@](C)(C(=O)O)CC[C@]5(C)CC[C@@]4(C)[C@]3(C)CC[C@@H]12,test
Cc1ccc2c(c1)[C@H](N)[C@@H](C)C2,train
C=C[C@H]1CN2CC[C@H]1C[C@@H]2[C@@H](O)c1ccnc2ccc(OC)cc12,train
CN(C)[C@H]1CCN(c2cc(-c3ccccc3)nc3ccnn23)C1,train
CC(C)[C@@H]1CC[C@@H](C)CC1=O,train
O[C@@H](CNCc1ccccc1)[C@H]1CCc2cc(F)ccc2O1,valid
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3OC1CC(/N=c2/c(O)c(O)/c2=N\CCCCCC/N=c2\c(O)c(O)\c2=N\C2CC(O[C@H]3C[C@](O)(C(=O)CO)Cc4c(O)c5c(c(O)c43)C(=O)c3c(OC)cccc3C5=O)OC(C)C2O)C(O)C(C)O1,train
C[C@@H]1COc2ccccc2N1C(=O)C(Cl)Cl.C[C@H]1COc2ccccc2N1C(=O)C(Cl)Cl,train
CC1(C)[C@H]2[C@H](O)OC(=O)[C@H]21,train
O[C@@H]1C=C[C@H](O)[C@@]23O[C@@]12C1(Oc2cccc4cccc(c24)O1)[C@@H]1O[C@@H]1[C@@H]3O,train
COC1CC[C@]2(O1)[C@H]1C[C@H]1[C@H]1[C@@H]3C=CC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]12C,test
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@@H](C)O[C@@H](C)C1,train
CSCC[C@H](NC(=O)[C@H](CC(C)C)NC(=O)CNC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](Cc1ccccc1)NC(=O)[C@H](CCC(N)=O)NC(=O)[C@@H](CCC(N)=O)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](CCCCN)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](N)CCCN=C(N)N)C(N)=O,train
O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.[Zn+2],train
CCCCCCCC/C=C\CCCCCCCC(=O)N[C@@H]1C[C@H]1O,train
CC1(C)[C@H]2CC[C@](C)(O)[C@@H]1C2,valid
O=C(O)C[C@@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@@H](C(=O)O)[C@@H](C(=O)O)[C@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@H](C(=O)O)[C@@H](C(=O)O)[C@@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@H](C(=O)O)[C@@H](C(=O)O)[C@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@H](C(=O)O)[C@H](C(=O)O)[C@@H](C(=O)O)P(=O)(O)O,train
C1CCC(NC2CCCCC2)CC1.CC(C)(O)c1ccccc1CC[C@@H](SCC1(CC(=O)O)CC1)c1cccc(/C=C/c2ccc3ccc(Cl)cc3n2)c1,train
Cc1ccc2c(c1)[C@@H](N)[C@@H](C)C2.Cc1ccc2c(c1)[C@@H](N)[C@H](C)C2.Cc1ccc2c(c1)[C@H](N)[C@@H](C)C2.Cc1ccc2c(c1)[C@H](N)[C@H](C)C2,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O.CCCCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O.OCCN(CCO)CCO.OCCN(CCO)CCO,train
COc1ccc(C(OC[C@H]2O[C@@H](n3ccc(NC(=O)c4ccccc4)nc3=O)C[C@H]2O)(c2ccccc2)c2ccc(OC)cc2)cc1,test
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1C(=O)NCCc1ccccn1,train
CC1(C)COC2(CC[C@H]3[C@@H]4CCC5=CC6(CC[C@@H]5[C@H]4C(=O)C[C@@]32C)SCCS6)OC1,train
CC(CC(=O)C[C@@H](C)[C@H]1CC(=O)[C@@]2(C)C3=C(C(=O)[C@@H](O)[C@]12C)[C@@]1(C)CC[C@H](O)C(C)(C)[C@@H]1CC3=O)C(=O)O,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]2(C)[C@H]1C(=O)CO,train
C=C1CCC(C)=CCCC=C(C)CC[C@@H]1C(C)=O.C=C1CCC(C)=CCCC=C(C)CC[C@H]1C(C)=O.CC(=O)/C1=C(\C)CC/C(C)=C\CC/C=C(/C)CC1.CC(=O)[C@@H]1CCC(C)=CCCC=C(C)CC=C1C.CC(=O)[C@H]1CCC(C)=CCCC=C(C)CC=C1C,valid
CCCOC(=O)[C@H](C)O,train
CC1(C)CC[C@]2(C(=O)O)CC[C@]3(C)C(=CC[C@@H]4[C@@]5(C)CC[C@H](OC(=O)/C=C/c6ccc(O)cc6)C(C)(C)[C@@H]5CC[C@]43C)[C@@H]2C1,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C=CC4=CC(=O)C=C[C@]4(C)[C@H]3CC[C@@]21C,train
CC(C)=CCC/C(C)=C/CC[C@H](C)CCO,train
CCCCCCCCCCCCC/C=C/[C@@H](O)[C@H](CO)NC(=O)CCCCCCC,test
CC(C)c1ccc2c(c1)CC[C@H]1[C@](C)(CN)CCC[C@]21C.Cc1nn(-c2cccc(Cl)c2)c(O)c1N=Nc1cc(S(C)(=O)=O)ccc1O.Cc1nn(-c2cccc(Cl)c2)c(O)c1N=Nc1cc(S(C)(=O)=O)ccc1O.[Co+3].[H+],train
CO[C@]12C[C@@H](CO)CN(C)[C@@H]1Cc1c[nH]c3cccc2c13,train
CC(c1ccc([C@@H](C)c2ccccc2)cc1)c1cc(C(=O)O)c(O)c(C(C)c2ccccc2)c1.CC(c1ccc([C@H](C)c2ccccc2)cc1)c1cc(C(=O)O)c(O)c(C(C)c2ccccc2)c1.CC(c1ccccc1)c1cc(C(=O)O)c(O)c(C(C)c2ccc([C@@H](C)c3ccccc3)cc2)c1.CC(c1ccccc1)c1cc(C(=O)O)c(O)c(C(C)c2ccc([C@H](C)c3ccccc3)cc2)c1.C[C@@H](c1ccccc1)c1cc(C(=O)O)c(O)c([C@@H](C)c2ccccc2)c1.C[C@@H](c1ccccc1)c1cc(C(=O)O)c(O)c([C@@H](C)c2ccccc2)c1.C[C@@H](c1ccccc1)c1ccc(O)c(C(=O)O)c1.C[C@@H](c1ccccc1)c1cccc(C(=O)O)c1O.C[C@H](c1ccccc1)c1cc(C(=O)O)c(O)c([C@H](C)c2ccccc2)c1.C[C@H](c1ccccc1)c1cc(C(=O)O)c(O)c([C@H](C)c2ccccc2)c1.C[C@H](c1ccccc1)c1ccc(O)c(C(=O)O)c1.C[C@H](c1ccccc1)c1cccc(C(=O)O)c1O.[Zn+2].[Zn+2].[Zn+2].[Zn+2].[Zn+2].[Zn+2],train
CC[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@H]43)[C@@H]1CCC2=O,train
CC(=O)C1=C(C)[C@@H]2C[C@@]3(C1)[C@H](C)CC[C@H]3C2(C)C,valid
CC1=CC[C@@H](C)[C@H](C=O)C1.CC1=CC[C@@H](C=O)[C@H](C)C1,train
C=C(C)[C@H]1C=C[C@@](C)(O)CC1,train
CN[C@@H](C)c1cccc(O)c1,train
C=C1C=CC(C(C)CC(=O)C=C(C)C)CC1.CC(C)=CC(=O)CC(C)C1C=CC(C)=CC1.CC(C)=CC(=O)C[C@H](C)c1ccc(C)cc1,train
CCCCCCCCCCCC(=O)NC(N)=NCCC[C@H](N)C(=O)OCC.O=C1CC[C@@H](C(=O)O)N1,test
C=C[C@H]1CN2CC[C@H]1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,train
COC1=CCC2=C(CC[C@@H]3[C@@H]2CC[C@]2(C)[C@@H](O)CC[C@@H]32)C1,train
CCOC(=O)[C@H](C)O,train
CCCCOP(=S)(S)OCCCC.CN1CCC[C@H]1c1cccnc1,train
Fc1ccc2c(c1)CC[C@H](C1CO1)O2,valid
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@H]43)[C@@H]1CCC2=O,train
CCCCCCCCCCCCCC[C@@H](O)[C@@H](O)[C@H](N)CO,train
CSCC[C@H](NC(=O)[C@H](CO)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@@H](N)CO)C(=O)N[C@@H](CCC(=O)O)C(=O)N[C@@H](Cc1c[nH]cn1)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)NCC(=O)N[C@@H](CCCCN)C(=O)N1CCC[C@H]1C(=O)N[C@H](C(=O)NCC(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N1CCC[C@H]1C(=O)N[C@H](C(=O)N[C@@H](CCCCN)C(=O)N[C@H](C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](C)C(=O)NCC(=O)N[C@@H](CCC(=O)O)C(=O)O)C(C)C)C(C)C)C(C)C,train
CC1(C)[C@H]2[C@H](C(Br)(Br)Br)OC(=O)[C@H]21,train
CCC(O)CCCCCCC[C@H](O)[C@H]1CC[C@H]([C@H]2CC[C@H]([C@H](O)CCCCCCCCCCC3CC(CC(C)=O)C(=O)O3)O2)O1,test
C[C@H](N)c1ccccc1,train
C[C@@H]1[C@@H](CN)C[C@H]2C[C@@H]1C2(C)C,train
CC(C)(C)NC(=O)[C@@H]1C[C@@H]2CCCC[C@@H]2CN1,train
CCc1ccc(C(=O)O[C@H]2C[C@H]3[C@](C)(COC(C)=O)[C@@H](OC(C)=O)CC[C@]3(C)[C@H]3[C@@H](O)c4c(cc(-c5cccnc5)oc4=O)O[C@]23C)cc1,train
C[C@]12CCC3=C4CCC(=O)C=C4CC[C@H]3[C@@H]1CC[C@@H]2O,valid
C[C@]12CC[C@H]3[C@@H](C=CC4=CC(=O)CC[C@@H]43)[C@@H]1CC[C@@H]2O,train
C=C[C@H]1CN2CC[C@@H]1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,train
N[C@@H](CC[C@]1(O)CNC1=O)C(=O)O,train
C[C@]12C[C@@H](O)[C@H]3[C@@H](CCC4=CC(=O)CC[C@@H]43)[C@@H]1CCC2=O,train
COC(=O)[C@@H](C)Oc1ccc(O)cc1,test
CC(C)/C(O)=C1/C(=O)O[C@@H](c2ccoc2)[C@]2(C)C(O)[C@@H](O)[C@@]34OC5(C)OC67CC(C)(C(O)[C@]6(O)[C@@H](O)[C@]3(O5)C12)[C@H](CC(=O)O)[C@]74C,train
CC(C)OC(=O)[C@H](C)OC(=O)C(C)(C)C,train
CCOC(=O)[C@]1(C)CCC[C@@]2(C)[C@H]1CC=C1C=C(C(C)C)CC[C@@H]12,train
CCCCC[C@@H]1C(=O)CC[C@H]1C(=O)OC,train
CCOC(=O)[C@H]1[C@H](C)CCCC1(C)C,valid
CCCC(O)CC[C@@H]1[C@@H](C)CCCC1(C)C,train
CC1=CC[C@@H]2C[C@H]1C2(C)C,train
O=C1OC([C@@H](O)CO)C(=O)[C-]1O,train
CC(C)C(C)(C)[C@@H]1CCC(=O)[C@H](C)C1,train
CC(C)=CCC/C(C)=C/CC[C@H](C)CC=O,test
CCCCCCCCCCCC(=O)N[C@@H](CCCNC(=N)N)C(=O)OCC,train
CC1(C)C[C@@H]1C(=O)N/C(=C\CCCCSC[C@H](N)C(=O)O)C(=O)O.C[C@@H](O)[C@H]1C(=O)N2C(C(=O)O)=C(SCC/N=C\N)C[C@H]12,train
C[C@@H](S)CC(=O)OCC(COC(=O)C[C@@H](C)S)(COC(=O)C[C@@H](C)S)COC(=O)C[C@@H](C)S.C[C@H](S)CC(=O)OCC(COC(=O)C[C@@H](C)S)(COC(=O)C[C@@H](C)S)COC(=O)C[C@@H](C)S.C[C@H](S)CC(=O)OCC(COC(=O)C[C@H](C)S)(COC(=O)C[C@@H](C)S)COC(=O)C[C@@H](C)S.C[C@H](S)CC(=O)OCC(COC(=O)C[C@H](C)S)(COC(=O)C[C@H](C)S)COC(=O)C[C@@H](C)S.C[C@H](S)CC(=O)OCC(COC(=O)C[C@H](C)S)(COC(=O)C[C@H](C)S)COC(=O)C[C@H](C)S,train
COC(=O)[C@]1(C)CCC[C@@]2(C)[C@H]1CC=C1[C@@H]2CC[C@](O)(C(C)C)[C@H]1O,train
CCOC(=O)NC(=O)[C@@H]1C[C@H](C)CC[C@H]1C(C)C,valid
CC(C)[C@H]1CC[C@H](C)C[C@H]1O,train
C=C[C@@](C)(O)/C=C/C=C(\C)CCC=C(C)C,train
CCCCCCCCCCC/C=C/CCCCC(=O)N(C)C[C@H](C)[C@@H](C)[C@H](C)[C@H](C)CO.CCCCCCCCCCCCCCCCCC(=O)N(C)C[C@H](C)[C@@H](C)[C@H](C)[C@H](C)CO,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,train
COc1cc(Br)c2c3c1O[C@@H]1CC(=O)C=C[C@]31CCNC2,test
C[C@H]1CN(C2CCN(c3nc(N)n[nH]3)CC2)[C@@H](Cc2ccc(Cl)cc2)CO1,train
CC1=CCC2CCCCC2C1N1C(=O)[C@@H]2[C@@H]3C[C@@H]4[C@@]5(C)CCC[C@@](C)(C(=O)O)[C@@H]5CC[C@]4(C=C3C(C)C)[C@@H]2C1=O,train
C[C@@H](CN)CCCCCCN.C[C@H](CN)CCCCCCN,train
Nc1nc(=O)c2c([nH]1)NCC(CNc1ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc1)N2,train
N[C@@H](CC(=O)O)C(=O)O.N[C@@H](CC(=O)O)C(=O)O.N[C@@H](CC(=O)O)C(=O)O.[Mg+2],valid
C[C@]12CCC(O)CC1=CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,train
C=C1CC[C@H]2C[C@@H]1C2(C)C,train
C[N+](C)(C)CCCCC[C@H](N)C(=O)O,train
CNC[C@H](O)c1cccc(O)c1.[H+],train
C[C@]12CCC(=O)C=C1C=C[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@@]21CCC(=O)O1.Oc1c(Cl)c(Cl)c(O)c(Cl)c1Cl,test
COc1ccc([C@@H](C)N)cc1,train
COc1ccc([C@H](C)N)cc1,train
CC1(C)[C@H]2CC=C(CCO)[C@@H]1C2,train
CC(C)CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=NO)CC[C@]4(C)[C@H]3CC[C@]12C,train
CC1(C)C2CC[C@]1(CS(=O)(=O)O)C(=O)C2.COC(=O)[C@H](c1ccccc1Cl)N1CCc2sccc2C1,valid
CC1(C)[C@H]2CC[C@](C)(C2)[C@H]1O,train
CC1(C)COC2(CCC3=C(CC[C@@H]4[C@@H]3CC[C@@]3(C)[C@H]4CC[C@]3(O)C#N)C2)OC1,train
C[C@H](N)C(=O)N[C@H](CCC(N)=O)C(=O)O,train
CCOC(=O)[C@H]1C2CCC(CC2)[C@H]1N,train
CC(=O)O[C@H]1[C@@H](N2CCCC2)C[C@H]2[C@@H]3CC[C@H]4C[C@H](O)[C@@H](N5CCOCC5)C[C@]4(C)[C@H]3CC[C@@]21C,test
Fc1ccc2c(c1)CC[C@@H](C1CO1)O2,train
C[C@H]1OC(=O)[C@@H](C)OC1=O,train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nn[nH]n2)cc1)[C@H](C(=O)OC)C(C)C,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,train
Fc1ccc2c(c1)CC[C@H]([C@H]1CO1)O2,valid
CC(c1ncncc1F)C(Cn1cncn1)(OS(=O)(=O)C[C@@]12CCC(CC1=O)C2(C)C)c1ccc(F)cc1F,train
CC[C@]12CC[C@@H]3C4=C(CC[C@H]3[C@@H]1CC[C@@H]2O)CC(OC)=CC4,train
C=CCOC(=O)[C@@H]1[C@@H](C)CCCC1(C)C,train
CCCCCCCCCCCCCC[C@@H](O)[C@@H](O)[C@@H](N)CO,train
C[C@H]1O[C@H]1C(=O)O,test
COc1ccnc(C(=O)N[C@H]2COC(=O)[C@H](Cc3ccccc3)[C@@H](OC(=O)C(C)C)[C@H](C)OC2=O)c1O,train
COC(=O)CC(C)(C)C[C@@H](C)C(=O)OC.COC(=O)CC(C)(C)C[C@H](C)C(=O)OC.COC(=O)C[C@@H](C)CC(C)(C)C(=O)OC.COC(=O)C[C@H](C)CC(C)(C)C(=O)OC,train
CC(=O)OCC[C@@H](C)CCC=C(C)C,train
COc1cccc([C@@H](C)N)c1,train
C[C@@H]1CCC2C(C)(C)C3CC21CC[C@@]3(C)OC(=O)Cc1ccncc1,valid
CCOC(=O)[C@@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@@H]1CC2C=C[C@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@H]1C2,train
C[C@]12CCC(=O)C=C1CC[C@H]1[C@@H]3[C@@H](O)CC(=O)[C@@]3(C)CC[C@@H]12,train
CC(=O)[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,train
CC[C@]1(CC=C2CCCc3cc(OC)ccc32)C(=O)CC[C@@H]1O,train
CC(=O)O[C@]1(C)CC[C@@]23C[C@@H]1C(C)(C)[C@@H]2CC[C@H]3C,test
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@@]2(O)C#CCO,train
CC[C@H](CC#N)OC(=O)N[C@@H](C)c1cccc(NC(=O)Nc2ccc(-c3cnco3)c(OC)c2)c1,train
CCCC[C@@H](NC(=O)[C@@H](N)CC)B(O)O,train
C1=C[C@H]2C[C@@H]1[C@H]1[C@H]3CC[C@H](C3)[C@H]12,train
CC1=CC[C@H]2C[C@@H]1C2(C)C,valid
Fc1ccc2c(c1)CC[C@H]([C@@H]1CO1)O2,train
COc1cc(Br)c2c3c1O[C@@H]1CC(=O)C=C[C@]31CCN(C)C2,train
C[Si](C)(C)OC[C@H]1OC(=O)[C@H](O[Si](C)(C)C)[C@@H](O[Si](C)(C)C)[C@@H]1O[Si](C)(C)C,train
CCOC(=O)C(C[C@]1(CC)CCCN2CCc3c([nH]c4ccccc34)[C@@H]21)=NO,train
C[C@H]1C[C@@H](O)CC(C)(C)C1,test
C[C@]12C=CC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@]2(C)C(O)CC[C@@H]12,train
Nc1c(Br)cc(Br)cc1CN[C@H]1CC[C@H](O)CC1,train
Clc1ccc(I)cc1Cc1ccc(O[C@H]2CCOC2)cc1,train
COc1cccc([C@H](C)N)c1,train
COc1cc2c(cc1O)CC[C@@H]1[C@@H]2CC[C@]2(C)[C@@H](O)CC[C@@H]12,valid
CCOc1ccc(C[C@@H](CNCCN)NC(=O)OCc2ccccc2)cc1,train
O=[N+]([O-])O[C@H]1CO[C@H]2[C@@H]1OC[C@H]2O[N+](=O)[O-],train
CN[C@@H](C)c1cccc(OC)c1,train
C[C@H]1C(C)(C)C2=C(c3ncncc3CC2)C1(C)C,train
C[C@@H]1O[C@@H]1P(=O)(O)O.C[C@H](N)c1ccccc1.C[C@H](N)c1ccccc1,test
O=C[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,train
CC(C)COC(=O)[C@@H]1[C@@H](C(=O)OCC(C)C)[C@@H](C)CC[C@H]1C.CC(C)COC(=O)[C@H]1[C@H](C(=O)OCC(C)C)[C@@H](C)CC[C@H]1C,train
CC(C)c1ccc2c(c1)CCC1C(C)(CN)CCCC21C.CC(C)c1ccc2c(c1)CC[C@@H]1[C@](C)(CN)CCC[C@@]21C,train
CC(C)(C)OC(=O)N[C@@H](CO)C(=O)O,train
CC(=O)O[C@H]1CC(=O)[C@@]2(C)CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@H]12,valid
CC1(C)[C@@H]2CC[C@@]1(C)C(=O)C2,train
C=C1C[C@]2(CC)C(=O)CC[C@H]2[C@@H]2CCC3=CC(=O)CC[C@@H]3[C@@H]12,train
CC(C)C[C@H](N)c1ccccc1N1CCCCC1,train
CC1=CC[C@H]([C@@H](C)CCO)CC1.CC1=CC[C@H]([C@H](C)CCO)CC1,train
C/C(=C\c1csc(C)n1)[C@H]1OC(=O)C[C@H](O)C(C)(C)C(=O)[C@H](C)[C@@H](O)[C@@H](C)CCC/C=C\[C@@H]1O,test
CCCCCCOC(C)c1c(C)c2cc3nc(c(CC(=O)N[C@@H](CCC(=O)O)C(=O)O)c4[nH]c(cc5nc(cc1[nH]2)C(C)=C5CC)c(C)c4C(=O)O)[C@@H](CCC(=O)O)[C@@H]3C,train
N[C@@H](CSS(=O)(=O)O)C(=O)O,train
COC1(OC)CCC2=C(CC[C@@H]3[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]32)C1,train
O=C(O)[C@H]1[C@H](C(=O)NCCO)[C@H]2C=C[C@H]1C2,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C[C@H](C)C4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C.C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,valid
CCNC(=O)C(=O)O[C@@H]1C[C@H](C)CC[C@H]1C(C)C,train
CC(C)[C@H]1OCC[C@]2(C[C@H]3CC[C@]2(C)C3(C)C)O1,train
C[C@H](O)C(=O)O.C[C@H](O)C(=O)O.[Ca+2],train
CC1(C)CC[C@H]2O[C@]23C(C)(C)[C@H]2CC[C@@]13C2,train
C1=C[C@H]2C[C@@H]1[C@H]1Cc3ccccc3[C@H]12,test
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1OC(=O)OCCO,train
CCCN[C@H]1CCc2c(cccc2OC)C1.O=C(O)[C@H](O)c1ccccc1Cl,train
CN[C@@H](C)[C@@H](O)c1ccccc1,train
CC(C)[C@@H](N)C(=O)O,train
CC(O)[C@H](N)C(=O)O,valid
CN(Cc1ccccc1)C[C@@H](O)c1cccc(O)c1,train
C=C(C)C(=O)NCCC[N+](C)(C)CC(=O)NCCC[N+](C)(C)C[C@@H](O)C[N+](C)(C)C.C=C(C)C(=O)NCCC[N+](C)(C)CC(=O)NCCC[N+](C)(C)C[C@H](O)C[N+](C)(C)C,train
COC(=O)OC1C/C=C\CCCC1.COC(=O)OC1C2CCCC1CC2.COC(=O)OC1CC/C=C\CCC1.COC(=O)O[C@@H]1CC[C@@H]2CCC[C@@H]21.COC(=O)O[C@H]1CC[C@@H]2CCC[C@@H]21,train
NC[C@](O)(c1ccc(F)cc1F)C(F)(F)c1ccc(-c2ccc(OCC(F)(F)F)cc2)cn1,train
CCC(C)C(N)C1=NCC(C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](CCC(=O)O)C(=O)N[C@H](C(=O)N[C@H]2CCCCNC(=O)[C@H](CC(N)=O)NC(=O)[C@@H](CC(=O)O)NC(=O)[C@H](Cc3cnc[nH]3)NC(=O)[C@@H](Cc3ccccc3)NC(=O)[C@H]([C@@H](C)CC)NC(=O)[C@@H](CCCN)NC2=O)[C@@H](C)CC)S1.[Zn+2],test
CCCCCC[C@@H](O)C/C=C\CCCCCCCC(=O)NCCCN(C)C,train
N[C@@H]1CC2OC1c1ccccc12,train
CC1=CC[C@H](C(C)C)C=C1,train
O=C(O)C(=O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C(O)C(=O)[C@@H](O)[C@H](O)[C@H](O)CO.[Ca+2],train
Nc1nc2c(ncn2[C@@H]2O[C@H](CO)[C@@H](O)[C@H]2O)c(=O)[nH]1,valid
CNc1ncnc2c1ncn2[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,train
c1cc[n+]([C@]23C[C@H]4C[C@H](C[C@H](C4)C2)C3)cc1,train
C[C@@H](Cl)C(=O)O,train
NCCC(=O)N[C@@H](Cc1c[nH]cn1)C(=O)O,train
NC(=O)NCCC[C@H](N)C(=O)O,test
CC(=O)C1C[C@H]2C=C[C@@H]1C2,train
O=C(N[C@H](CO)[C@H](O)c1ccc([N+](=O)[O-])cc1)C(Cl)Cl,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](COP(=O)(O)OP(=O)(O)O)[C@@H](O)[C@H]1O,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)ccc(Cl)c4[C@@](C)(O)C3C[C@@H]12,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,valid
Nc1ncnc2c1ncn2[C@H]1C[C@H](O)[C@@H](COP(=O)(O)O)O1,train
C[C@@H](Cc1ccc(C(C)(C)C)cc1)CN1CCCCC1,train
O=C1[C@H]2C[C@@H]3C[C@@H](C[C@H]1C3)C2,train
N[C@@H](Cc1c[nH]cn1)C(=O)O,train
Cc1cc2nc3c(=O)[nH]c(=O)nc-3n(C[C@H](O)[C@H](O)[C@H](O)CO)c2cc1C,test
CC(=O)N[C@@H](CC(=O)O)C(=O)O,train
CNC(=O)Oc1ccc2c(c1)[C@]1(C)CCN(C)[C@@H]1N2C,train
C[C@@H](Cc1cccc(CC(=O)NCc2ccc(N(C)C(=O)CCN3CCC(OC(=O)Nc4ccccc4-c4ccccc4)CC3)cc2)c1)NC[C@H](O)c1ccc(O)c2[nH]c(=O)ccc12,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCNC(=N)N)NC1=O,train
N=C(N)Nc1ccc(CNC(=O)N2CCN(C(=O)O[C@H]3CCC[C@@H](OC(=O)N4CCN(C(=O)NCc5ccccc5)CC4)CCC3)CC2)cc1,valid
Cc1nc(/C=C(\CO)[C@@H]2C/C=C\CCC[C@H](C)[C@H](O)[C@@H](C)C(=O)C(C)(C)[C@@H](O)CC(=O)O2)cs1,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](COP(=O)(S)S)[C@@H](O)[C@H]1O,train
CC1(C)O[C@@H]2CC3C4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1.CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCNC(N)=O)NC1=O,train
CC(CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3[C@H](O)C[C@H]4C[C@@H](NCCCNCCCCNCCCN)CC[C@]4(C)[C@H]3CC[C@]12C)C(=O)O,test
CNC(=O)O[C@H]1CCC(n2cc(C(N)=O)c(Nc3ccc(F)cc3)n2)[C@@H](C#N)C1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CC(N)=O)NC1=O,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)C1CCN1C(C)(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
OC[C@@H](O)[C@@H](O)[C@H](O)[C@@H](O)C(c1c[nH]c2ccccc12)c1c[nH]c2ccccc12,train
COc1cccc2c1C(=N)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(CCO)C[C@@H]3O[C@H]1C[C@H](N)[C@H](O)[C@H](C)O1,valid
CC(C)c1cc2c(cc1S(=O)(=O)O)[C@@]1(C)CCC[C@@](C)(C(=O)O)[C@@H]1CC2,train
N[C@@H](C(=O)N1CCCC1)[C@@H](O)c1ccncc1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)C1CC1,train
CN1CCN(C(=O)Cc2ccc(Cl)c(Cl)c2)[C@H]2[C@@H]1CCC[C@@H]2N1CCCC1,train
C[C@H]1O[C@@H](c2ccccc2)OC[C@H]1NC(=O)CN,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CCC,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCN)NC1=O,train
O=C1O[C@H](O)[C@@H]2[C@H]1[C@H]1CC[C@@H]2O1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C1CCCCC1)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
CN(C(=O)c1ccc(OCC2CC2)cc1)[C@@H]1Cc2ccc(CN3CCCCCC3)cc2C1,valid
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
CC(C)/C(O)=C1/C(=O)O[C@@H](c2ccoc2)[C@]2(C)C(O)[C@@H](O)[C@@]34OC5(C)OC67CC(C)(C(O)[C@]6(O)[C@@H](O)[C@]3(O5)C12)[C@H](CC(=O)O)[C@]74C,train
N[C@@H](CCC(=O)N[C@@H](CSc1c(Br)c(Br)c2c(c1Br)C(=O)OC2(c1ccc(O)c(S(=O)(=O)O)c1)c1ccc(O)c(S(=O)(=O)O)c1)C(=O)NCC(=O)O)C(=O)O,train
CN(C)CCC[C@@]1(c2ccc(F)cc2)OCc2cc(C#N)ccc21,train
CCN1CCN(C(=O)Cc2ccc(Cl)c(Cl)c2)[C@H]2[C@@H]1CCC[C@@H]2N1CCCC1,test
O=C1N[C@@H]2C(=C[C@H](O)[C@H]3OP(=O)(O)O[C@H]32)c2cc3c(cc21)OCO3,train
CN(C)[C@]12C(=O)C(c3ccccc3)=C(c3ccccc3)[C@H]1C1CCC2C1,train
N[C@@H](CCC(=O)C[C@@H](CSSC[C@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)C(=O)NCC(=O)O)C(=O)O,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)C(NC(=O)[C@@H]1CCCCN1CC(F)F)C1CCCCC1)C1CCOCC1)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
N[C@@H](CCC(=O)O)C(=O)O,valid
CC[C@H](C)[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)CNC(=O)[C@@H](N)Cc1ccc(O)cc1)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CCCCN)C(N)=O,train
N#C[C@@H]1CCCC[C@H]1n1cc(C(N)=O)c(Nc2ccc(S(=O)(=O)C(F)F)cc2)n1,train
CC(=O)OC[C@H]1O[C@@H](NS(N)(=O)=O)C[C@@H](OC(C)=O)[C@@H]1OC(C)=O,train
Fc1c(Cl)cc([C@]2(C(F)(F)F)CC(c3ccc4c(c3)COC43CNC3)=NO2)cc1Cl.O=S(=O)(O)c1ccccc1,train
O=c1nc(NO)ccn1[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,test
Oc1ccc(O)c2c1[C@@H](O)[C@H]1O[C@H]1C21Oc2cccc3cccc(c23)O1,train
CCCCCCCCCCCC(=O)NCCCC[C@H](N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(N)=O,train
CCCCCCCCCCCC(=O)OC[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@](C)(Cc2ccccc2)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,train
O[C@@H]1CO[C@H]2[C@@H]1OC[C@@H]2O,valid
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CC(N)=O)NC1=O,train
O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)C(O)CO,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)c1cccnc1N(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCCNC(=O)CC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,test
CC(C)[C@]12CC[C@](C)(O1)C1CC[C@](C)(S(=O)(=O)O)C1[C@@H]2O,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,train
NCCCC[C@H](N)C(=O)N1CCSC1,train
CNc1ncnc2c1ncn2C1O[C@H](CO)[C@H](O[C@@H]2OC(CO)[C@H](OP(=O)(O)O)C(OP(=O)(O)O)[C@@H]2O)[C@@H]1OP(=O)(O)O,train
CC[C@]1(O)C[C@@H]2C[C@H](c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)c3[nH]c4ccccc4c3CCN(C2)C1,valid
CC[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
CCOP(=O)(O)N[C@@H](CC(C)C)C(=O)O,train
C=C(C)[C@H]1CC=C(C)CC1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC(F)F,train
Cn1c(COc2ccc(CC3SC(=O)N([C@@H]4O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]4O)C3=O)cc2)nc2ccc(O)cc21,test
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3OC1CC(/N=c2/c(O)c(O)/c2=N\CCCCCC/N=c2\c(O)c(O)\c2=N\C2CC(O[C@H]3C[C@](O)(C(=O)CO)Cc4c(O)c5c(c(O)c43)C(=O)c3c(OC)cccc3C5=O)OC(C)C2O)C(O)C(C)O1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CC[C@H](F)C1,train
C[C@H](O)CCO,train
CSCC[C@H](NC(=O)[C@H](CC(C)C)NC(=O)CNC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](Cc1ccccc1)NC(=O)[C@H](CCC(N)=O)NC(=O)[C@@H](CCC(N)=O)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](CCCCN)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](N)CCCN=C(N)N)C(N)=O,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)NC1CCC1,valid
N=C(N)NCCC[C@@H]1NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCC(N)=O)NC(=O)[C@@H](NC(=O)[C@@H](N)CCCNC(=N)N)CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC1=O,train
CC(C)(O)[C@@H]1CC[C@@H](Nc2ccc3ncc(-c4cccc(Cl)c4)n3n2)C1,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O.CCCCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O.OCCN(CCO)CCO.OCCN(CCO)CCO,train
OC[C@H]1NC[C@H](O)[C@H]1O,train
CC(CC(=O)C[C@@H](C)[C@H]1CC(=O)[C@@]2(C)C3=C(C(=O)[C@@H](O)[C@]12C)[C@@]1(C)CC[C@H](O)C(C)(C)[C@@H]1CC3=O)C(=O)O,test
CC(C)=CCC/C(C)=C/CC[C@H](C)CCO,train
CC[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)C1CC1,train
CC/C=C\C[C@H]1C(=O)C=C[C@H]1CC(=O)OCCO,train
O=C1C[C@@H](c2ccc(O)cc2)[C@]2(O)C(=O)O[C@@H]([C@H](O)CO)[C@H]2O1,train
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1O,valid
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC,train
CN1CC[C@@H](c2c(O)cc(O)c3c(=O)cc(-c4ccccc4Cl)oc23)[C@@H]1CO,train
CCCCCCCCCCCCCC[C@@H](O)[C@@H](O)[C@H](N)CO,train
Nc1ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc1,train
C=C/C=C\C(C)C(OC(N)=O)C(C)C(O)C(C)/C=C(\C)C1C(C)C(O)C(C)C1/C=C/C[C@@H]1OC(=O)[C@H](C)[C@@H](O)[C@@H]1C,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)OC,train
O=C(OC[C@H]1OC(OC(=O)c2ccc(O)c(O)c2O)[C@H](OC(=O)c2ccc(O)c(O)c2O)[C@@H](OC(=O)c2ccc(O)c(O)c2O)[C@@H]1OC(=O)c1ccc(O)c(O)c1O)c1ccc(O)c(O)c1O,train
CC(=O)O[C@@]12CO[C@@H]1C[C@H](O)[C@@]1(C)C(=O)[C@H](OC(=O)N[C@@H](C)C(=O)O)C3=C(C)[C@@H](OC(=O)[C@H](O)[C@@H](NC(=O)c4ccccc4)c4ccccc4)C[C@@](O)([C@@H](OC(=O)c4ccccc4)[C@H]21)C3(C)C,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCOCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
CCCCCCCCCCCCCCCC(=O)N[C@@H](CCC(=O)NCCCC[C@H](NC(=O)[C@H](C)NC(=O)[C@H](C)NC(=O)[C@H](CCC(N)=O)NC(=O)CNC(=O)[C@H](CCC(=O)O)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@H](CO)NC(=O)[C@H](CO)NC(=O)[C@@H](NC(=O)[C@H](CC(=O)O)NC(=O)[C@H](CO)NC(=O)[C@@H](NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](NC(=O)CNC(=O)[C@H](CCC(=O)O)NC(=O)[C@H](C)NC(=O)[C@@H](N)Cc1cnc[nH]1)[C@@H](C)O)[C@@H](C)O)C(C)C)C(=O)N[C@@H](CCC(=O)O)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@H](C(=O)N[C@@H](C)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)NCC(=O)N[C@@H](CCCNC(=N)N)C(=O)NCC(=O)O)C(C)C)[C@@H](C)CC)C(=O)O,valid
C=C1CCC[C@]2(C)[C@H]3CC4=C(O[C@]3(C)CC[C@@H]12)C(=O)C=C(OCC)C4=O,train
CCCC[C@H](N)C(=O)O,train
CC(C)[C@@H]1OCCN1CCO.CC(C)[C@@H]1OCCN1CCOC(=O)OCCN1CCO[C@H]1C(C)C.CC(C)[C@H]1OCCN1CCO.CC(C)[C@H]1OCCN1CCOC(=O)OCCN1CCO[C@H]1C(C)C.COC(=O)OCCN1CCO[C@@H]1C(C)C.COC(=O)OCCN1CCO[C@H]1C(C)C,train
CNC(=O)[C@H](Cc1ccc(OC)cc1)NC(=O)C1(CC(=O)NO)CCCCC1,train
N=C(N)NCCC[C@H](N)C(=O)O.N[C@@H](CCC(=O)O)C(=O)O,test
C[C@@]1(c2cc(NC(=O)c3cnc(NS(C)(=O)=O)cn3)ccc2F)C=CSC(N)=N1,train
O=C1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,train
N#C[C@@H]1CCCCC1n1cc(C(N)=O)c(Nc2ccc(S(=O)(=O)CC(F)(F)F)cc2)n1,train
C[C@H](c1ccccc1)c1cccc([C@@H](C)c2ccccc2)c1O,train
COc1cc(Br)c2c3c1O[C@@H]1CC(=O)C=C[C@]31CCNC2,valid
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCN1C(C)C)C1CCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
CC1=CCC2CCCCC2C1N1C(=O)[C@@H]2[C@@H]3C[C@@H]4[C@@]5(C)CCC[C@@](C)(C(=O)O)[C@@H]5CC[C@]4(C=C3C(C)C)[C@@H]2C1=O,train
CO[C@H]1/C=C\C=C(/C)C(=O)NC2=CC(=O)C(NCCCCCCNC(=O)c3ccccn3)=C(C[C@@H](C)C[C@H](OC)[C@H](O)[C@@H](C)/C=C(\C)[C@@H]1OC(N)=O)C2=O,train
C[C@H](N)C(=O)N[C@@H](C)C(=O)N[C@H]1[C@@H]2CN(c3nc4c(cc3F)c(=O)c(C(=O)O)cn4-c3ccc(F)cc3F)C[C@@H]21,train
C=CC(=O)OCCOC(=O)NCCC(C)(C)CC(C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCCC(C)(C)C[C@@H](C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCCC(C)(C)C[C@H](C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCCC(C)CC(C)(C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCC[C@@H](C)CC(C)(C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCC[C@H](C)CC(C)(C)CNC(=O)OCCOC(=O)C=C,test
C[C@]12CCC(=O)C=C1C=C[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@@]21CCC(=O)O1.Oc1c(Cl)c(Cl)c(O)c(Cl)c1Cl,train
CC1(C)[C@H]2CC[C@@]3(CCc4c(O)c(C=O)c(O)c(C=O)c4O3)[C@@H]1C2,train
CC(C)CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=NO)CC[C@]4(C)[C@H]3CC[C@]12C,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](Cc2cccc(C(N)=O)c2)NC1=O,train
CCCCC/C=C\C/C=C\C=C\C=C\[C@@H](SC[C@@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)[C@@H](O)CCCC(=O)O,valid
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)CC1(CC#N)CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,train
COc1cc(OC(F)(F)F)ccc1Cn1c([C@H]2CCCC[C@H]2C(=O)O)nc2ccc(OCc3ccn(C)n3)cc21,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CC[C@@H](F)C1,train
COC(=O)CC(C)(C)C[C@@H](C)C(=O)OC.COC(=O)CC(C)(C)C[C@H](C)C(=O)OC.COC(=O)C[C@@H](C)CC(C)(C)C(=O)OC.COC(=O)C[C@H](C)CC(C)(C)C(=O)OC,train
C[C@@]1(c2cc(NC(=O)c3cnc(-n4ccnn4)cn3)ccc2F)C=CSC(N)=N1,test
O=C(CCc1ccc(CN[C@H](C(=O)OC2CCCC2)C2CCCCC2)cn1)NO,train
CCOC(=O)[C@@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@@H]1CC2C=C[C@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@H]1C2,train
O=[N+]([O-])c1ccc(OP(=O)(O)O[C@@H]2[C@H](O)[C@H](O)[C@@H](OP(=O)(O)O)[C@H](OP(=O)(O)O)[C@H]2O)cc1,train
CC(=O)O[C@]1(C)CC[C@@]23C[C@@H]1C(C)(C)[C@@H]2CC[C@H]3C,train
Cc1ncoc1C(=O)Nc1n[nH]c2c1CN(C(=O)N1C[C@@H](C)N(C)C[C@@H]1C)C2(C)C,valid
C[C@H]1CO[C@]2(C)Oc3c(c4c(c5c3C[C@@H]3[C@@H](C)CO[C@]3(C)O5)C[C@@H]3[C@@H](C)CO[C@]3(C)O4)C[C@H]12,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCCN)NC1=O,train
Cc1ccccc1NC(=O)Nc1ccc(CC(=O)N(C)[C@@H](CC(C)C)C(=O)NCC[C@H](NC(=O)[C@@H]2CCCN2S(=O)(=O)c2cc(Cl)cc(Cl)c2)C(=O)O)cc1,train
C[N+](C)(C)CCCCC[C@H](N)C(=O)O,train
CC(C)c1ccc2c(c1)CCC1C(C)(CN)CCCC21C.CC(C)c1ccc2c(c1)CC[C@@H]1[C@](C)(CN)CCC[C@@]21C,test
CCCCCCCCO[C@H]1O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]1O[C@H]1O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]2O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]3O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]4O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]4OC(C)=O)[C@@H]3OC(C)=O)[C@@H]2OC(C)=O)[C@@H]1OC(C)=O,train
CC1=CC[C@H]([C@@H](C)CCO)CC1.CC1=CC[C@H]([C@H](C)CCO)CC1,train
C/C(=C\c1csc(C)n1)[C@H]1OC(=O)C[C@H](O)C(C)(C)C(=O)[C@H](C)[C@@H](O)[C@@H](C)CCC/C=C\[C@@H]1O,train
N[C@@H](CSS(=O)(=O)O)C(=O)O,train
C=C[C@@H]1C[C@]1(NC(=O)C1C[C@@]2(CN1C(=O)[C@@H](NC(=O)c1cccc(C(=O)NCCC)c1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,valid
O=C(O)CCC(=O)NCCCC[C@H](NC(=O)CCC(=O)O)C(=O)O,train
CC1(C)[C@H]2CC[C@]1(C)[C@H](O)C2,train
ClC[C@H]1CO1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC,train
CCCC[C@H]1CO[C@H](c2ccc(Br)c(F)c2)OC1,test
CC1=C[C@H]2O[C@@H]3C[C@H]4OC(=O)/C=C\C=C\[C@H]([C@H](C)O)OCC[C@@H](C)[C@H](O)C(=O)OC[C@@]2(CC1)[C@]4(C)[C@]31CO1,train
CCOC(=O)[C@]1(C)CCC[C@@]2(C)[C@H]1CC=C1C=C(C(C)C)CC[C@@H]12,train
O=C1CC[C@H]2C[C@]34CN5CCCC/C=C\CC[C@](O)(C=C(c6nccc7c6[nH]c6c(O)cccc67)[C@@H]3CC5)[C@@H]4N2CC/C1=C/c1ccc(Br)cc1,train
CC(C)(O)[C@H]1CC[C@H](Nc2ccc3ncc(-c4cccc(F)c4)n3n2)CC1,train
CC1(C)[C@H](/C=C(\Cl)C(F)(F)F)[C@@H]1C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,valid
CC(=O)N[C@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCC(N)=O)NC1=O,train
COc1cc(/C=C/C(=O)O[C@H]2[C@H](O[C@@H]3O[C@@H](C)[C@H](O)[C@@H](O)[C@H]3O)[C@H]3O[C@@H](c4ccc(O)c(O)c4)CO[C@@H]3O[C@@H]2CO)ccc1O,train
COC1[C@H](NC(=O)CC2OC(COCc3ccccc3)C(OCc3ccccc3)C(OCc3ccccc3)C2OCc2ccccc2)CC[C@H]2[C@H]3Cc4ccc(O)cc4[C@@]12CCN3C,train
C[C@H]1O[C@H]1C(=O)O,train
Cc1oc(-c2ccccc2)nc1CCCC[C@H]1CO[C@](C)(C(=O)O)OC1,test
OC[C@@H]1[C@@H](O)[C@H](O)C[S+]1C[C@@H](O)[C@@H](O)[C@H](O)[C@H](O)[C@@H](O)CO,train
