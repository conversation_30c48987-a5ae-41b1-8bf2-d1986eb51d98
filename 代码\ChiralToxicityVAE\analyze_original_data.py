"""
分析原始数据的分子特征分布，为改进生成算法提供依据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from rdkit import Chem
from rdkit.Chem import Descriptors, Crippen, Lipinski
import warnings
warnings.filterwarnings('ignore')

def analyze_original_molecular_features():
    """分析原始数据的分子特征分布"""
    print("=== 分析原始数据分子特征分布 ===")
    
    # 加载原始数据
    df = pd.read_csv('../../数据/Chiral_AquaTox_scr.csv')
    print(f"原始数据: {len(df)} 个分子")
    
    # 计算分子特征
    features = {
        'MW': [],           # 分子量
        'LogP': [],         # 脂水分配系数
        'TPSA': [],         # 拓扑极性表面积
        'HBD': [],          # 氢键供体
        'HBA': [],          # 氢键受体
        'RotBonds': [],     # 可旋转键
        'AromaticRings': [], # 芳香环数
        'stereo_centers': [],# 手性中心数
        'length': [],       # SMILES长度
        'carbon_count': [], # 碳原子数
        'nitrogen_count': [],# 氮原子数
        'oxygen_count': [], # 氧原子数
        'sulfur_count': [], # 硫原子数
        'halogen_count': [],# 卤素原子数
        'ring_count': [],   # 环数
        'double_bonds': [], # 双键数
        'triple_bonds': [], # 三键数
    }
    
    valid_count = 0
    for smiles in df['smiles']:
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                # RDKit计算的特征
                features['MW'].append(Descriptors.MolWt(mol))
                features['LogP'].append(Crippen.MolLogP(mol))
                features['TPSA'].append(Descriptors.TPSA(mol))
                features['HBD'].append(Lipinski.NumHDonors(mol))
                features['HBA'].append(Lipinski.NumHAcceptors(mol))
                features['RotBonds'].append(Descriptors.NumRotatableBonds(mol))
                features['AromaticRings'].append(Descriptors.NumAromaticRings(mol))
                
                # SMILES字符串特征
                features['stereo_centers'].append(smiles.count('@'))
                features['length'].append(len(smiles))
                features['carbon_count'].append(smiles.count('C'))
                features['nitrogen_count'].append(smiles.count('N'))
                features['oxygen_count'].append(smiles.count('O'))
                features['sulfur_count'].append(smiles.count('S'))
                features['halogen_count'].append(
                    smiles.count('F') + smiles.count('Cl') + 
                    smiles.count('Br') + smiles.count('I')
                )
                features['ring_count'].append(
                    smiles.count('1') + smiles.count('2') + 
                    smiles.count('3') + smiles.count('4') + 
                    smiles.count('5') + smiles.count('6')
                )
                features['double_bonds'].append(smiles.count('='))
                features['triple_bonds'].append(smiles.count('#'))
                
                valid_count += 1
        except Exception as e:
            print(f"Error processing {smiles}: {e}")
    
    print(f"成功处理: {valid_count} 个分子")
    
    # 转换为DataFrame
    features_df = pd.DataFrame(features)
    
    # 打印统计信息
    print("\n=== 原始数据特征统计 ===")
    print(features_df.describe())
    
    # 分析分布特征
    print("\n=== 分布特征分析 ===")
    for feature in features.keys():
        values = np.array(features[feature])
        print(f"{feature}:")
        print(f"  范围: {values.min():.2f} - {values.max():.2f}")
        print(f"  均值: {values.mean():.2f}")
        print(f"  标准差: {values.std():.2f}")
        print(f"  中位数: {np.median(values):.2f}")
        print(f"  25%分位数: {np.percentile(values, 25):.2f}")
        print(f"  75%分位数: {np.percentile(values, 75):.2f}")
        print()
    
    return features_df

def generate_distribution_parameters(features_df):
    """基于原始数据生成分布参数"""
    print("=== 生成分布参数 ===")
    
    distribution_params = {}
    
    for feature in features_df.columns:
        values = features_df[feature].values
        
        # 计算基本统计量
        mean_val = np.mean(values)
        std_val = np.std(values)
        min_val = np.min(values)
        max_val = np.max(values)
        median_val = np.median(values)
        
        # 根据特征类型选择合适的分布
        if feature in ['stereo_centers', 'HBD', 'HBA', 'AromaticRings', 'ring_count', 'double_bonds', 'triple_bonds']:
            # 离散特征：使用泊松分布或负二项分布
            lambda_param = max(0.1, mean_val)  # 避免lambda为0
            distribution_params[feature] = {
                'type': 'poisson',
                'lambda': lambda_param,
                'min': int(min_val),
                'max': int(max_val)
            }
        elif feature in ['MW', 'TPSA', 'RotBonds', 'length', 'carbon_count']:
            # 正偏斜分布：使用Gamma分布
            if mean_val > 0 and std_val > 0:
                # Gamma分布参数估计
                scale = std_val**2 / mean_val
                shape = mean_val / scale
                distribution_params[feature] = {
                    'type': 'gamma',
                    'shape': shape,
                    'scale': scale,
                    'min': min_val,
                    'max': max_val
                }
            else:
                distribution_params[feature] = {
                    'type': 'uniform',
                    'min': min_val,
                    'max': max_val
                }
        elif feature == 'LogP':
            # LogP可能是正态分布
            distribution_params[feature] = {
                'type': 'normal',
                'mean': mean_val,
                'std': std_val,
                'min': min_val,
                'max': max_val
            }
        else:
            # 其他特征使用均匀分布或泊松分布
            if mean_val < 10:  # 小数值特征
                lambda_param = max(0.1, mean_val)
                distribution_params[feature] = {
                    'type': 'poisson',
                    'lambda': lambda_param,
                    'min': int(min_val),
                    'max': int(max_val)
                }
            else:
                distribution_params[feature] = {
                    'type': 'uniform',
                    'min': min_val,
                    'max': max_val
                }
    
    # 打印分布参数
    for feature, params in distribution_params.items():
        print(f"{feature}: {params}")
    
    return distribution_params

def create_improved_feature_generator(distribution_params):
    """创建改进的特征生成函数"""
    
    code = '''
def generate_realistic_molecular_features(n_samples):
    """基于真实数据分布生成分子特征"""
    import numpy as np
    
    features = []
    
    for _ in range(n_samples):
        feature_values = []
        
'''
    
    # 为每个特征生成代码
    feature_order = ['stereo_centers', 'MW', 'LogP', 'TPSA', 'HBD', 'HBA', 'RotBonds', 'AromaticRings',
                    'length', 'carbon_count', 'nitrogen_count', 'oxygen_count', 'sulfur_count', 
                    'halogen_count', 'ring_count', 'double_bonds', 'triple_bonds']
    
    for feature in feature_order:
        if feature in distribution_params:
            params = distribution_params[feature]
            
            if params['type'] == 'poisson':
                code += f'''        # {feature}
        {feature}_val = np.random.poisson({params['lambda']:.3f})
        {feature}_val = np.clip({feature}_val, {params['min']}, {params['max']})
        feature_values.append({feature}_val)
        
'''
            elif params['type'] == 'gamma':
                code += f'''        # {feature}
        {feature}_val = np.random.gamma({params['shape']:.3f}, {params['scale']:.3f})
        {feature}_val = np.clip({feature}_val, {params['min']:.3f}, {params['max']:.3f})
        feature_values.append({feature}_val)
        
'''
            elif params['type'] == 'normal':
                code += f'''        # {feature}
        {feature}_val = np.random.normal({params['mean']:.3f}, {params['std']:.3f})
        {feature}_val = np.clip({feature}_val, {params['min']:.3f}, {params['max']:.3f})
        feature_values.append({feature}_val)
        
'''
            else:  # uniform
                code += f'''        # {feature}
        {feature}_val = np.random.uniform({params['min']:.3f}, {params['max']:.3f})
        feature_values.append({feature}_val)
        
'''
    
    code += '''        features.append(feature_values)
    
    return np.array(features)
'''
    
    return code

def main():
    """主函数"""
    print("分析原始数据分子特征分布")
    print("=" * 50)
    
    # 分析原始数据
    features_df = analyze_original_molecular_features()
    
    # 生成分布参数
    distribution_params = generate_distribution_parameters(features_df)
    
    # 创建改进的特征生成函数
    improved_code = create_improved_feature_generator(distribution_params)
    
    # 保存改进的代码
    with open('improved_feature_generator.py', 'w', encoding='utf-8') as f:
        f.write('"""\n基于真实数据分布的改进特征生成器\n"""\n\n')
        f.write(improved_code)
    
    print("\n" + "=" * 50)
    print("分析完成！")
    print("改进的特征生成代码已保存到: improved_feature_generator.py")
    print("\n主要发现:")
    print("1. 分子量范围:", features_df['MW'].min(), "-", features_df['MW'].max())
    print("2. LogP范围:", features_df['LogP'].min(), "-", features_df['LogP'].max())
    print("3. 手性中心数范围:", features_df['stereo_centers'].min(), "-", features_df['stereo_centers'].max())
    print("4. 碳原子数范围:", features_df['carbon_count'].min(), "-", features_df['carbon_count'].max())

if __name__ == "__main__":
    main()
