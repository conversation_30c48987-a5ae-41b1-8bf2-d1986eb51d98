"""
测试改进后的生成器
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from generate import ToxicityGenerator
from improved_visualization import ImprovedChemicalSpaceAnalyzer

def test_improved_generation():
    """测试改进后的生成器"""
    print("=== 测试改进后的生成器 ===")
    
    # 配置参数
    config = {
        'model_path': 'models/best_model.pth',
        'metadata_path': 'data/metadata.pkl',
        'reference_data_path': '../../数据/Chiral_AquaTox_scr.csv',
        
        # 改进的生成参数
        'n_samples_per_condition': 150,  # 每个条件生成更多样本
        'temperature': 1.5,              # 更高的温度增加多样性
        'generate_enantiomers': True,
        
        # 更多样化的条件
        'target_conditions': [
            [1, None, None, None, None],  # FishLC50 toxic
            [0, None, None, None, None],  # FishLC50 non-toxic
            [None, 1, None, None, None],  # FishCT toxic
            [None, 0, None, None, None],  # FishCT non-toxic
            [None, None, 1, None, None],  # DMCT toxic
            [None, None, 0, None, None],  # DMCT non-toxic
            [None, None, None, 1, None],  # DMAT toxic
            [None, None, None, 0, None],  # DMAT non-toxic
            [None, None, None, None, 1],  # AlaAT toxic
            [None, None, None, None, 0],  # AlaAT non-toxic
            [1, 1, None, None, None],     # FishLC50 + FishCT toxic
            [0, 0, None, None, None],     # FishLC50 + FishCT non-toxic
            [1, None, None, 1, None],     # FishLC50 + DMAT toxic
            [0, None, None, 0, None],     # FishLC50 + DMAT non-toxic
            [None, None, None, None, None], # Random
        ],
        
        'molecular_feature_ranges': {
            'stereo_centers': (1, 67),
            'length': (12, 661),
            'carbon_count': (2, 146),
        },
        
        'output_dir': 'improved_results'
    }
    
    # 检查模型文件是否存在
    if not os.path.exists(config['model_path']):
        print(f"错误: 模型文件不存在: {config['model_path']}")
        print("请先运行训练脚本生成模型")
        return False
    
    if not os.path.exists(config['metadata_path']):
        print(f"错误: 元数据文件不存在: {config['metadata_path']}")
        print("请先运行数据处理脚本")
        return False
    
    try:
        # 创建生成器
        print("初始化生成器...")
        generator = ToxicityGenerator(config)
        
        # 加载模型
        print("加载模型和元数据...")
        generator.load_model_and_metadata()
        
        # 生成数据
        print("开始生成数据...")
        results = generator.generate_all_conditions()
        
        # 分析结果
        print("分析生成结果...")
        analysis = generator.analyze_generated_data()
        
        # 保存结果
        print("保存结果...")
        output_dir = generator.save_results()
        
        print(f"\n=== 生成完成 ===")
        print(f"总共生成: {analysis['total_samples']} 个样本")
        print(f"有效分子: {analysis.get('valid_molecules', 'N/A')}")
        print(f"结果保存在: {output_dir}")
        
        # 运行改进的可视化分析
        print("\n=== 运行改进的可视化分析 ===")
        generated_csv_path = os.path.join(output_dir, 'generated_molecules.csv')
        
        if os.path.exists(generated_csv_path):
            analyzer = ImprovedChemicalSpaceAnalyzer(
                original_data_path='../../数据/Chiral_AquaTox_scr.csv',
                generated_data_path=generated_csv_path
            )
            
            # 加载数据
            analyzer.load_and_process_data()
            
            # 分析特征分布
            analyzer.analyze_feature_distributions()
            
            # 分析化学空间
            space_metrics = analyzer.analyze_chemical_space()
            
            # 计算多样性指标
            diversity_metrics = analyzer.calculate_diversity_metrics()
            
            print("\n=== 改进效果评估 ===")
            if space_metrics:
                print(f"PCA解释方差: {space_metrics['pca_explained_variance']:.3f}")
                print(f"原始数据数量: {space_metrics['original_count']}")
                print(f"生成数据数量: {space_metrics['generated_count']}")
            
            if diversity_metrics:
                print(f"多样性比率: {diversity_metrics['diversity_ratio']:.3f}")
                print(f"覆盖度: {diversity_metrics['coverage']:.3f}")
                
                # 评估改进效果
                if diversity_metrics['diversity_ratio'] > 0.8:
                    print("✓ 生成数据多样性良好")
                else:
                    print("⚠ 生成数据多样性需要进一步改进")
                
                if diversity_metrics['coverage'] < 0.3:
                    print("✓ 生成数据与原始数据相似度适中")
                else:
                    print("⚠ 生成数据可能过于相似原始数据")
        else:
            print(f"警告: 生成的CSV文件不存在: {generated_csv_path}")
        
        return True
        
    except Exception as e:
        print(f"生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_original():
    """与原始生成方法比较"""
    print("\n=== 与原始方法比较 ===")
    
    # 这里可以加载之前的生成结果进行比较
    original_results_path = 'results/generated_molecules.csv'
    improved_results_path = 'improved_results/generated_molecules.csv'
    
    if os.path.exists(original_results_path) and os.path.exists(improved_results_path):
        print("比较原始方法和改进方法的结果...")
        
        # 加载数据
        original_df = pd.read_csv(original_results_path)
        improved_df = pd.read_csv(improved_results_path)
        
        print(f"原始方法生成: {len(original_df)} 个分子")
        print(f"改进方法生成: {len(improved_df)} 个分子")
        
        # 可以添加更多比较指标
        
    else:
        print("缺少比较数据，跳过比较分析")

def main():
    """主函数"""
    print("开始测试改进后的生成器...")
    
    # 创建输出目录
    os.makedirs('improved_results', exist_ok=True)
    
    # 测试改进的生成器
    success = test_improved_generation()
    
    if success:
        # 与原始方法比较
        compare_with_original()
        
        print("\n=== 测试完成 ===")
        print("改进建议:")
        print("1. 如果多样性仍然不足，可以进一步增加温度参数")
        print("2. 如果生成质量下降，可以适当降低温度参数")
        print("3. 可以调整生成条件的组合以获得更好的覆盖度")
        print("4. 考虑使用更复杂的特征生成策略")
    else:
        print("\n=== 测试失败 ===")
        print("请检查模型文件和配置参数")

if __name__ == "__main__":
    main()
