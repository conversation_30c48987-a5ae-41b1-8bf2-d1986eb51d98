# Chiral Toxicity VAE

基于变分自编码器的手性分子毒性数据生成系统，支持对映异构体生成和金标准分子验证。

## 文件结构

```
ChiralToxicityVAE/
├── main.py                  # 主程序
├── model.py                 # VAE模型
├── data.py                  # 数据处理
├── train.py                 # 模型训练
├── generate.py              # 毒性生成
├── molecular_validator.py   # 分子验证
├── validate_molecules.py    # 独立验证脚本
├── visualize.py             # 数据可视化
├── requirements.txt         # 依赖包
└── README.md               # 说明文档
```

## 安装与运行

```bash
pip install -r requirements.txt
python main.py
```

## 核心功能

- **手性分子专用**: 基于SMILES提取手性特征
- **对映异构体生成**: 生成R/S构型分子对
- **条件生成**: 指定部分毒性终点，生成其他终点
- **金标准验证**: 5阶段验证确保分子合理性
- **缺失数据处理**: 掩码机制处理不完整数据
- **化学空间可视化**: 专业的分子数据可视化分析

## 配置参数

在 `main.py` 中修改 `CONFIG` 字典：

```python
CONFIG = {
    'epochs': 200,                    # 训练轮数
    'n_samples_per_condition': 100,   # 每条件生成样本数
    'generate_enantiomers': True,     # 生成对映异构体
    'target_conditions': [            # 生成条件
        [1, None, None, None, None],  # FishLC50有毒
        [0, None, None, None, None],  # FishLC50无毒
        [1, 1, 1, 1, 1],              # 全部有毒
    ]
}
```

## 输出结果

主要输出文件：
- `results/generated_toxicity_data.csv`: 生成的毒性数据
- `chemical_space_analysis/`: 化学空间分析图表
- `models/best_model.pth`: 训练好的VAE模型

CSV文件包含：
- `smiles`: 生成的手性分子SMILES结构
- `clean_smiles`: 通过金标准验证的SMILES（与smiles相同）
- `tanimoto_similarity`: 与原始数据集的最大Tanimoto相似性评分
- `enantiomer_type`: R/S构型标记
- `is_valid`: 金标准验证结果（全部为True）
- `final_score`: 分子质量评分
- `FishLC50_binary`等: 5个毒性终点预测

## 金标准验证

独立验证脚本：
```bash
python validate_molecules.py results/generated_toxicity_data.csv
```

## 化学空间可视化

独立可视化分析：
```bash
python visualize.py
```

专业化学信息学可视化：
1. **宏观层面**: t-SNE/UMAP化学空间分布对比
2. **中观层面**: 物理化学性质分布对比（小提琴图）
3. **微观层面**: 分子结构多样性与新颖性展示

输出图表：
- `chemical_space_distribution.png`: 化学空间分布
- `physicochemical_properties.png`: 物化性质对比
- `chirality_diversity.png`: 手性多样性
- `similarity_distribution.png`: 相似性分布

5阶段验证流程：
1. 基础化学有效性检查
2. 化学可行性过滤（PAINS/BRENK）
3. 合成可行性评估
4. 3D稳定性验证
5. 应用域评估

## 毒性终点

- FishLC50: 鱼类急性毒性
- FishCT: 鱼类慢性毒性
- DMCT: 大型蚤慢性毒性
- DMAT: 大型蚤急性毒性
- AlaAT: 藻类急性毒性
