# Chiral Toxicity VAE

基于变分自编码器的手性分子毒性数据生成系统，支持对映异构体生成和金标准分子验证。

## 文件结构

```
ChiralToxicityVAE/
├── main.py                  # 主程序入口
├── model.py                 # VAE模型定义
├── data.py                  # 数据处理模块
├── train.py                 # 模型训练脚本
├── generate.py              # 分子生成模块
├── molecular_validator.py   # 分子验证器
├── visualize.py             # 数据可视化分析
├── requirements.txt         # 项目依赖
├── data/                    # 数据文件目录
├── models/                  # 训练模型存储
├── results/                 # 生成结果存储
└── README.md               # 项目说明文档
```

## 安装与运行

```bash
pip install -r requirements.txt
python main.py
```

## 核心功能

- **手性分子专用**: 基于SMILES提取手性特征
- **对映异构体生成**: 生成R/S构型分子对
- **条件生成**: 指定部分毒性终点，生成其他终点
- **金标准验证**: 5阶段验证确保分子合理性
- **缺失数据处理**: 掩码机制处理不完整数据
- **化学空间可视化**: 专业的分子数据可视化分析

## 配置参数

在 `main.py` 中修改 `CONFIG` 字典：

```python
CONFIG = {
    'epochs': 200,                    # 训练轮数
    'n_samples_per_condition': 100,   # 每条件生成样本数
    'generate_enantiomers': True,     # 生成对映异构体
    'target_conditions': [            # 生成条件
        [1, None, None, None, None],  # FishLC50有毒
        [0, None, None, None, None],  # FishLC50无毒
        [1, 1, 1, 1, 1],              # 全部有毒
    ]
}
```

## 输出结果

主要输出文件：
- `results/generated_toxicity_data.csv`: 生成的毒性数据
- `chemical_space_analysis/`: 化学空间分析报告
- `models/best_model.pth`: 训练好的VAE模型

生成数据包含：
- `smiles`: 手性分子SMILES结构
- `tanimoto_similarity`: 与原始数据的相似性评分
- `enantiomer_type`: R/S构型标记
- `is_valid`: 分子验证结果
- `final_score`: 分子质量评分
- 5个毒性终点预测值

## 化学空间可视化

运行可视化分析：
```bash
python visualize.py
```

专业化学信息学可视化包括：
1. **化学空间分布**: t-SNE/UMAP降维可视化
2. **物化性质对比**: 分子描述符分布分析
3. **手性多样性**: 手性中心分布统计
4. **相似性评估**: Tanimoto相似性分布

输出图表位于 `chemical_space_analysis/` 目录：
- `chemical_space_distribution.png`: 化学空间分布
- `physicochemical_properties.png`: 物化性质对比
- `chirality_diversity.png`: 手性多样性
- `similarity_distribution.png`: 相似性分布

## 分子验证

5阶段金标准验证流程：
1. 基础化学有效性检查
2. 化学可行性过滤（PAINS/BRENK）
3. 合成可行性评估
4. 3D稳定性验证
5. 应用域评估

## 毒性终点

- FishLC50: 鱼类急性毒性
- FishCT: 鱼类慢性毒性
- DMCT: 大型蚤慢性毒性
- DMAT: 大型蚤急性毒性
- AlaAT: 藻类急性毒性
