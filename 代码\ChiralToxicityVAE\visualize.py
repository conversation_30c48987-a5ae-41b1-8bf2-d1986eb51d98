"""
化学信息学可视化模块
专业的分子数据可视化分析：化学空间分布、物化性质对比、结构多样性展示
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
# 尝试导入UMAP（可选依赖）
try:
    import umap.umap_ as umap
    UMAP_AVAILABLE = True
except ImportError:
    UMAP_AVAILABLE = False
import os
import warnings

# 抑制所有警告
warnings.filterwarnings('ignore')

# 设置图表样式
sns.set_style("whitegrid")

def setup_matplotlib_font():
    """设置matplotlib字体"""
    import warnings
    import matplotlib.font_manager as fm

    # 抑制字体警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

    # 检查可用的中文字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']

    found_chinese_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            found_chinese_font = font
            break

    if found_chinese_font:
        plt.rcParams['font.sans-serif'] = [found_chinese_font, 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    else:
        # 使用英文字体
        plt.rcParams['font.family'] = 'DejaVu Sans'
        plt.rcParams['axes.unicode_minus'] = False
        return False

# 设置字体
CHINESE_FONT_AVAILABLE = setup_matplotlib_font()

class ChemicalSpaceVisualizer:
    """化学空间可视化器"""

    def __init__(self, original_data_path, generated_data_path):
        """初始化可视化器"""
        self.original_data_path = original_data_path
        self.generated_data_path = generated_data_path

        # 检测中文字体支持
        self.use_chinese = CHINESE_FONT_AVAILABLE

        # 加载数据
        self.original_data = self._load_data(original_data_path, "原始" if self.use_chinese else "Original")
        self.generated_data = self._load_data(generated_data_path, "生成" if self.use_chinese else "Generated")

        # 分子指纹缓存
        self.original_fingerprints = None
        self.generated_fingerprints = None


        
    def _load_data(self, path, data_type):
        """加载数据"""
        try:
            df = pd.read_csv(path)
            print(f"加载{data_type}数据: {len(df)} 条记录")
            return df
        except Exception as e:
            print(f"加载{data_type}数据失败: {e}")
            return None
    
    def _calculate_molecular_fingerprints(self):
        """计算分子指纹"""
        try:
            from rdkit import Chem
            from rdkit.Chem import AllChem
            
            print("计算分子指纹...")
            
            # 计算原始数据指纹
            if self.original_data is not None:
                self.original_fingerprints = []
                for smiles in self.original_data['smiles']:
                    mol = Chem.MolFromSmiles(smiles)
                    if mol:
                        fp = AllChem.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048, useChirality=True)
                        self.original_fingerprints.append(np.array(fp))
                    else:
                        self.original_fingerprints.append(np.zeros(2048))
                self.original_fingerprints = np.array(self.original_fingerprints)
            
            # 计算生成数据指纹
            if self.generated_data is not None:
                self.generated_fingerprints = []
                for smiles in self.generated_data['smiles']:
                    mol = Chem.MolFromSmiles(smiles)
                    if mol:
                        fp = AllChem.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048, useChirality=True)
                        self.generated_fingerprints.append(np.array(fp))
                    else:
                        self.generated_fingerprints.append(np.zeros(2048))
                self.generated_fingerprints = np.array(self.generated_fingerprints)
            
            print(f"指纹计算完成: 原始{len(self.original_fingerprints)}, 生成{len(self.generated_fingerprints)}")
            return True
            
        except ImportError:
            print("Warning: RDKit未安装，无法计算分子指纹")
            return False
        except Exception as e:
            print(f"指纹计算失败: {e}")
            return False
    
    def _calculate_molecular_properties(self, smiles_list):
        """计算分子物化性质"""
        try:
            from rdkit import Chem
            from rdkit.Chem import Descriptors, Crippen, Lipinski
            
            properties = {
                'MW': [],           # 分子量
                'LogP': [],         # 脂水分配系数
                'TPSA': [],         # 拓扑极性表面积
                'HBD': [],          # 氢键供体
                'HBA': [],          # 氢键受体
                'RotBonds': [],     # 可旋转键
                'AromaticRings': [] # 芳香环数
            }
            
            for smiles in smiles_list:
                mol = Chem.MolFromSmiles(smiles)
                if mol:
                    properties['MW'].append(Descriptors.MolWt(mol))
                    properties['LogP'].append(Crippen.MolLogP(mol))
                    properties['TPSA'].append(Descriptors.TPSA(mol))
                    properties['HBD'].append(Lipinski.NumHDonors(mol))
                    properties['HBA'].append(Lipinski.NumHAcceptors(mol))
                    properties['RotBonds'].append(Descriptors.NumRotatableBonds(mol))
                    properties['AromaticRings'].append(Descriptors.NumAromaticRings(mol))
                else:
                    # 无效分子使用默认值
                    for key in properties:
                        properties[key].append(0)
            
            return properties
            
        except ImportError:
            print("Warning: RDKit未安装，无法计算分子性质")
            return None
        except Exception as e:
            print(f"性质计算失败: {e}")
            return None
    
    def create_chemical_space_analysis(self, output_dir='chemical_space_analysis'):
        """创建完整的化学空间分析"""
        os.makedirs(output_dir, exist_ok=True)
        
        print("开始化学空间分析...")
        
        # 1. 宏观层面：化学空间分布对比
        self._plot_chemical_space_distribution(output_dir)
        
        # 2. 中观层面：物理化学性质分布对比
        self._plot_physicochemical_properties(output_dir)
        
        # 3. 微观层面：分子结构多样性展示
        self._plot_molecular_diversity(output_dir)
        
        # 4. 生成分析报告
        self._generate_analysis_report(output_dir)
        
        print(f"化学空间分析完成，结果保存在: {output_dir}")
    
    def _plot_chemical_space_distribution(self, output_dir):
        """1. 宏观层面：化学空间分布对比 (t-SNE/UMAP)"""
        print("绘制化学空间分布图...")
        
        if not self._calculate_molecular_fingerprints():
            print("无法计算分子指纹，跳过化学空间分布分析")
            return
        
        # 合并指纹数据
        all_fingerprints = np.vstack([self.original_fingerprints, self.generated_fingerprints])
        
        # 创建子图
        fig, axes = plt.subplots(1, 2, figsize=(16, 7))
        
        # t-SNE降维
        print("执行t-SNE降维...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=30)
        tsne_coords = tsne.fit_transform(all_fingerprints)
        
        # 绘制t-SNE图
        original_coords = tsne_coords[:len(self.original_fingerprints)]
        generated_coords = tsne_coords[len(self.original_fingerprints):]
        
        # 设置标签（中英文）
        original_label = '原始数据' if self.use_chinese else 'Original Data'
        generated_label = '生成数据' if self.use_chinese else 'Generated Data'
        tsne_title = 't-SNE 化学空间分布' if self.use_chinese else 't-SNE Chemical Space Distribution'

        axes[0].scatter(original_coords[:, 0], original_coords[:, 1],
                       c='blue', alpha=0.6, s=20, label=original_label)
        axes[0].scatter(generated_coords[:, 0], generated_coords[:, 1],
                       c='orange', alpha=0.6, s=20, label=generated_label)
        axes[0].set_title(tsne_title)
        axes[0].set_xlabel('t-SNE 1')
        axes[0].set_ylabel('t-SNE 2')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # UMAP降维
        if UMAP_AVAILABLE:
            try:
                print("执行UMAP降维...")
                import warnings
                with warnings.catch_warnings():
                    warnings.filterwarnings('ignore')
                    umap_reducer = umap.UMAP(n_components=2, random_state=42, n_jobs=1)
                    umap_coords = umap_reducer.fit_transform(all_fingerprints)
            
                original_umap = umap_coords[:len(self.original_fingerprints)]
                generated_umap = umap_coords[len(self.original_fingerprints):]

                umap_title = 'UMAP 化学空间分布' if self.use_chinese else 'UMAP Chemical Space Distribution'

                axes[1].scatter(original_umap[:, 0], original_umap[:, 1],
                               c='blue', alpha=0.6, s=20, label=original_label)
                axes[1].scatter(generated_umap[:, 0], generated_umap[:, 1],
                               c='orange', alpha=0.6, s=20, label=generated_label)
                axes[1].set_title(umap_title)
                axes[1].set_xlabel('UMAP 1')
                axes[1].set_ylabel('UMAP 2')
                axes[1].legend()
                axes[1].grid(True, alpha=0.3)

            except Exception as e:
                print(f"UMAP降维失败: {e}")
                umap_fail_msg = 'UMAP降维失败' if self.use_chinese else 'UMAP Failed'
                axes[1].text(0.5, 0.5, umap_fail_msg, ha='center', va='center', transform=axes[1].transAxes)
        else:
            umap_unavailable_msg = 'UMAP不可用\n请安装: pip install umap-learn' if self.use_chinese else 'UMAP Unavailable\nInstall: pip install umap-learn'
            axes[1].text(0.5, 0.5, umap_unavailable_msg, ha='center', va='center', transform=axes[1].transAxes)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'chemical_space_distribution.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_physicochemical_properties(self, output_dir):
        """2. 中观层面：物理化学性质分布对比"""
        print("绘制物理化学性质分布图...")
        
        # 计算分子性质
        original_props = self._calculate_molecular_properties(self.original_data['smiles'])
        generated_props = self._calculate_molecular_properties(self.generated_data['smiles'])
        
        if original_props is None or generated_props is None:
            print("无法计算分子性质，跳过物化性质分析")
            return
        
        # 创建小提琴图
        properties = ['MW', 'LogP', 'TPSA', 'HBD', 'HBA', 'RotBonds', 'AromaticRings']

        if self.use_chinese:
            property_names = ['分子量', '脂水分配系数', '极性表面积', '氢键供体', '氢键受体', '可旋转键', '芳香环数']
            original_type = '原始'
            generated_type = '生成'
            data_type_label = '数据类型'
        else:
            property_names = ['Molecular Weight', 'LogP', 'TPSA', 'H-Bond Donors', 'H-Bond Acceptors', 'Rotatable Bonds', 'Aromatic Rings']
            original_type = 'Original'
            generated_type = 'Generated'
            data_type_label = 'Data Type'
        
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        axes = axes.flatten()
        
        for i, (prop, name) in enumerate(zip(properties, property_names)):
            if i < len(axes):
                # 准备数据
                data_for_violin = []
                labels_for_violin = []
                
                data_for_violin.extend(original_props[prop])
                labels_for_violin.extend([original_type] * len(original_props[prop]))

                data_for_violin.extend(generated_props[prop])
                labels_for_violin.extend([generated_type] * len(generated_props[prop]))
                
                # 创建DataFrame用于seaborn
                df_violin = pd.DataFrame({
                    'value': data_for_violin,
                    'type': labels_for_violin
                })
                
                # 绘制小提琴图
                sns.violinplot(data=df_violin, x='type', y='value', ax=axes[i])

                title_suffix = '分布对比' if self.use_chinese else 'Distribution Comparison'
                axes[i].set_title(f'{name} {title_suffix}')
                axes[i].set_xlabel(data_type_label)
                axes[i].set_ylabel(name)
                axes[i].grid(True, alpha=0.3)
        
        # 删除多余的子图
        if len(properties) < len(axes):
            for j in range(len(properties), len(axes)):
                fig.delaxes(axes[j])
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'physicochemical_properties.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_molecular_diversity(self, output_dir):
        """3. 微观层面：分子结构多样性展示"""
        print("分析分子结构多样性...")

        # 分析手性特征
        self._analyze_chirality_diversity(output_dir)

        # 分析分子相似性分布
        self._analyze_similarity_distribution(output_dir)

        # 创建分子结构网格图（简化版）
        self._create_molecular_grid_analysis(output_dir)

    def _analyze_chirality_diversity(self, output_dir):
        """分析手性多样性"""
        if 'chirality_count' not in self.generated_data.columns:
            return

        plt.figure(figsize=(12, 5))

        # 设置标签
        if self.use_chinese:
            chirality_xlabel = '手性中心数量'
            chirality_ylabel = '分子数量'
            chirality_title = '生成分子手性中心分布'
            enantiomer_title = '对映异构体类型分布'
        else:
            chirality_xlabel = 'Number of Chiral Centers'
            chirality_ylabel = 'Number of Molecules'
            chirality_title = 'Chiral Centers Distribution'
            enantiomer_title = 'Enantiomer Type Distribution'

        # 手性中心分布
        plt.subplot(1, 2, 1)
        chirality_dist = self.generated_data['chirality_count'].value_counts().sort_index()
        plt.bar(chirality_dist.index, chirality_dist.values, alpha=0.7, color='skyblue')
        plt.xlabel(chirality_xlabel)
        plt.ylabel(chirality_ylabel)
        plt.title(chirality_title)
        plt.grid(True, alpha=0.3)

        # 对映异构体类型分布
        plt.subplot(1, 2, 2)
        if 'enantiomer_type' in self.generated_data.columns:
            enantiomer_dist = self.generated_data['enantiomer_type'].value_counts()
            plt.pie(enantiomer_dist.values, labels=enantiomer_dist.index, autopct='%1.1f%%')
            plt.title(enantiomer_title)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'chirality_diversity.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _analyze_similarity_distribution(self, output_dir):
        """分析相似性分布"""
        if 'tanimoto_similarity' not in self.generated_data.columns:
            return

        plt.figure(figsize=(10, 6))

        similarities = self.generated_data['tanimoto_similarity'].dropna()

        # 设置标签
        if self.use_chinese:
            mean_label = f'平均值: {similarities.mean():.3f}'
            median_label = f'中位数: {similarities.median():.3f}'
            xlabel = 'Tanimoto相似性'
            ylabel = '分子数量'
            title = '生成分子与原始数据集的相似性分布'
        else:
            mean_label = f'Mean: {similarities.mean():.3f}'
            median_label = f'Median: {similarities.median():.3f}'
            xlabel = 'Tanimoto Similarity'
            ylabel = 'Number of Molecules'
            title = 'Similarity Distribution to Original Dataset'

        plt.hist(similarities, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
        plt.axvline(similarities.mean(), color='red', linestyle='--', label=mean_label)
        plt.axvline(similarities.median(), color='orange', linestyle='--', label=median_label)

        plt.xlabel(xlabel)
        plt.ylabel(ylabel)
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'similarity_distribution.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _create_molecular_grid_analysis(self, output_dir):
        """创建分子网格分析（文本版）"""
        # 选择一些代表性的生成分子
        sample_molecules = self.generated_data.sample(min(16, len(self.generated_data)))

        analysis_text = "=== 分子结构多样性分析 ===\n\n"
        analysis_text += "代表性生成分子样本:\n\n"

        for i, (_, mol) in enumerate(sample_molecules.iterrows()):
            analysis_text += f"{i+1:2d}. SMILES: {mol['smiles']}\n"
            if 'chirality_count' in mol:
                analysis_text += f"    手性中心: {mol['chirality_count']}\n"
            if 'tanimoto_similarity' in mol:
                analysis_text += f"    相似性: {mol['tanimoto_similarity']:.3f}\n"
            if 'final_score' in mol:
                analysis_text += f"    质量评分: {mol['final_score']:.3f}\n"
            analysis_text += "\n"

        with open(os.path.join(output_dir, 'molecular_diversity_analysis.txt'), 'w', encoding='utf-8') as f:
            f.write(analysis_text)

    def _generate_analysis_report(self, output_dir):
        """生成分析报告"""
        report_path = os.path.join(output_dir, 'chemical_space_analysis_report.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=== 化学空间分析报告 ===\n\n")

            # 数据概览
            f.write("1. 数据概览\n")
            f.write(f"   原始数据: {len(self.original_data)} 个分子\n")
            f.write(f"   生成数据: {len(self.generated_data)} 个分子\n\n")

            # 化学空间分布分析
            f.write("2. 化学空间分布分析\n")
            f.write("   - 使用t-SNE和UMAP降维可视化化学空间\n")
            f.write("   - 检查生成分子是否合理分布在原始数据周围\n")
            f.write("   - 评估是否存在模式坍塌或分布偏离\n\n")

            # 物化性质分析
            f.write("3. 物理化学性质分析\n")
            f.write("   - 对比7个关键分子描述符的分布\n")
            f.write("   - 使用小提琴图展示分布形状和统计特征\n")
            f.write("   - 评估生成分子的理化性质合理性\n\n")

            # 结构多样性分析
            f.write("4. 结构多样性分析\n")
            if 'chirality_count' in self.generated_data.columns:
                avg_chirality = self.generated_data['chirality_count'].mean()
                f.write(f"   - 平均手性中心数: {avg_chirality:.2f}\n")

            if 'tanimoto_similarity' in self.generated_data.columns:
                avg_similarity = self.generated_data['tanimoto_similarity'].mean()
                f.write(f"   - 平均Tanimoto相似性: {avg_similarity:.3f}\n")

            if 'final_score' in self.generated_data.columns:
                avg_quality = self.generated_data['final_score'].mean()
                f.write(f"   - 平均质量评分: {avg_quality:.3f}\n")

            f.write("\n5. 结论与建议\n")
            f.write("   请查看生成的可视化图表进行详细分析:\n")
            f.write("   - chemical_space_distribution.png: 化学空间分布\n")
            f.write("   - physicochemical_properties.png: 物化性质对比\n")
            f.write("   - chirality_diversity.png: 手性多样性\n")
            f.write("   - similarity_distribution.png: 相似性分布\n")

        print(f"分析报告已保存: {report_path}")

def main():
    """主函数"""
    # 数据路径
    original_data_path = '../../数据/Chiral_AquaTox_scr.csv'
    generated_data_path = 'results/generated_toxicity_data.csv'

    # 检查文件是否存在
    if not os.path.exists(generated_data_path):
        print(f"生成数据文件不存在: {generated_data_path}")
        print("请先运行 python main.py 生成数据")
        return

    # 创建可视化器
    visualizer = ChemicalSpaceVisualizer(original_data_path, generated_data_path)

    # 执行化学空间分析
    visualizer.create_chemical_space_analysis()

    print("化学空间分析完成！")

if __name__ == "__main__":
    main()
