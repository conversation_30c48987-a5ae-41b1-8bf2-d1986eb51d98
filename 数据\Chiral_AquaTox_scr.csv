﻿smiles,FishLC50,FishCT,DMCT,DMAT,AlaAT,group
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,1,,,,,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@@]21C,1,,,,,train
CN(C)[C@@H]1C(=O)C(C(N)=O)=C(O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,0,,,,,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O,0,,,,,train
NC[C@@H]1O[C@H](O[C@H]2[C@@H](O)[C@H](O[C@@H]3[C@@H](O)[C@H](N)C[C@H](N)[C@H]3O[C@H]3O[C@H](CN)[C@@H](O)[C@H](O)[C@H]3N)O[C@@H]2CO)[C@H](N)[C@@H](O)[C@@H]1O,1,,,,,valid
COc1ccc([C@@H]2Sc3ccccc3N(CCN(C)C)C(=O)[C@@H]2OC(C)=O)cc1,1,,,,,train
CN[C@H]1CC[C@@H](c2ccc(Cl)c(Cl)c2)c2ccccc21,1,,,,,train
CCC(C)(C)C(=O)O[C@H]1C[C@@H](C)C=C2C=C[C@H](C)[C@H](CC[C@@H]3C[C@@H](O)CC(=O)O3)[C@H]21,1,,,,,train
C=C1C(=O)N[C@H](C)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](C(=O)O)[C@H](C)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](/C=C/C(C)=C/[C@H](C)[C@H](Cc2ccccc2)OC)[C@H](C)C(=O)N[C@@H](C(=O)O)CCC(=O)N1C,1,,,,,train
C=C1C(=O)N[C@H](C)C(=O)N[C@@H](Cc2ccc(O)cc2)C(=O)N[C@@H](C(=O)O)[C@H](C)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](/C=C/C(C)=C/[C@H](C)[C@H](Cc2ccccc2)OC)[C@H](C)C(=O)N[C@@H](C(=O)O)CCC(=O)N1C,1,,,,,test
C=C1C(=O)N[C@H](C)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](C(=O)O)[C@H](C)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](/C=C/C(C)=C/[C@H](C)[C@H](Cc2ccccc2)OC)[C@H](C)C(=O)N[C@@H](C(=O)O)CCC(=O)N1C,1,,,,,train
C[C@@H]1CO[C@](Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,1,,,,,train
C[C@H]1CO[C@@](Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,1,,,,,train
C[C@@H]1CO[C@@](Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,1,,,,,train
C[C@H]1CO[C@](Cn2cncn2)(c2ccc(Oc3ccc(Cl)cc3)cc2Cl)O1,1,,,,,valid
CN1CCC[C@H]1c1cccnc1,1,,,,,train
O=C(N[C@H](CO)[C@H](O)c1ccc([N+](=O)[O-])cc1)C(Cl)Cl,0,,,,,train
O=C1C[C@@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@H]3C[C@H]46,1,,,,,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)ccc(Cl)c4[C@@](C)(O)[C@H]3C[C@@H]12,1,,,,,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H]1Cl,1,,,,,test
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H]4C[C@@H]([C@H]5O[C@@H]45)[C@@H]3[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,1,,,,,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,0,,,,,train
C[C@@H]1C[C@@H]([C@H](O)CC2CC(=O)NC(=O)C2)C(=O)[C@@H](C)C1,1,,,,,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@H]4C[C@H]([C@@H]5O[C@H]45)[C@@H]3[C@@]1(Cl)C2(Cl)Cl,1,,,,,valid
C[C@H](O)C(=O)O,0,,,,,train
C=C(C)[C@H]1Cc2c(ccc3c2O[C@@H]2COc4cc(OC)c(OC)cc4[C@@H]2C3=O)O1,1,,,,,train
O[C@H]1[C@H](O)[C@H](O)OC[C@H]1O,0,,,,,train
CCOC(=O)[C@H](O)[C@@H](O)C(=O)OCC,0,,,,,train
CC1(C)[C@@H]2CC[C@@]1(C)[C@H](OC(=O)CSC#N)C2,1,,,,,test
C=C(C)[C@@H]1[C@H]2OC(=O)[C@@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C.CC(C)(O)C1C2OC(=O)C1C1(O)CC3OC34C(=O)OC2C14C,1,,,,,train
C=C[C@@]1(C)C=C2CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1,1,,,,,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@@H]4C=C[C@H]3C4)[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H](Cl)[C@H]1Cl,1,,,,,train
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@@H](Cl)[C@@H]1Cl,1,,,,,valid
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@@H](Cl)[C@H]1Cl,1,,,,,train
COc1cc2c(cc1OC)[C@@]13CCN4CC5=CCO[C@H]6CC(=O)N2[C@H]1[C@H]6[C@H]5C[C@H]43,1,,,,,train
CC1(C)[C@H]2CC[C@]1(C)[C@H](O)C2,1,,,,,train
CC1(C)[C@H]2CC[C@]1(C)C(=O)C2,1,,,,,train
CC1(C)[C@@H]2CC[C@@]1(C)C(=O)C2,0,,,,,test
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@H]4C=C[C@H]3C4)[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
C=C[C@]1(C)C=C2CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1,1,,,,,train
CN1CCc2cc3c(cc2[C@H]1[C@@H]1OC(=O)c2c1ccc1c2OCO1)OCO3,1,,,,,train
O[C@@H]1C[C@H]2CC[C@@H]1C2,0,,,,,train
CC(C)C1=CC2=CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1,1,,,,,valid
CC1=C2C(=CO[C@@H](C)[C@@H]2C)C(O)=C(C(=O)O)C1=O,1,,,,,train
C=C[C@@](C)(O)CC[C@H]1C(=C)CC[C@H]2C(C)(C)CCC[C@]12C,1,,,,,train
C=C[C@H]1C[N@@]2CC[C@H]1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,1,,,,,train
Cl[C@@H]1CCCC[C@H]1Cl,1,,,,,train
CNC(=O)ON=C1[C@@H](Cl)[C@@H]2C[C@H](C#N)[C@H]1C2,1,,,,,test
O=S1OC[C@@H]2[C@H](CO1)[C@]1(Cl)C(Cl)=C(Cl)[C@@]2(Cl)C1(Cl)Cl,1,,,,,train
COc1cc2c(c3oc(=O)c4c(c13)CCC4=O)[C@@H]1C=CO[C@@H]1O2,1,,,,,train
NC[C@@H]1O[C@H](O[C@H]2[C@@H](O)[C@H](O[C@@H]3[C@@H](O)[C@H](N)C[C@H](N)[C@H]3O[C@H]3O[C@H](CN)[C@@H](O)[C@H](O)[C@H]3N)O[C@@H]2CO)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@H]4O[C@H](CN)[C@@H](O)[C@H](O)[C@H]4N)[C@H]3O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O,1,,,,,train
C=C[C@@]1(C)C=C2CC[C@H]3[C@](C)(CO)CCC[C@]3(C)[C@H]2CC1,1,,,,,train
CN[C@@H]1[C@H](O)[C@H](NC)[C@H]2O[C@@]3(O)C(=O)C[C@@H](C)O[C@H]3O[C@@H]2[C@H]1O,0,,,,,valid
CC(C)c1ccc2c(c1)CC[C@H]1[C@](C)(C(=O)O)CCC[C@]21C,1,,,,,train
C[C@@H]1CCCC[C@@H]1C,1,,,,,train
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1O,1,,,,,train
O[C@@H]1CCCC[C@H]1c1ccccc1,1,,,,,train
O=C1[C@H]2CC=CC[C@H]2C(=O)N1SC(Cl)(Cl)C(Cl)Cl,1,,,,,test
CC(C)c1ccc2c(c1)CC[C@H]1[C@](C)(CO)CCC[C@]21C,1,,,,,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
C=C[C@@]1(C)CC[C@H]2C(=CC[C@@H]3[C@]2(C)CCC[C@@]3(C)C(=O)O)C1,1,,,,,train
C=C(C)[C@H]1CC=C(C)CC1,1,,,,,valid
CC1=CC[C@@H]2C[C@H]1C2(C)C,1,,,,,train
CC1(C(Cl)Cl)[C@H]2[C@@H](Cl)[C@H](Cl)[C@]1(C(Cl)Cl)[C@@H](Cl)[C@@H]2Cl.ClCC1(C(Cl)Cl)[C@@H]2C[C@H](Cl)[C@@]1(C(Cl)Cl)CC2(Cl)Cl.ClCC1(C(Cl)Cl)[C@@H]2[C@@H](Cl)[C@H](Cl)[C@@]1(C(Cl)Cl)C[C@@H]2Cl.ClCC1(C(Cl)Cl)[C@H]2CC(Cl)(Cl)[C@]1(C(Cl)Cl)CC2(Cl)Cl.ClCC1(CCl)[C@H]2[C@@H](Cl)[C@H](Cl)[C@]1(C(Cl)Cl)[C@@H](Cl)[C@@H]2Cl.ClC[C@@]1(C(Cl)Cl)C2[C@@H](Cl)[C@H](Cl)C1(C(Cl)Cl)[C@@H](Cl)[C@@H]2Cl,1,,,,,train
C/C=C\CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](/C=C(\C)C(=O)OC)C2(C)C)CC1=O.C/C=C\CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O.C=C/C=C\CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](/C=C(\C)C(=O)OC)C2(C)C)CC1=O.C=C/C=C\CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,1,,,,,train
C[C@H]1CC[C@H]2C[C@@H]1C2(C)C,1,,,,,train
CC1(C)[C@@H]2CC[C@@]1(C)C(=O)[C@H]2Br,1,,,,,test
C/C=C(\C)C(=O)O[C@H]1C[C@@H](OC(C)=O)[C@@]2(C(=O)OC)CO[C@H]3[C@@H](O)[C@@](C)([C@]45O[C@@]4(C)[C@H]4C[C@@H]5O[C@@H]5OC=C[C@@]54O)[C@H]4[C@]1(CO[C@]4(O)C(=O)OC)[C@@H]32,1,,,,,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)C=C[C@@H]3[C@@]1(Cl)C2(Cl)Cl.ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl.ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl.ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@H](Cl)[C@@H](Cl)[C@@H]3[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
Cl[C@@H]1C2(Cl)[C@H]3C4C=CC3[C@H]3[C@@H]4[C@]1(Cl)C(Cl)(Cl)[C@]32Cl,1,,,,,train
CNC(=O)O/N=C1\[C@@H](Cl)[C@@H]2C[C@H](C#N)[C@H]1C2,1,,,,,train
COC(=O)C1=CC[C@H]([C@H](C)CC(=O)C=C(C)C)CC1,1,,,,,valid
COC(=O)C1=CC[C@H]([C@H](C)CC(=O)CC(C)C)CC1,1,,,,,train
C=C1CC[C@H]2C[C@@H]1C2(C)C,1,,,,,train
CN(C)C(=O)N[C@@H]1C[C@@H]2C[C@H]1[C@@H]1CCC[C@H]21,1,,,,,train
CC1(C)[C@H](C=C2CCCC2)[C@H]1C(=O)OCc1coc(Cc2ccccc2)c1,1,,,,,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@H](Cl)[C@]4(Cl)O[C@H]34)[C@@]1(Cl)C2(Cl)Cl,1,,,,,test
CC(C)=C[C@@H]1[C@@H](C(=O)OCc2coc(Cc3ccccc3)c2)C1(C)C,1,,,,,train
O=S1OC[C@@H]2[C@H](CO1)[C@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,1,,,,,train
CCOC(=O)[C@H](C)N(C(=O)c1ccccc1)c1ccc(Cl)c(Cl)c1,1,,,,,train
C[C@H](NC(=O)[C@@H](C)NC(=O)[C@@H](N)CCP(C)(=O)O)C(=O)O,1,,,,,train
CC[C@H]1O[C@]2(CC[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)C[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,1,,,,,valid
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1C=C(Br)Br,1,,,,,train
CC1(C)[C@H](C(=O)OCc2cccc(Oc3ccccc3)c2)[C@@H]1C=C(Cl)Cl,1,,,,,train
CC1(C)[C@H](C=C(Cl)Cl)[C@H]1C(=O)OCc1cccc(Oc2ccccc2)c1,1,,,,,train
CC(C)[C@H](C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,1,,,,,train
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1C(Br)C(Br)(Br)Br,1,,,,,test
ClC[C@@]1(C(Cl)Cl)C2[C@@H](Cl)[C@H](Cl)C1(C(Cl)Cl)[C@@H](Cl)[C@@H]2Cl,1,,,,,train
CC1(C)[C@H](C=C(Cl)Cl)[C@@H]1C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,1,,,,,train
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@@H](C)O[C@@H](C)C1,1,,,,,train
CC(C)[C@H](C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1)c1ccc(OC(F)F)cc1,1,,,,,train
COCC(=O)N(c1c(C)cccc1C)[C@H](C)C(=O)OC,0,,,,,valid
CCOC(=O)[C@@H](C)Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1,1,,,,,train
C#CCN1CC(=O)N(COC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)C1=O,1,,,,,train
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1/C=C(\Cl)C(F)(F)F,1,,,,,train
CC(C)(C)[C@@H](O)[C@@H](Cc1ccc(Cl)cc1)n1cncn1,1,,,,,train
C[C@H]1[C@H](c2ccc(Cl)cc2)SC(=O)N1C(=O)NC1CCCCC1,1,,,,,test
Cc1c(COC(=O)[C@@H]2[C@H](/C=C(\Cl)C(F)(F)F)C2(C)C)cccc1-c1ccccc1,1,,,,,train
CCc1cccc(C)c1N(C(=O)CCl)[C@@H](C)COC,1,,,,,train
CCOC(=O)[C@@H](C)Oc1ccc(Oc2cnc3cc(Cl)ccc3n2)cc1,1,,,,,train
CC(C)[C@@H](Nc1ccc(C(F)(F)F)cc1Cl)C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1,1,,,,,train
C#CCOC(=O)[C@@H](C)Oc1ccc(Oc2ncc(Cl)cc2F)cc1,1,,,,,valid
CCC(C)C(=O)O[C@H]1C[C@@H](OC(C)=O)[C@@]2(C(=O)OC)CO[C@H]3[C@@H](O)[C@@](C)([C@]45O[C@@]4(C)[C@H]4CC5O[C@@H]5OCC[C@@]54O)[C@H]4[C@]1(CO[C@]4(O)C(=O)OC)[C@@H]32,1,,,,,train
C[C@@H](Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1)C(=O)O,1,,,,,train
CCCCOC(=O)[C@@H](C)Oc1ccc(Oc2ccc(C#N)cc2F)cc1,1,,,,,train
CSC1=N[C@@](C)(c2ccccc2)C(=O)N1Nc1ccccc1,1,,,,,train
COC[C@H](C)N(C(=O)CCl)c1c(C)csc1C,1,,,,,test
COC(=O)N(C(=O)N1CO[C@@]2(C(=O)OC)Cc3cc(Cl)ccc3C2=N1)c1ccc(OC(F)(F)F)cc1,1,,,,,train
CCOC(=O)OC1=C(c2cc(C)ccc2C)C(=O)N[C@]12CC[C@@H](OC)CC2,1,,,,,train
C/C=C\[C@@H]1[C@@H](C(=O)OCc2c(F)c(F)c(COC)c(F)c2F)C1(C)C,1,,,,,train
N#Cc1nn(-c2c(Cl)cc(C(F)(F)F)cc2Cl)c(N)c1[S@](=O)C(F)(F)F,1,,,,,train
N#Cc1nn(-c2c(Cl)cc(C(F)(F)F)cc2Cl)c(N)c1[S@@](=O)C(F)(F)F,1,,,,,valid
Cc1ccc2c(c1)[C@H](Nc1nc(N)nc(C(C)F)n1)[C@@H](C)C2,1,,,,,train
CC(C)=C1C=C2CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1,1,,,,,train
C=C1[C@H]2CC[C@@H]3[C@H]2C(C)(C)CCC[C@]13C,1,,,,,train
CC(C)C1=CC2=CC[C@H]3[C@](C)(CO)CCC[C@]3(C)[C@H]2CC1,1,,,,,train
O=C(OC[C@H]1O[C@@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@@H](OC(=O)c2cc(O)c(O)c(OC(=O)c3cc(O)c(O)c(O)c3)c2)[C@@H]1OC(=O)c1cc(O)c(O)c(OC(=O)c2cc(O)c(O)c(O)c2)c1)c1cc(O)c(O)c(OC(=O)c2cc(O)c(O)c(O)c2)c1,1,,,,,test
CC(C)C1=CC2=C(CC1)[C@@]1(C)CCC[C@@](C)(C(=O)O)[C@@H]1CC2,1,,,,,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O.O=[P](=O)(O)O,0,,,,,train
CCCCCCCCCC[C@H]1O[C@H]1CCCCC(C)C,1,,,,,train
COC(=O)[C@@]1(Cl)CCC[C@]2(C)c3ccc(C(C)C)cc3CC[C@@H]12,1,,,,,train
COC(=O)[C@@]1(Cl)C(Cl)CC[C@]2(C)c3ccc(C(C)C)cc3CC[C@@H]12,1,,,,,valid
CC(C)c1c(Cl)cc2c(c1Cl)CC[C@H]1[C@](C)(C(=O)O)CCC[C@]21C,1,,,,,train
CC(C)[C@H](C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,1,,,,,train
CC(C)[C@@H](C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,1,,,,,train
CC(C)[C@@H](C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1)c1ccc(Cl)cc1,1,,,,,train
CC[C@H](C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H](O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.CO[C@H]1C[C@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3/C(C)=C/C[C@@H]4C[C@@H](C[C@]5(C=C[C@H](C)[C@@H](C(C)C)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OC/C(=C\C=C\[C@@H]3C)[C@]54O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,1,,,,,test
CC[C@H](C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@H](NC)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.CN[C@@H]1[C@H](C)O[C@@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3/C(C)=C/C[C@@H]4C[C@@H](C[C@]5(C=C[C@H](C)[C@@H](C(C)C)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OC/C(=C\C=C\[C@@H]3C)[C@]54O)C[C@@H]2OC)C[C@@H]1OC,1,,,,,train
CCO[C@@H]1[C@@H](OC)[C@H](C)O[C@@H](O[C@@H]2C[C@H]3CC[C@@H]4[C@@H](C=C5C(=O)[C@H](C)[C@@H](O[C@H]6CC[C@H](N(C)C)[C@@H](C)O6)CCC[C@H](CC)OC(=O)C[C@H]54)[C@@H]3C2)[C@@H]1OC,1,,,,,train
C=C(C)[C@H]1Cc2c(ccc3c2OC2COc4cc(OC)c(OC)cc4C2C3=O)O1,1,,,,,train
C=CCC1=C(C)C(OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,1,,,,,train
CC(C)c1ccc2c(c1)CCC1[C@](C)(C(=O)O)CCC[C@]21C,1,,,,,valid
CC(C)C1=CC2=CCC3[C@](C)(CCC[C@@]3(C)C(=O)O)C2CC1,1,,,,,train
CC(C)=C1C=C2CCC3[C@](C)(CCC[C@@]3(C)C(=O)O)C2CC1,1,,,,,train
C=C[C@@]1(C)CCC2C(=CCC3[C@]2(C)CCC[C@@]3(C)C(=O)O)C1,1,,,,,train
CC1=CC[C@H]2C[C@H]1C2(C)C,1,,,,,train
ClC1CCCC[C@H]1Cl,1,,,,,test
O=c1nc(NO)ccn1[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,1,,,,,train
Oc1ccc(O)c2c1[C@@H](O)[C@H]1O[C@H]1C21Oc2cccc3cccc(c23)O1,1,,,,,train
CC1(C)O[C@]2(C)CC[C@H]1CC2,1,,,,,train
CCOP(=O)(O)N[C@@H](CC(C)C)C(=O)O,1,,,,,train
CC(=O)OP(=O)(O)N(C[C@H]1CN(c2cc(F)c(N3C=CC(=O)CC3)c(F)c2F)C(=O)O1)c1ccon1,1,,,,,valid
O[C@@H]1C=C[C@H](O)[C@@]23O[C@@]12C1(Oc2cccc4cccc(c24)O1)[C@@H]1O[C@@H]1[C@@H]3O,1,,,,,train
N[C@@H](C(=O)N1CCCC1)[C@@H](O)c1ccncc1,1,,,,,train
CCCCCCCC/C=C\CCCCCCCC(=O)N[C@@H]1C[C@H]1O,1,,,,,train
CC[C@]1(O)C[C@@H]2C[C@H](c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)c3[nH]c4ccccc4c3CCN(C2)C1,1,,,,,train
OC[C@H]1NC[C@H](O)[C@H]1O,1,,,,,test
Nc1ncnc2c1ncn2[C@H]1CC[C@@H](COP(=O)(O)OP(=O)(O)OP(=O)(O)O)O1,0,,,,,train
CC1(C)CC[C@]2(C(=O)O)CC[C@]3(C)C(=CC[C@@H]4[C@@]5(C)CC[C@H](OC(=O)/C=C/c6ccc(O)cc6)C(C)(C)[C@@H]5CC[C@]43C)[C@@H]2C1,1,,,,,train
CC1(C)O[C@@H]2CC3C4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1.CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,1,,,,,train
CC(C)CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=NO)CC[C@]4(C)[C@H]3CC[C@]12C,1,,,,,train
C[C@@H]1CCC2C(C)(C)C3CC21CC[C@@]3(C)OC(=O)Cc1ccncc1,1,,,,,valid
CCCC[C@@H](O)[C@@H](N)CC.CCCC[C@@H](O)[C@H](N)CC.CCCC[C@H](O)[C@@H](N)CC.CCCC[C@H](O)[C@H](N)CC,1,,,,,train
CC1=CC[C@H]2C[C@@H]1C2(C)C,1,,,,,train
COc1cc(Br)c2c3c1O[C@@H]1CC(=O)C=C[C@]31CCN(C)C2,1,,,,,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,1,,,,,train
C[C@H]1C(C)(C)C2=C(c3ncncc3CC2)C1(C)C,1,,,,,test
CC(=O)OCC1O[C@@H](OC2[C@H](OC(C)CCCCCC/C=C\CCCCCCC(=O)O)OC(COC(C)=O)[C@@H](O)[C@@H]2O)C(O)[C@@H](O)[C@@H]1O.CC(=O)OCC1O[C@H]2OC(C)CCCCCC/C=C\CCCCCCC(=O)O[C@@H]3C(COC(C)=O)O[C@@H](OC2[C@@H](O)[C@@H]1O)C(O)[C@H]3O,1,,,,,train
C/C(=C\c1csc(C)n1)[C@H]1OC(=O)C[C@H](O)C(C)(C)C(=O)[C@H](C)[C@@H](O)[C@@H](C)CCC/C=C\[C@@H]1O,1,,,,,train
N[C@@H](CSS(=O)(=O)O)C(=O)O,1,,,,,train
COC(=O)OC1C/C=C\CCCC1.COC(=O)OC1C2CCCC1CC2.COC(=O)OC1CC/C=C\CCC1.COC(=O)O[C@@H]1CC[C@@H]2CCC[C@@H]21.COC(=O)O[C@H]1CC[C@@H]2CCC[C@@H]21,1,,,,,train
CC(C)(C)OC(=O)N[C@@H](CO)c1ccccc1,1,,,,,valid
COC1=C[C@@H]2[C@@H]3Cc4ccc(OC)c(OCCCCCCOc5c(OC)ccc6c5[C@@]57CCN(C)[C@@H](C6)[C@H]5C=C(OC)C(=O)C7)c4[C@]2(CCN3C)CC1=O,1,,,,,train
CC1(C)[C@H](C(=O)OC(C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1/C=C(\Cl)C(F)(F)F,1,,,,,train
CCCCCCCCCCCCCCCCCC(=O)N1C[C@H](OS(=O)(=O)O)C[C@H]1C(=O)NCCCCCCCCCCCCCC,1,,,,,train
CCCCCC[C@H]1C(=O)O[C@H](C)[C@H](NC(=O)c2cccc(NC=O)c2O)C(=O)O[C@@H](C)[C@@H]1OC(=O)CC(C)C,1,,,,,train
O=C1O[C@H]([C@@H](O)CO)C(O)=C1O,1,,,,,test
ClC1=C(Cl)[C@]2(Cl)[C@H]3[C@H]([C@H]4C=C[C@H]3C4)[C@]1(Cl)C2(Cl)Cl,1,,,,,train
C=C(C)[C@@H]1[C@@H]2OC(=O)[C@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C.CC(C)(O)[C@@H]1[C@@H]2OC(=O)[C@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C,1,,,,,train
CCC(C)[C@H]1O[C@]2(CC[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H](O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.CO[C@H]1C[C@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3/C(C)=C/C[C@@H]4C[C@@H](C[C@]5(CC[C@H](C)[C@@H](C(C)C)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OC/C(=C\C=C\[C@@H]3C)[C@]54O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,1,,,,,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC(F)F,1,,,,,train
O=C(O)[C@@H]1[C@H](C(=O)O)[C@@H]2C=C[C@H]1O2,1,,,,,valid
Ic1cncc([C@H]2C[C@H]3CC[C@H]2N3)c1,1,,,,,train
CC(C)(O)CC[C@@H](O)[C@](C)(O)[C@H]1CC[C@@]2(O)C3=CC(=O)[C@@H]4C[C@@H](O)[C@@H](O)C[C@]4(C)[C@H]3CC[C@]12C,0,,,,,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@@H](Cc2c[nH]cn2)NC1=O,1,,,,,train
C=C[C@H]1CN2CCC1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,1,,,,,train
CN(C)[C@@H]1C(=O)/C(=C(\N)O)C(=O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12.[H+],0,,,,,test
CC1(C)[C@@H](C=C(Cl)Cl)[C@@H]1C(=O)OCc1cccc(Oc2ccccc2)c1,1,,,,,train
CN(C)[C@@H]1C(=O)/C(=C(\N)O)C(=O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)C3C[C@@H]12.[H+],0,,,,,train
NCCCCNCC(=O)NCCCNCCCNCCCCNC(=O)[C@H](CC(N)=O)NC(=O)c1cc2ccc(O)cc2oc1=O,1,,,,,train
CC1(C)[C@H](C=C(Cl)Cl)[C@H]1C(=O)O[C@H](C#N)c1ccc(F)c(Oc2ccccc2)c1,1,,,,,train
COC(=O)N[C@H]1[C@H](O[C@H]2[C@H](O)[C@@H](N)[C@H](O[C@H]3[C@H](O)[C@@H](N)[C@H](O)O[C@@H]3CO)O[C@@H]2CO)O[C@H](CO)[C@@H](O[C@@H]2O[C@H](CO)[C@@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@@H]4O[C@H](CO)[C@@H](O[C@@H]5O[C@H](CO)[C@@H](O[C@@H]6O[C@H](CO)[C@@H](O[C@@H]7O[C@H](CO)[C@@H](O)[C@H](O)[C@H]7N)[C@H](O)[C@H]6N)[C@H](O)[C@H]5N)[C@H](O)[C@H]4N)[C@H](O)[C@H]3N)[C@H](O)[C@H]2N)[C@@H]1O,1,,,,,valid
CC#CCn1c(N2CCC[C@@H](N)C2)nc2c1c(=O)n(Cc1nc3ccccc3s1)c(=O)n2C,1,,,,,train
CCCCCCCCCCc1ccc2oc(NC[C@H]3CCCN3)nc2c1,0,,,,,train
O=C(O)c1cccc(/C=C2/C[C@@H]3[C@@H](/C=C/[C@H](O)C4CCCCC4)[C@H](O)C[C@@H]3O2)c1,1,,,,,train
C=C/C=C/CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,1,,,,,train
C=C(C)[C@H](CC=C(C)C)Cc1c(O)cc(O)c2c1O[C@H](c1ccc(O)cc1O)CC2=O,1,,,,,test
CO[C@H]1[C@H](O)[C@@H](O)[C@H](n2c3ccccc3c3c4c(c5c6ccccc6[nH]c5c32)C(=O)N(C)C4)O[C@@H]1CO.CO[C@H]1[C@H](O)[C@@H](O)[C@H](n2c3ccccc3c3c4c(c5c6ccccc6[nH]c5c32)CN(C)C4=O)O[C@@H]1CO,1,,,,,train
C[Se]CC[C@H](N)C(=O)O,1,,,,,train
CCC(C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@@H](O)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2.CO[C@H]1C[C@H](O[C@H]2[C@H](C)O[C@@H](O[C@@H]3/C(C)=C/C[C@@H]4C[C@@H](C[C@]5(C=C[C@H](C)[C@@H](C(C)C)O5)O4)OC(=O)[C@@H]4C=C(C)[C@@H](O)[C@H]5OC/C(=C\C=C\[C@@H]3C)[C@]54O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,1,,,,,train
CC12CCC(=O)C=C1CCC1C2[C@@H](O)CC2(C)C1CC[C@]2(O)C(=O)COP(=O)(O)O,1,,,,,train
CN(CC[C@H](N)CC(=O)N[C@@H]1C=C[C@@H](n2ccc(N)nc2=O)O[C@H]1C(=O)O)C(=N)N,1,,,,,valid
C=C1C(C)(C)[C@@]2(Cl)C(Cl)[C@]1(Cl)C(Cl)(Cl)C2(Cl)Cl,1,,,,,train
Cc1cc(Cl)ccc1O[C@@H](C)C(=O)ON(C)C,0,,,,,train
COC(=O)[C@@H](N)Cc1ccc(O)cc1,1,,,,,train
CCC1O[C@]2(CCC1C)C[C@@H]1C[C@@H](C/C=C(\C)CC(C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,1,,,,,train
N=C(N)NCCC[C@H]1N[C@@H](CNC(=O)c2cnc3ccccc3c2)CCN(CC2CCCCC2)C1=O,1,,,,,test
CC(C)=CC1[C@@H](C(=O)OCc2cccc(Oc3ccccc3)c2)C1(C)C,1,,,,,train
O=C(O)[C@@H]1[C@H](C(=O)O)[C@H]2CC[C@@H]1O2,0,,,,,train
CCCCCCCCC1CC(=O)N[C@@H](CC(N)=O)C(=O)N[C@H](Cc2ccc(O)cc2)C(=O)N[C@H](CC(N)=O)C(=O)N2CCC[C@H]2C(=O)N[C@@H](CCC(=O)O)C(=O)N[C@H](CO)C(=O)N[C@@H]([C@@H](C)O)C(=O)N1,1,,,,,train
C[C@H](NC(=O)[C@H](C)NC(=O)[C@@H](N)CCP(C)(=O)O)C(=O)O,1,,,,,train
CC1(C)[C@@H]([C@@H](Br)C(Br)(Br)Br)[C@H]1C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1,1,,,,,valid
ClC1=C(Cl)[C@]2(Cl)[C@H]3C[C@H](Cl)[C@H](Cl)[C@H]3[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
CC1=CCC[C@H]2[C@](C)(Cc3cc(O)ccc3O)[C@@H](C)CC[C@]12C,1,,,,,train
COc1cc2c(cc1OC)[C@@]13CCN4CC5=CCOC6CC(=O)N2C1[C@H]6[C@H]5C[C@H]43,1,,,,,train
CN([C@@H]1CCc2c(CC(=O)O)c3cc(S(C)(=O)=O)ccc3n2C1)S(=O)(=O)c1ccc(F)cc1,1,,,,,train
COC(=O)[C@@H]1c2cc3c(c(O)c2[C@@H](OC)C[C@@]1(C)O)C(=O)c1c(O)cc2c(c1C3=O)O[C@@H]1O[C@@]2(C)[C@H](O)[C@@H](N(C)C)[C@@H]1O,1,,,,,test
C[C@H](CCC(=O)O)[C@H]1CC[C@H]2[C@@H]3CC[C@@H]4C[C@H](O)CC[C@]4(C)[C@H]3C[C@H](O)[C@]12C,0,,,,,train
CC(=O)NC(C(=O)N[C@H](Cc1cc2ccccc2n1C(C)=O)C(=O)NC(Cc1ccccc1)C(=O)N(C)Cc1ccccc1)C(C)O,1,,,,,train
CC1(C)OC[C@@H]2O[C@@]3(C(=O)O)OC(C)(C)O[C@H]3[C@@H]2O1,0,,,,,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](CO[C@H]2[C@H](O)[C@@H](O)[C@@H](OC(C(=O)O)C(O)C(OP(=O)(O)O)C(O)C(=O)O)O[C@@H]2CO)[C@@H](O)[C@H]1O,1,,,,,train
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3OC1CC(/N=c2/c(O)c(O)/c2=N\CCCCCC/N=c2\c(O)c(O)\c2=N\C2CC(O[C@H]3C[C@](O)(C(=O)CO)Cc4c(O)c5c(c(O)c43)C(=O)c3c(OC)cccc3C5=O)OC(C)C2O)C(O)C(C)O1,1,,,,,valid
CC(C)(C)[C@H](O)[C@H](Cc1ccc(Cl)cc1)n1cncn1,1,,,,,train
CC(C)[C@@]1(O)[C@@H](OC(=O)c2ccc[nH]2)[C@@]2(O)[C@@]3(C)CC4(O)O[C@@]5([C@H](O)[C@@H](C)CC[C@]35O)[C@@]2(O)C41C,1,,,,,train
O=C1C[C@H]2OCC=C3CN4CC[C@]56c7ccccc7N1[C@H]5[C@H]2[C@@H]3C[C@H]46,1,,,,,train
CC(C)(C)NCC(O)COc1cccc2c1C[C@H](O)[C@H](O)C2,1,,,,,train
C=CCC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,1,,,,,test
C#CCC1=C(C)[C@H](OC(=O)[C@H]2[C@H](C=C(C)C)C2(C)C)CC1=O,1,,,,,train
COc1ccc([C@@H]2Sc3ccccc3N(CCN(C)C)C(=O)[C@@H]2OC(C)=O)cc1.O=C(O)CC(O)C(=O)O,1,,,,,train
CC(C)C[C@H](NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCCN)NC(=O)C(C)C)C(=O)N[C@@H](Cc1ccccc1)C(=O)NCC(=O)O,1,,,,,train
C/C=C(\C)C(=O)O[C@H]1[C@H](O)[C@@H](O)[C@H](O[C@@H]2[C@H]3OC(=O)CCCCCCCCC[C@H](CCCCC)O[C@@H]4O[C@H](C)[C@@H](O)[C@H](O)[C@H]4O[C@@H]4O[C@H](COC(=O)C(C)C)[C@@H](O)[C@H](O)[C@H]4O[C@H](O[C@H]2C)[C@@H]3OC(=O)[C@H](C)[C@@H](C)O)O[C@@H]1C,1,,,,,train
C[C@@H](Oc1ccc(Cl)cc1Cl)C(=O)O,0,,,,,valid
CNCC[C@@H](Oc1ccc(C(F)(F)F)cc1)c1ccccc1,1,,,,,train
ClC1=C(Cl)[C@]2(Cl)[C@H]3C[C@@H](Cl)[C@H](Cl)[C@H]3[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
C[C@H]1COc2c(N3CCN(C)CC3)c(F)cc3c(=O)c(C(=O)O)cn1c23,1,,,,,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCCC(=O)NCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCS(C)(=O)=O)NC1=O,0,,,,,train
CCO[C@@H]1[C@@H](OC)[C@H](C)O[C@@H](O[C@@H]2C[C@H]3CC[C@@H]4[C@@H](C=C5C(=O)[C@H](C)[C@@H](O[C@H]6CC[C@H](N(C)C)[C@@H](C)O6)CCC[C@H](CC)OC(=O)C[C@H]54)[C@@H]3C2)[C@@H]1OC.CCO[C@@H]1[C@@H](OC)[C@H](C)O[C@@H](O[C@H]2C[C@H]3[C@@H]4C=C5C(=O)[C@H](C)[C@@H](O[C@H]6CC[C@H](N(C)C)[C@@H](C)O6)CCC[C@H](CC)OC(=O)C[C@H]5[C@@H]4C=C(C)[C@@H]3C2)[C@@H]1OC,1,,,,,test
CC[C@H](C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O[C@H]4C[C@H](OC)[C@H](NC)[C@H](C)O4)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,1,,,,,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C(C)(C)C)C1(C)CCOCC1)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,1,,,,,train
CC(C)=C[C@@H]1[C@@H](C(=O)OCN2C(=O)C3=C(CCCC3)C2=O)C1(C)C,1,,,,,train
CC[C@H](C)[C@H](NC(=O)[C@@H](NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H]1CCCN1C(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](CCC(=O)O)NC(=O)[C@H](CCCCN)NC(=O)[C@H](C)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@@H](NC(=O)[C@H](CO)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](N)CCCCN)C(C)C)C(C)C)C(=O)NCC(=O)N[C@H](C(=O)N[C@@H](CO)C(=O)N[C@H](C(=O)N1CCC[C@H]1C(=O)N[C@@H](Cc1ccccc1)C(=O)O)[C@@H](C)CC)C(C)C,1,,,,,train
NCCCC[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCCN)NC(=O)[C@@H](N)CCCN=C(N)N)C(N)=O,0,,,,,valid
CC=C[C@@H]1[C@@H](C(=O)OCc2c(F)c(F)c(COC)c(F)c2F)C1(C)C,1,,,,,train
CN(C)[C@@H]1C(=O)/C(=C(\N)O)C(=O)[C@@]2(O)C(=O)C3=C(O)c4c(O)ccc(Cl)c4[C@@](C)(O)[C@H]3C[C@@H]12,1,,,,,train
CCCCC(CC)COC(=O)[C@@H](C)Oc1ccc(Cl)cc1Cl,1,,,,,train
CC[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)C(NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(CC)CC,0,,,,,train
COc1ccc(C(=O)O[C@H]2CC[C@@]3(C)[C@@H]4CC[C@H]5[C@]6(O)C[C@H](O)[C@@]7(O)[C@@H](CN8C[C@@H](C)CC[C@H]8[C@@]7(C)O)[C@]6(O)C[C@@]53O[C@]24O)cc1OC,1,,,,,test
C=C(C)O[C@@]12CO[C@@H]1C[C@@H]1C[C@@]13C(=O)[C@H](OC(C)=O)C1=C(C)[C@@H](OC(=O)[C@H](O)[C@@H](NC(=O)OC(C)(C)C)c4ccccc4)C[C@@](O)([C@H](OC(=O)c4ccccc4)[C@H]23)C1(C)C,1,,,,,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](N)c3ccc(O)cc3)C(=O)N2[C@H]1C(=O)O,0,,,,,train
CNCC[C@H](Oc1ccc(C(F)(F)F)cc1)c1ccccc1,1,,,,,train
CO[C@H]1C[C@H](O[C@H]2[C@H](C)OC(O[C@@H]3/C(C)=C\CC4CC(CC5(CC[C@H](C)CO5)O4)OC(=O)C4C=C(C)[C@@H](O)C5OC/C(=C/C=C\C3C)[C@@]45O)C[C@@H]2OC)O[C@@H](C)[C@@H]1O,1,,,,,train
CC1=C2C(=CO[C@H](C)[C@H]2C)C(=O)C(=C(O)O)C1=O,1,,,,,valid
CN[C@@H]1[C@H](O[C@H]2[C@H](O[C@H]3[C@H](O)[C@@H](O)[C@H](NC(=N)N)[C@@H](O)[C@@H]3NC(=N)N)O[C@@H](C)[C@]2(O)C=O)O[C@@H](CO)[C@H](O)[C@H]1O,0,,,,,train
CC1(C)S[C@@H]2[C@H](NC(=O)[C@H](N)c3ccccc3)C(=O)N2[C@H]1C(=O)O,0,,,,,train
O=S(=O)(O)[C@H](O)CC[C@H](O)S(=O)(=O)O,1,,,,,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@](C)(Cc2ccccc2)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,1,,,,,train
OCC1=C[C@H](N[C@H]2C[C@H](CO)[C@@H](O[C@@H]3O[C@H](CO)[C@@H](O)[C@H](O)[C@H]3O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,0,,,,,test
CCNC(=O)[C@H](C)OC(=O)Nc1ccccc1,0,,,,,train
CC1(C)[C@H](/C=C(\Cl)C(F)(F)F)[C@@H]1C(=O)O[C@H](C#N)c1cccc(Oc2ccccc2)c1,1,,,,,train
CNC.C[C@@H](Oc1ccc(Cl)cc1Cl)C(=O)O,0,,,,,train
C=C1C[C@]23C[C@@]1(O)CC[C@H]2[C@@]12C=C[C@H](O)[C@@](C)(C(=O)O1)[C@H]2[C@@H]3C(=O)O,0,,,,,train
CC[C@H](C)[C@@H]1NC(=O)[C@@H](Cc2ccc(OC)cc2)NC(=O)[C@H](N)C(C)(C)SSC[C@H](C(=O)N2CCC[C@H]2C(=O)N[C@@H](CCCN=C(N)N)C(=O)NCC(N)=O)NC(=O)[C@@H](CC(N)=O)NC(=O)[C@H](C(C)C)NC1=O,1,,,,,valid
CC(=O)O[C@H]1C[C@H]2[C@H]([C@@H]3[C@@H](O)[C@@H]4[C@H]([C@H](C)C=C5OC(=O)[C@@](C)(O)[C@@]54C)[C@]31C)[C@@H](O)C(=O)[C@H]1C[C@@H]3O[C@@H]3[C@H](O)[C@@]12C,1,,,,,train
CN(C)[C@@H]1C(=O)/C(=C(\N)O)C(=O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12,0,,,,,train
C=CC[C@H]1OC(=O)CCNC(=O)[C@H](C)N(C)C(=O)[C@H](C(C)C)N(C)C(=O)[C@H]([C@@H](C)CC)NC(=O)[C@@H]2CCCN2C1=O,0,,,,,train
CNC(C)[C@@H]1CC[C@@H](N)[C@@H](OC2[C@@H](N)C[C@@H](N)[C@H](OC3OC[C@](C)(O)[C@H](NC)[C@H]3O)[C@H]2O)O1,0,,,,,train
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H]([C@H]4C=C[C@@H]3C4)[C@@]1(Cl)C2(Cl)Cl,1,,,,,test
CC(C)(C)c1cc(C(C)(C)C)c2[nH]c(C(C)(C)C)c(C[C@H](NC(=O)[C@@H](N)CCCNC(=N)N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)NCCc3ccccc3)c2c1,0,,,,,train
COCCOC[C@@H](NC(=O)Nc1cc2[nH]nc(-c3cc(C)on3)c2cn1)c1ccccc1,1,,,,,train
Cc1cc(Cl)ccc1O[C@H](C)C(=O)O,1,,,,,train
c1ccc([C@H]2CN3CCSC3=N2)cc1,0,,,,,train
CC(C)c1ccc2c(c1Cl)CC[C@H]1[C@](C)(C(=O)O)CCC[C@]21C,1,,,,,valid
CCC(C)[C@H]1O[C@]2(C=C[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)[C@@H](O[C@H]3C[C@H](OC)[C@@H](O)[C@H](C)O3)[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,1,,,,,train
CCCCC(CC)CO[C@H]1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,0,,,,,train
CC(C)NC[C@@H](O)COc1cccc2ccccc12.[H+],1,,,,,train
C[C@H]1CN2CCC[C@H]2CN1C(=O)N1Cc2c(NC(=O)c3cccc(C#N)c3)n[nH]c2C1(C)C,1,,,,,train
C[C@@H](NC(=O)[C@@H](N)CC[S+](C)O)P(=O)(O)O,0,,,,,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)C(NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CCCl,1,,,,,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(OC)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O,1,,,,,train
C[C@H](N)C(=O)N[C@@H](C)C(=O)N[C@H]1[C@@H]2CN(c3nc4c(cc3F)c(=O)c(C(=O)O)cn4-c3ccc(F)cc3F)C[C@@H]21,1,,,,,train
OC[C@H]1O[C@@H](c2ccc(Cl)c(Cc3ccc(O[C@H]4C[C@@H]5C[C@@H]5C4)cc3)c2)[C@H](O)[C@@H](O)[C@@H]1O,1,,,,,train
CCCCC(CC)COC(=O)[C@H](C)O,1,,,,,valid
CN[C@@H](C)[C@H](O)c1ccccc1.[H+],0,,,,,train
CC(C)=CCC[C@@H](C)CC=O,1,,,,,train
CC(C)=CCC[C@@H](C)CCO,1,,,,,train
NC[C@@H]1CCC[C@H](CN)C1,0,,,,,train
CC1=CC2CC1[C@H]1C(=O)OC(=O)[C@@H]21,0,,,,,test
CC1CC[C@@]2(OC1)O[C@H]1C[C@H]3[C@@H]4CC[C@@H]5C[C@@H](O[C@@H]6O[C@H](CO)[C@H](O)[C@H](O[C@@H]7OC[C@@H](O)[C@H](O)[C@H]7O)[C@H]6O[C@@H]6O[C@H](CO)[C@@H](O)[C@H](O)[C@H]6O)[C@@H](O)C[C@]5(C)[C@H]4CC[C@]3(C)[C@H]1[C@@H]2C,1,,,,,train
CC(C)[C@H](N)C(=O)N1Cc2ccccc2C[C@H]1C(=O)N[C@H](C(=O)O)C(C)C,1,,,,,train
CCCCCC[C@@H](C)[C@H](O)CC(=O)NCC(=O)N[C@H](C(=O)C[C@@H](CC(C)C)C(=O)N[C@@H](C)C(=O)N[C@@H](Cc1ccccc1)C(=O)OC)C(C)C,1,,,,,train
CCCCCCOC1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,0,,,,,train
CC(CC(=O)C[C@@H](C)[C@H]1CC(=O)[C@@]2(C)C3=C(C(=O)[C@@H](O)[C@]12C)[C@@]1(C)CC[C@H](O)C(C)(C)[C@@H]1CC3=O)C(=O)O,1,,,,,valid
CC(=O)OC[C@H]1O[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H](OC(C)=O)[C@@H]1OC(C)=O,1,,,,,train
OC[C@@H]1[C@@H](O)[C@H](O)C[S+]1C[C@@H](O)[C@@H](O)[C@H](O)[C@H](O)[C@@H](O)CO,0,,,,,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O,0,,,,,train
OC[C@H]1O[C@@H](OC/C=C/c2ccccc2)[C@H](O)[C@@H](O)[C@@H]1O,1,,,,,train
CC(C)C1=CC2=CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1.CC(C)C1=CC2=CC[C@@H]3[C@](C)(CCC[C@@]3(C)C(=O)O)[C@H]2CC1.[Ca+2],1,,,,,test
COC1=CCC2=C(CC[C@@H]3[C@@H]2CC[C@]2(C)[C@@H](O)CC[C@@H]32)C1,1,,,,,train
CCOC(=O)[C@H](C)O,0,,,,,train
N=C(N)NCCC[C@H](N)C(=O)O,0,,,,,train
COC(=O)[C@H](C)O,0,,,,,train
C=CC(=O)OC1C[C@@H]2CC[C@@]1(C)C2(C)C,1,,,,,valid
N[C@@H](CCC(=O)C[C@@H](CSSC[C@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)C(=O)NCC(=O)O)C(=O)O,0,,,,,train
CC(=O)OCCC1=CC[C@H]2C[C@@H]1C2(C)C,1,,,,,train
CC1(C)CCC[C@]2(C)[C@H]3CCO[C@]3(C)CC[C@@H]12,0,,,,,train
NC(=NCCCCCCN=C(N)N=C(N)Nc1ccc(Cl)cc1)N=C(N)Nc1ccc(Cl)cc1.O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,1,,,,,train
CC(=O)O[C@@H]1CCCC[C@@H]1C(C)(C)C,1,,,,,test
COC(=O)Nc1nc2cc([S@](=O)c3ccccc3)ccc2[nH]1,1,,,,,train
CC(C)(C)[C@@H](O)[C@H](Cc1ccc(Cl)cc1)n1cncn1.CC(C)(C)[C@H](O)[C@@H](Cc1ccc(Cl)cc1)n1cncn1,1,,,,,train
CC(C)[C@H](Nc1ccc(C(F)(F)F)cc1Cl)C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,1,,,,,train
C=C1C(C)(C)[C@]2(Cl)C(Cl)[C@]1(Cl)C(Cl)(Cl)C2(Cl)Cl,1,,,,,train
ClC1=C(Cl)[C@@]2(Cl)[C@H]3[C@H]([C@H]4C=C[C@@H]3C4)[C@@]1(Cl)C2(Cl)Cl,1,,,,,valid
COC(=O)[C@@H](C)Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1,1,,,,,train
CCO[P@@](=S)(Oc1cnc(C(C)(C)C)nc1)OC(C)C,1,,,,,train
CCO[P@](=S)(NC(C)C)Oc1ccccc1C(=O)OC(C)C,1,,,,,train
C[C@H]1C(c2ccc(Cl)cc2)SC(=O)N1C(=O)NC1CCCCC1,1,,,,,train
C[C@@H](Oc1ccc(Oc2ncc(Cl)cc2F)cc1)C(=O)O,1,,,,,test
CCSC(=O)/C=C(C)/C=C/C[C@H](C)CCCC(C)(C)OC,1,,,,,train
O=[S@]1OC[C@@H]2[C@H](CO1)[C@@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,1,,,,,train
CC1(C)C(C=C(Cl)Cl)C1C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,1,,,,,train
CO[P@](=S)(Oc1cc(Cl)c(Br)cc1Cl)c1ccccc1,1,,,,,train
C#CCC1=C(C)[C@@H](OC(=O)[C@H]2[C@H](C=C(C)C)C2(C)C)CC1=O,1,,,,,valid
CCOC(=O)CCC(=O)CC1(O)C2(Cl)[C@]3(Cl)C4(Cl)C(Cl)(Cl)C5(Cl)[C@@](Cl)(C1(Cl)[C@@]53Cl)[C@]42Cl,1,,,,,train
C[C@@H](Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1)C(=O)N(C)c1ccccc1F,1,,,,,train
C[C@@H](COc1ccc(C(C)(C)C)cc1)O[S@](=O)OCCCl,1,,,,,train
COC(=O)[C@]1(O)c2ccccc2-c2ccc(Cl)cc21,1,,,,,train
C[C@@H](Oc1ccc(Oc2ncc(Cl)cc2Cl)cc1)C(=O)N1CCCO1,1,,,,,test
CC(C)(C)[C@@H](O)/C(=C/c1ccc(Cl)cc1Cl)n1cncn1,1,,,,,train
CC(C)OC(=O)[C@@H](C)N(C(=O)c1ccccc1)c1ccc(F)c(Cl)c1,1,,,,,train
CCCCCCCCCCCCN1C[C@@H](C)O[C@H](C)C1,1,,,,,train
CCO[P@](=S)(N[C@H](C)CC)Oc1cc(C)ccc1[N+](=O)[O-],1,,,,,train
COC(=O)[C@@H](C)N(C(=O)Cc1ccccc1)c1c(C)cccc1C,1,,,,,valid
CC1(C)O[C@H](c2ccco2)CN1C(=O)C(Cl)Cl,1,,,,,train
CC1=CC[C@H](C(C)(C)O)CC1,1,,,,,train
CC(C)[C@@H](NC(=O)O)C(=O)N[C@H](C)c1nc2ccc(F)cc2s1,1,,,,,train
CC(C)OC(=O)N[C@@H](C(=O)N[C@H](C)c1nc2ccc(F)cc2s1)C(C)C,1,,,,,train
C/C=C(\C)C(=O)O[C@H]1C[C@@H](OC(C)=O)[C@@]2(C(=O)OC)CO[C@H]3[C@@H](O)[C@@](C)([C@]45O[C@@]4(C)[C@H]4C[C@@H]5O[C@H]5OCC[C@@]54O)[C@H]4[C@]1(CO[C@]4(O)C(=O)OC)[C@@H]32,1,,,,,test
C=C(C)[C@@H]1CC=C(C)C(=O)C1,1,,,,,train
C[C@@H](Oc1cccc(Cl)c1)C(=O)O,1,,,,,train
Cc1ccc([C@H](C)NC(=O)C(NC(=O)OC(C)C)C(C)C)cc1,1,,,,,train
CP(=O)(O)CC[C@H](N)C(=O)O,1,,,,,train
CCCS[P@@](=O)(OCC)N1CCN(CC)/C1=N/C#N,1,,,,,valid
CCNC(=O)[C@@H](C)OC(=O)Nc1ccccc1,1,,,,,train
C[C@@H](Oc1ccc(Oc2cnc3cc(Cl)ccc3n2)cc1)C(=O)O,1,,,,,train
COC(=O)CC(NC(=O)[C@@H](NC(=O)OC(C)C)C(C)C)c1ccc(Cl)cc1,1,,,,,train
C=C1C[C@]23C[C@@H]1CC[C@H]2[C@@]12CC[C@H](O)[C@](C)(C(=O)O1)[C@H]2[C@@H]3C(=O)O,1,,,,,train
COCC(=O)N(c1c(C)cccc1C)[C@H](C)C(=O)O,1,,,,,test
COCC(=O)N(c1c(C)cccc1C(=O)O)[C@H](C)C(=O)O,1,,,,,train
Cc1ccc(C(=O)O)c(C2=N[C@](C)(C(C)C)C(=O)N2)c1,1,,,,,train
C=C1C[C@]23CC1(O)CC[C@H]2[C@@]12C=C[C@H](O)C(C)(C(=O)O1)[C@H]2[C@@H]3C(=O)O,0,,,,,train
COC(=O)[C@@H](C)N(C(=O)c1ccccc1)c1ccc(F)c(Cl)c1,0,,,,,train
COc1cc(CCCS(=O)(=O)O)ccc1O[C@H](Cc1cccc(OC)c1O)CS(=O)(=O)O,0,,,,,valid
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1C=C(Cl)Cl,1,,,,,train
C#C[C@@H](OC(=O)[C@@H]1[C@H](C=C(C)C)C1(C)C)/C(C)=C/CC,1,,,,,train
COC(=O)N[C@H]1[C@H](O[C@H]2[C@H](O)[C@@H](N)[C@H](O[C@H]3[C@@H](O)[C@@H](N)[C@H](O)O[C@@H]3CO)O[C@@H]2CO)O[C@H](CO)[C@@H](O[C@@H]2O[C@H](CO)[C@@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@@H]4O[C@H](CO)[C@@H](O[C@@H]5O[C@H](CO)[C@@H](O[C@@H]6O[C@H](CO)[C@@H](O[C@@H]7O[C@H](CO)[C@@H](O)[C@H](O)[C@H]7N)[C@H](O)[C@H]6N)[C@H](O)[C@H]5N)[C@H](O)[C@H]4N)[C@H](O)[C@H]3N)[C@H](O)[C@H]2N)[C@@H]1O,1,,,,,train
O=C1[C@H]2CC=CC[C@H]2C(=O)N1SC(Cl)(Cl)Cl,1,,,,,train
O=C(O)[C@]1(O)c2ccccc2-c2ccc(Cl)cc21,1,,,,,test
CCCCOC(=O)[C@@H](C)Oc1ccc(Oc2ccc(C(F)(F)F)cn2)cc1,1,,,,,train
C=C1[C@@H](C)[C@H]2[C@H](Cc3ccccc3)NC(=O)[C@]23OC(=O)/C=C/[C@H](O)CCC[C@@H](C)C/C=C/[C@H]3[C@@H]1O,1,,,,,train
CC(C)(C)[C@H](O)/C(=C\c1ccc(Cl)cc1)n1cncn1,1,,,,,train
C[C@@H](Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1)C(=O)O,1,,,,,train
CC[C@H](Oc1ccccc1)C(=O)O,1,,,,,valid
C[C@@H](Oc1ccc(Oc2ccc(C#N)cc2F)cc1)C(=O)O,1,,,,,train
C=C1CC[C@H](O)C/C1=C/C=C1\CCC[C@]2(C)[C@@H]([C@H](C)CCCC(C)C)CC[C@@H]12,0,,,,,train
ClC1=C(Cl)[C@@]2(Cl)[C@H]3C[C@@H](Cl)[C@H](Cl)[C@H]3[C@@]1(Cl)C2(Cl)Cl,1,,,,,train
CCC1O[C@]2(CCC1C)C[C@@H]1C[C@@H](CC=C(C)CC(C)C=CC=C3CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,1,,,,,train
CNC(=O)O/N=C1/[C@@H](Cl)[C@H]2C[C@@H](C#N)[C@@H]1C2,1,,,,,test
CC(C)[C@@]1(O)[C@@H](OC(=O)c2ccc[nH]2)[C@@]2(O)[C@@]3(C)C[C@]4(O)O[C@@]5([C@H](O)[C@@H](C)CC[C@]35O)[C@@]2(O)[C@@]14C,1,,,,,train
Cc1cnc(C2=N[C@](C)(C(C)C)C(=O)N2)c(C(=O)O)c1,1,,,,,train
CC=C(C)C(=O)O[C@H]1CC[C@@]2(C)[C@@H]3CC[C@H]4[C@]5(O)C[C@H](O)[C@@]6(O)[C@@H](CN7C[C@@H](C)CC[C@H]7[C@@]6(C)O)[C@]5(O)C[C@@]42O[C@]13O,1,,,,,train
CCCCO[C@@H](C)COC(=O)[C@@H](C)Oc1cc(Cl)c(Cl)cc1Cl,1,,,,,train
C[C@H](c1ccccc1CNCCN)[N+](=O)[O-],1,,,,,valid
O=S1OC[C@@H]2[C@H](CO1)[C@@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,1,,,,,train
O=[S@@]1OC[C@@H]2[C@H](CO1)[C@@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,1,,,,,train
CCCCCCCC[C@H](Cl)[C@H](Cl)CCCCCCCC(=O)O,1,,,,,train
CC1(C)[C@H](C(=O)O[C@@H](C#N)c2ccc(F)c(Oc3ccccc3)c2)[C@@H]1C=C(Cl)Cl,1,,,,,train
C/C=C/[C@H]1[C@H](C(=O)OCc2c(F)c(F)c(C)c(F)c2F)C1(C)C,1,,,,,test
CCC[C@@H](C)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,1,,,,,train
Cl[C@H]1[C@H](Cl)[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H]1Cl,1,,,,,train
CO[P@@](=S)(NC(C)C)Oc1ccc(C)cc1[N+](=O)[O-],1,,,,,train
O=CN[C@H](Nc1ccc(Cl)c(Cl)c1)C(Cl)(Cl)Cl,1,,,,,train
CC(C)(NC(=O)[C@H](Br)C(C)(C)C)c1ccccc1,1,,,,,valid
CC1=CC[C@H](C(C)C)CC1,1,,,,,train
Cc1cccc([C@H](C)c2c[nH]cn2)c1C,1,,,,,train
O=C(Nc1ccc(Cl)cc1[C@@H](O)c1ccccc1)c1ccncc1,1,,,,,train
C[C@H]1O[C@H](O[C@H]2[C@H](O)[C@@H](O)[C@@H](O)[C@@H](O)[C@@H]2O)[C@H](N)C[C@@H]1NC(=N)C(=O)O,1,,,,,train
C[C@H](CCl)O[C@H](C)CCl,1,,,,,test
COCC1O[C@@H](O[C@@H]2C(COC)OC(OC)C(OC)C2OC)C(OC)C(OC)C1OC,0,,,,,train
C[C@H](O)CC(C)(C)O,0,,,,,train
CCC(CC)c1cccc(OC(=O)NC)c1.CCC[C@@H](C)c1cccc(OC(=O)NC)c1,1,,,,,train
CC(C)C[C@H](C)c1sccc1NC(=O)c1cn(C)nc1C(F)(F)F,1,,,,,train
C#C[C@@H](C)Oc1cc(N2C(=O)C3=C(CCCC3)C2=O)c(F)cc1Cl,1,,,,,valid
CCOC(=O)[C@@H](Cl)Cc1cc(-n2nc(C)n(C(F)F)c2=O)c(F)cc1Cl,1,,,,,train
CCN(CC)C(=O)[C@@H](C)Oc1cccc2ccccc12,1,,,,,train
C[C@@H](NC(=O)[C@@H](C#N)C(C)(C)C)c1ccc(Cl)cc1Cl,1,,,,,train
COCC(=O)O[C@H](c1ncccc1S(=O)(=O)NC(=O)Nc1nc(OC)cc(OC)n1)[C@@H](C)F,1,,,,,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)C=C[C@]4(C)[C@@]3(F)[C@@H](O)C[C@]2(C)[C@@]1(O)C(=O)CO,,1,,,,test
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1C[C@@H](O)[C@@H]2O,,1,,,,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,,1,,,,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CCC2=O,,1,,,,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@@]21C,,1,,,,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@]2(C)O,,1,,,,valid
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@@H]1CC[C@@H]2O,,1,,,,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@@H]4[C@H]3CC[C@@]21C,,1,,,,train
CC[C@H](CC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CC=C4C[C@@H](O)CC[C@]4(C)[C@H]3CC[C@]12C)C(C)C,,1,,,,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C=C(Cl)C4=CC(=O)[C@@H]5C[C@@H]5[C@]4(C)[C@H]3CC[C@@]21C,,1,,,,train
C[C@]12CCC(=O)C[C@@H]1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@]2(C)O,,1,,,,test
C[C@]12CC[C@H]3[C@@H](CC[C@@]45O[C@@H]4C(O)=C(C#N)C[C@]35C)[C@@H]1CC[C@@H]2O,,1,,,,train
C[C@]12C=CC3=C4CCC(=O)C=C4CC[C@H]3[C@@H]1CC[C@@H]2O,,1,,,,train
COc1ccc2cc([C@H](C)C(=O)O)ccc2c1,,1,,,,train
CC1(C)[C@H](C(=O)O[C@H](C#N)c2cccc(Oc3ccccc3)c2)[C@@H]1C=C(Br)Br,,1,,,,train
C=C1C(=O)N[C@H](C)C(=O)N[C@H](CC(C)C)C(=O)N[C@@H](C(=O)O)[C@H](C)C(=O)N[C@@H](CCCCNC(=N)N)C(=O)N[C@@H](/C=C/C(C)=C/[C@H](C)[C@H](Cc2ccccc2)OC)[C@H](C)C(=O)N[C@@H](C(=O)O)CCC(=O)N1C,,1,,,,valid
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H]1Cl,,1,,,,train
Cl[C@H]1CCCC[C@@H]1Cl,,1,,,,train
Cc1c(COC(=O)[C@@H]2[C@H](/C=C(\Cl)C(F)(F)F)C2(C)C)cccc1-c1ccccc1,,1,,,,train
COc1cccc([C@]2(O)CCCC[C@@H]2CN(C)C)c1,,1,,,,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@@]3(C#N)CC[C@@]21C,,1,,,,test
Nc1ccn([C@H]2C[C@H](O)[C@@H](CO)S2)c(=O)n1,,0,,,,train
N[C@@H](C(=O)N1CCCC1)[C@@H](O)c1ccncc1,,0,,,,train
CCCC[C@@H](CC)COC(=O)c1ccccc1C(=O)OC[C@@H](CC)CCCC,,1,,,,train
Cl[C@@H]1CCCC[C@H]1Cl,,1,,,,train
C=C/C=C/CC1=C(C)[C@@H](OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,,1,,,,valid
COc1cccc([C@@]2(O)CCCC[C@@H]2CN(C)C)c1,,1,,,,train
CCc1cccc(C)c1N(C(=O)CCl)[C@@H](C)COC,,1,,,,train
CCOC(=O)[C@@H](C)Oc1ccc(Oc2cnc3cc(Cl)ccc3n2)cc1,,1,,,,train
C[C@H]1CN(C2CCN(c3nc(N)n[nH]3)CC2)[C@@H](Cc2ccc(Cl)cc2)CO1,,1,,,,train
COC(=O)N(C(=O)N1CO[C@@]2(C(=O)OC)Cc3cc(Cl)ccc3C2=N1)c1ccc(OC(F)(F)F)cc1,,1,,,,test
Cc1ccc2c(c1)[C@H](Nc1nc(N)nc(C(C)F)n1)[C@@H](C)C2,,1,,,,train
CN(C)[C@@H]1C(=O)C(C(N)=O)=C(O)[C@@]2(O)C(=O)C3=C(O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12,,0,,,,train
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@@H](C)O[C@@H](C)C1,,1,,,,train
Cc1ccc([C@@H]2O[C@H](COC(=O)C(C)(C)C)[C@@H](OC(=O)C(C)(C)C)[C@H](OC(=O)C(C)(C)C)[C@H]2OC(=O)C(C)(C)C)cc1Cc1ccc(-c2ccc(F)cc2)s1,,1,,,,train
Cn1c(COc2ccc(CC3SC(=O)N([C@@H]4O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]4O)C3=O)cc2)nc2ccc(O)cc21,,0,,,,valid
C[C@@H]1COc2ccccc2N1C(=O)C(Cl)Cl.C[C@H]1COc2ccccc2N1C(=O)C(Cl)Cl,,1,,,,train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nnn[nH]2)cc1)[C@H](C(=O)O)C(C)C,,0,,,,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,,0,,,,train
Cc1cc(Cl)ccc1O[C@H](C)C(=O)O,,0,,,,train
O=C1[C@H](CC[C@H](O)c2ccc(F)cc2)[C@@H](c2ccc(O)cc2)N1c1ccc(F)cc1,,1,,,,test
COC(=O)[C@H](c1ccccc1Cl)N1CCc2sccc2C1,,1,,,,train
OCc1cc([C@@H](O)CNCCCCCCOCCOCc2c(Cl)cccc2Cl)ccc1O,,1,,,,train
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,,,1,,,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2C(=O)C[C@@]2(C)[C@H]1CC[C@]2(O)C(=O)CO,,,0,,,train
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4[C@H]3CC[C@@]21C,,,1,,,valid
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,,,0,,,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,,,0,,,train
CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]2O[C@H](C)C[C@H](N(C)C)[C@H]2O)[C@](C)(O)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O,,,0,,,train
NC[C@@H]1O[C@H](O[C@H]2[C@@H](O)[C@H](O[C@@H]3[C@@H](O)[C@H](N)C[C@H](N)[C@H]3O[C@H]3O[C@H](CN)[C@@H](O)[C@H](O)[C@H]3N)O[C@@H]2CO)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@H]4O[C@H](CN)[C@@H](O)[C@H](O)[C@H]4N)[C@H]3O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O,,,1,,,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3[C@H](O)[C@@H]12,,,0,,,test
CC(C)(O)CC[C@@H](O)[C@](C)(O)[C@H]1CC[C@@]2(O)C3=CC(=O)[C@@H]4C[C@@H](O)[C@@H](O)C[C@]4(C)[C@H]3CC[C@]12C,,,1,,,train
CC(C)NC[C@@H](O)COc1cccc2ccccc12,,,1,,,train
CC(=O)N1CCN(c2ccc(OC[C@H]3CO[C@](Cn4ccnc4)(c4ccc(Cl)cc4Cl)O3)cc2)CC1,,,1,,,train
CCOC(=O)[C@@H](C)Oc1ccc(Oc2nc3ccc(Cl)cc3o2)cc1,,,1,,,train
CN[C@H]1CC[C@@H](c2ccc(Cl)c(Cl)c2)c2ccccc21,,,1,,,valid
Cc1c(COC(=O)[C@@H]2[C@H](/C=C(\Cl)C(F)(F)F)C2(C)C)cccc1-c1ccccc1,,,1,,,train
CNCC[C@H](Oc1ccc(C(F)(F)F)cc1)c1ccccc1,,,1,,,train
CNCC[C@@H](Oc1ccc(C(F)(F)F)cc1)c1ccccc1,,,1,,,train
Cc1ccc2c(c1)[C@H](Nc1nc(N)nc(C(C)F)n1)[C@@H](C)C2,,,1,,,train
CCCCCCCCCCCCCCCCCC(=O)OCC(O)[C@H]1OC[C@@H](O)[C@@H]1O,,,1,,,test
COC1=C[C@@H]2[C@@H]3Cc4ccc(OC)c(OCCCCCCOc5c(OC)ccc6c5[C@@]57CCN(C)[C@@H](C6)[C@H]5C=C(OC)C(=O)C7)c4[C@]2(CCN3C)CC1=O,,,1,,,train
Cc1nc(/C=C(\CO)[C@@H]2C/C=C\CCC[C@H](C)[C@H](O)[C@@H](C)C(=O)C(C)(C)[C@@H](O)CC(=O)O2)cs1,,,1,,,train
OC[C@@H](O)[C@@H](O)[C@H](O)[C@@H](O)C(c1c[nH]c2ccccc12)c1c[nH]c2ccccc12,,,0,,,train
CC[C@]1(O)C[C@@H]2C[C@H](c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)c3[nH]c4ccccc4c3CCN(C2)C1,,,1,,,train
CC(C)/C(O)=C1/C(=O)O[C@@H](c2ccoc2)[C@]2(C)C(O)[C@@H](O)[C@@]34OC5(C)OC67CC(C)(C(O)[C@]6(O)[C@@H](O)[C@]3(O5)C12)[C@H](CC(=O)O)[C@]74C,,,1,,,valid
CN[C@H](CC(=O)O)C(=O)O,,,0,,,train
CCC(C)C(N)C1=NCC(C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](CCC(=O)O)C(=O)N[C@H](C(=O)N[C@H]2CCCCNC(=O)[C@H](CC(N)=O)NC(=O)[C@@H](CC(=O)O)NC(=O)[C@H](Cc3cnc[nH]3)NC(=O)[C@@H](Cc3ccccc3)NC(=O)[C@H]([C@@H](C)CC)NC(=O)[C@@H](CCCN)NC2=O)[C@@H](C)CC)S1.[Zn+2],,,1,,,train
Cc1nn(C)cc1S(=O)(=O)NC(=O)c1ccc(-n2ccc(OCC(C)(C)C(F)(F)F)n2)nc1N1C[C@@H](C)CC1(C)C,,,1,,,train
CN(C)[C@]12C(=O)C(c3ccccc3)=C(c3ccccc3)[C@H]1C1CCC2C1,,,1,,,train
CCCCC(CC)COC(=O)[C@H](C)O,,,0,,,test
CCCCCCCCCCCCC/C=C/[C@@H](O)[C@H](CO)NC(=O)CCCCCCC,,,0,,,train
CC[C@H](C)[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)CNC(=O)[C@@H](N)Cc1ccc(O)cc1)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CCCCN)C(N)=O,,,1,,,train
CC(C)n1c(/C=C/[C@@H](O)C[C@@H](O)CC(=O)OC(C)(C)C)c(-c2ccc(F)cc2)c2ccccc21,,,1,,,train
CC1(C)O[C@@H]2CC3C4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1.CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,,,0,,,train
CCCCCCCCCCCC(=O)NCCCC[C@H](N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(N)=O,,,1,,,valid
NC[C@H]1O[C@H](O[C@H]2[C@H](OC(=O)Nc3ccccc3)[C@@H](OC(=O)Nc3ccccc3)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](OC(=O)Nc2ccccc2)[C@@H]1OC(=O)Nc1ccccc1.O=C(O)C(F)(F)F,,,0,,,train
Oc1ccc(O)c2c1[C@@H](O)[C@H]1O[C@H]1C21Oc2cccc3cccc(c23)O1,,,1,,,train
COC(=O)N[C@@H](C(=O)N1CCC[C@H]1C(=O)Nc1ccc(-c2ccc(NC(=O)[C@@H]3CCCN3C(=O)[C@H](NC(=O)OC)c3ccccc3)cc2C(F)(F)F)c(C(F)(F)F)c1)c1ccccc1,,,1,,,train
CCCCC(CC)/N=C1\N[C@H](CO)[C@H](O)[C@H](O)[C@H]1O,,,0,,,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O,,,0,,,test
CCOP(=O)(O)N[C@@H](CC(C)C)C(=O)O,,,0,,,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,,,1,,,train
OCC(COC1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O)OC1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O.OCC(O)COC1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O.OC[C@H]1OC(OCC(COC2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)OC2O[C@H](CO)[C@@H](O)[C@H](O)[C@H]2O)[C@H](O)[C@@H](O)[C@@H]1O,,,0,,,train
CC(C)[C@H]1CC=C(CCC=O)CC1,,,1,,,train
C=CC(=O)O[C@H]1C[C@@H]2CC[C@@]1(C)C2(C)C,,,1,,,valid
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3OC1CC(/N=c2/c(O)c(O)/c2=N\CCCCCC/N=c2\c(O)c(O)\c2=N\C2CC(O[C@H]3C[C@](O)(C(=O)CO)Cc4c(O)c5c(c(O)c43)C(=O)c3c(OC)cccc3C5=O)OC(C)C2O)C(O)C(C)O1,,,0,,,train
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@@H](C)O[C@@H](C)C1,,,1,,,train
C1CCC(NC2CCCCC2)CC1.CC(C)(O)c1ccccc1CC[C@@H](SCC1(CC(=O)O)CC1)c1cccc(/C=C/c2ccc3ccc(Cl)cc3n2)c1,,,1,,,train
CC(=O)C1=C(C)[C@@H]2C[C@@]3(C1)[C@H](C)CC[C@H]3C2(C)C,,,1,,,train
C[C@H](NC(=O)[C@@H](NC(=O)CNC(=O)[C@H](Cc1c[nH]cn1)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)[C@H](CC(N)=O)NC(=O)CN)[C@@H](C)O)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)O,,,0,,,test
CCCC[C@H](N)C(=O)O,,,0,,,train
O=C(O[C@H]1OC[C@@H](OC(=O)c2cc(O)c(O)c(O)c2)[C@H](OC(=O)c2cc(O)c(O)c(O)c2)[C@H]1OC(=O)c1cc(O)c(O)c(O)c1)c1cc(O)c(O)c(O)c1,,,0,,,train
ClC[C@H]1CO1,,,0,,,train
CC(C)[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@@H](N)CC(=O)O)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@H]1CC/C=C/SC[C@@H](C(=O)N[C@@H](Cc2ccccc2)C(=O)O)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,,,0,,,train
CNC(=O)[C@H](Cc1ccc(OC)cc1)NC(=O)C1(CC(=O)NO)CCCCC1,,,0,,,valid
CC(C)(C)C(=O)OC[C@H]1O[C@H](Br)[C@H](OC(=O)C(C)(C)C)[C@@H](OC(=O)C(C)(C)C)[C@@H]1OC(=O)C(C)(C)C,,,1,,,train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nnn[nH]2)cc1)[C@H](C(=O)O)C(C)C,,,0,,,train
C[C@H](c1ccccc1)c1cccc([C@@H](C)c2ccccc2)c1O,,,1,,,train
CC1(C)C[C@@H]1C(=O)N/C(=C\CCCCSC[C@H](N)C(=O)O)C(=O)O.C[C@@H](O)[C@H]1C(=O)N2C(C(=O)O)=C(SCC/N=C\N)C[C@H]12,,,1,,,train
CCCCCCCCCCC/C=C/CCCCC(=O)N(C)C[C@H](C)[C@@H](C)[C@H](C)[C@H](C)CO.CCCCCCCCCCCCCCCCCC(=O)N(C)C[C@H](C)[C@@H](C)[C@H](C)[C@H](C)CO,,,1,,,test
C[C@]12CCC(=O)C=C1C=C[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@@]2(O)CCC(=O)O,,,0,,,train
CC1=CCC2CCCCC2C1N1C(=O)[C@@H]2[C@@H]3C[C@@H]4[C@@]5(C)CCC[C@@](C)(C(=O)O)[C@@H]5CC[C@]4(C=C3C(C)C)[C@@H]2C1=O,,,0,,,train
COc1ccc2c3c1O[C@H]1C(=O)CC[C@@]4(O)[C@@H](C2)N(C)CC[C@]314.COc1ccc2c3c1O[C@H]1C(=O)CC[C@@]4(O)[C@@H](C2)N(C)CC[C@]314.O=C(O)c1ccc(C(=O)O)cc1,,,0,,,train
CC(OC(=O)OC1CCCCC1)OC(=O)C1=C(CSc2nnnn2CCN(C)C)CS[C@@H]2[C@H](NC(=O)Cc3csc(N)n3)C(=O)N12,,,1,,,train
Cc1ccc(C2OC[C@@H]3OC(c4ccc(C)cc4)O[C@H]([C@H](O)CO)[C@@H]3O2)cc1,,,1,,,valid
CCC1(CC[C@@H]2[C@H](C)CCCC2(C)C)CCCO1.CC[C@@]12CCC3C(C)(C)CCC[C@]3(C)[C@@H]1CCO2.CC[C@@]12CCC3C(C)(C)CCC[C@]3(C)[C@@H]1CCO2,,,1,,,train
CC1(C)[C@H]2CC=C(CCO)[C@@H]1C2,,,1,,,train
O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.[Zn+2],,,1,,,train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nn[nH]n2)cc1)[C@H](C(=O)OC)C(C)C,,,1,,,train
C[C@H](OC(C)(C)COC(=O)C1CC1)[C@@H]1CCCC(C)(C)C1.C[C@H](OC(C)(C)COC(=O)C1CC1)[C@H]1CCCC(C)(C)C1,,,1,,,test
CN1[C@@H]2C[C@H](OC(=O)Nc3ccc(F)cc3-c3ccccc3)C[C@H]1[C@@H]1O[C@@H]12,,,0,,,train
O=C1N[C@H]2[C@H](OP(=O)(O)O)[C@H](O)[C@@H](O)[C@H](O)[C@@H]2c2cc3c(c(O)c21)OCO3.[Zn+2],,,1,,,train
C=C(C)[C@@H]1CC[C@]2(C(=O)NCC(=O)N3CCC[C@H]3C(=O)O)CC[C@]3(C)[C@H](CC[C@@H]4[C@@]5(C)CC[C@H](O)[C@@](C)(CO)[C@@H]5CC[C@]43C)[C@@H]12,,,0,,,train
Cc1ccccc1NC(=O)Nc1ccc(CC(=O)N(C)[C@@H](CC(C)C)C(=O)NCC[C@H](NC(=O)[C@@H]2CCCN2S(=O)(=O)c2cc(Cl)cc(Cl)c2)C(=O)O)cc1,,,0,,,train
CCCCCCCCO[C@H]1O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]1O[C@H]1O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]2O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]3O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]4O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]4OC(C)=O)[C@@H]3OC(C)=O)[C@@H]2OC(C)=O)[C@@H]1OC(C)=O,,,0,,,valid
Cc1ccc(C#Cc2ccc(S(=O)(=O)N[C@H](Cc3c[nH]c4ccccc34)C(=O)O)s2)cc1,,,1,,,train
COC1[C@H](NC(=O)CC2OC(COCc3ccccc3)C(OCc3ccccc3)C(OCc3ccccc3)C2OCc2ccccc2)CC[C@H]2[C@H]3Cc4ccc(O)cc4[C@@]12CCN3C,,,1,,,train
C[C@@H](N)COc1ccc(-c2cnc3ccc(N[C@H](C)c4cccc(F)c4)nn23)cc1.O=C(O)CCCCC(=O)O,,,1,,,train
CCCCOC(=O)[C@H]1CCCC[C@H]1C(=O)OCc1ccccc1,,,1,,,train
CC(=O)N[C@@H](C)C(=O)N[C@@H](CO)C(=O)N[C@@H](CSSC[C@@H](NC(=O)[C@H](CO)NC(=O)[C@H](C)NC(C)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1ccc(O)cc1)C(N)=O)[C@@H](C)O)C(C)C)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1ccc(O)cc1)C(N)=O)[C@@H](C)O)C(C)C,,,1,,,test
CO[C@@H]1C/C=C/[C@H](C)[C@@H]2O[C@](O)([C@H](C)C(=O)O[C@H](c3ccccc3)CCCC1)[C@H](OC)[C@@H](O)[C@@H]2C,,,1,,,train
C[C@H]1CN(C2CCN(c3nc(N)n[nH]3)CC2)[C@@H](Cc2ccc(Cl)cc2)CO1,,,1,,,train
CC(CC(=O)C[C@@H](C)[C@H]1CC(=O)[C@@]2(C)C3=C(C(=O)[C@@H](O)[C@]12C)[C@@]1(C)CC[C@H](O)C(C)(C)[C@@H]1CC3=O)C(=O)O,,,1,,,train
CC1([C@H]2CC[C@H]3C4=CC=C5CC6(CC[C@]5(C)[C@H]4CC[C@]23C)OCCO6)OCCO1,,,1,,,train
CSCC[C@H](NC(=O)[C@H](CO)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@@H](N)CO)C(=O)N[C@@H](CCC(=O)O)C(=O)N[C@@H](Cc1c[nH]cn1)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)NCC(=O)N[C@@H](CCCCN)C(=O)N1CCC[C@H]1C(=O)N[C@H](C(=O)NCC(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N1CCC[C@H]1C(=O)N[C@H](C(=O)N[C@@H](CCCCN)C(=O)N[C@H](C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](C)C(=O)NCC(=O)N[C@@H](CCC(=O)O)C(=O)O)C(C)C)C(C)C)C(C)C,,,1,,,valid
CCC(O)CCCCCCC[C@H](O)[C@H]1CC[C@H]([C@H]2CC[C@H]([C@H](O)CCCCCCCCCCC3CC(CC(C)=O)C(=O)O3)O2)O1,,,1,,,train
C[C@@H]1C/C=C\CCCCCCCCCC(=O)C1,,,1,,,train
CCOC(=O)[C@]1(C)CCC[C@@]2(C)[C@H]1CC=C1C=C(C(C)C)CC[C@@H]12,,,1,,,train
C#C[C@]1(OC(C)=O)CC[C@H]2[C@@H]3CCC4=C[C@@H](OC(C)=O)CC[C@@H]4[C@H]3CC[C@@]21C,,,1,,,train
CC(=O)N[C@@H](CCCCN)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](CCCNC(=N)N)C(N)=O,,,1,,,test
N[C@@H](CCC(=O)C[C@@H](CSSC[C@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)C(=O)NCC(=O)O)C(=O)O,,,1,,,train
O=c1nc(NO)ccn1[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,,,1,,,train
Br[C@H]1CC[C@H](Br)[C@@H](Br)CC[C@H](Br)[C@@H](Br)CC[C@@H]1Br,,,1,,,train
COC(=O)O[C@@]12CO[C@@H]1C[C@H](O)[C@@]1(C)C(=O)[C@H](OC(C)=O)C3=C(C)[C@@H](OC(=O)[C@H](O)[C@@H](NC(=O)c4ccccc4)c4ccccc4)C[C@@](O)([C@@H](OC(=O)c4ccccc4)[C@H]21)C3(C)C,,,1,,,train
N[C@@H]1CC2OC1c1ccccc12,,,1,,,valid
CCCC[C@@H](NC(=O)[C@@H](N)CC)B(O)O,,,1,,,train
C=C(C)[C@H]1CC=C(C)CC1,,,1,,,train
CCO[C@@H]1[C@]2(CC)C=CCN3CC[C@@]4(c5ccc(OC)cc5N(C)[C@H]4[C@@]1(O)C(=O)OC)[C@@H]32,,,1,,,train
CC(C)C[C@H](NC(=O)CNC(=O)[C@@H]1CCCN1C(=O)[C@@H]1CCCN1C(=O)CNC(=O)[C@H](CO)NC(=O)[C@@H](N)CCCN=C(N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)NCC(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CCC(N)=O)C(=O)O,,,,1,,train
CO[C@]12C[C@@H](CO)CN(C)[C@@H]1Cc1cn(C)c3cccc2c13,,,,1,,test
COC1[C@H](NC(=O)CC2OC(COCc3ccccc3)C(OCc3ccccc3)C(OCc3ccccc3)C2OCc2ccccc2)CC[C@H]2[C@H]3Cc4ccc(O)cc4[C@@]12CCN3C,,,,1,,train
Cc1nc(/C=C(\CO)[C@@H]2C/C=C\CCC[C@H](C)[C@H](O)[C@@H](C)C(=O)C(C)(C)[C@@H](O)CC(=O)O2)cs1,,,,1,,train
Nc1ccc(CCNC[C@H](O)c2ccccc2)cc1,,,,1,,train
C[C@]12C[C@H]3O[C@H]3C[C@@H]1CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)[C@@H](N3CCCC3)C[C@@H]12,,,,1,,train
CCOc1ccc(Cc2cc([C@@H]3O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@H]3OC(C)=O)ccc2Cl)cc1,,,,1,,valid
CCCC[C@H](N)C(=O)O,,,,1,,train
CC1(C)O[C@@H]2CC3C4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1.CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,,,,1,,train
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1OC(=O)[C@@H]1O[C@@H](O)CS1,,,,0,,train
Fc1ccc2c(c1)CC[C@@H]([C@H]1CO1)O2,,,,1,,train
Cn1c(COc2ccc(CC3SC(=O)N([C@@H]4O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]4O)C3=O)cc2)nc2ccc(O)cc21,,,,1,,test
OC[C@@H](O)[C@@H](O)[C@H](O)[C@@H](O)C(c1c[nH]c2ccccc12)c1c[nH]c2ccccc12,,,,1,,train
CC(=O)O[C@H]1C(C)(C)[C@H]2CC[C@]1(C)C2,,,,1,,train
CN1CCN(C(=O)Cc2ccc(Cl)c(Cl)c2)[C@H]2[C@@H]1CCC[C@@H]2N1CCCC1,,,,1,,train
F[C@@H]([C@H](F)C(F)(F)C(F)(F)F)C(F)(F)F.F[C@H]([C@@H](F)C(F)(F)C(F)(F)F)C(F)(F)F,,,,1,,train
COC(=O)[C@@H](C)Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1,,,,1,,valid
CC(C)C[C@@H](NC(=O)[C@H](O)COS(=O)(=O)O)C(=O)N[C@@H]1C(=O)N[C@@H](CCCN=C(N)N)C(=O)N[C@H]2CC[C@H](O)N(C2=O)[C@@H](CC(C)C)C(=O)N(C)[C@H](Cc2ccc(O)cc2)C(=O)N[C@H](C(C)C)C(=O)O[C@@H]1C,,,,1,,train
CCOc1ccc(C[C@H](N)CNCCN)cc1,,,,1,,train
Fc1ccc2c(c1)CC[C@@H]([C@@H]1CO1)O2,,,,1,,train
CC[C@]1(O)C[C@@H]2C[C@H](c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)c3[nH]c4ccccc4c3CCN(C2)C1,,,,1,,train
C[C@]12CC=C3[C@@H](CC[C@@]45CC6(CC[C@@]34O5)OCCO6)[C@@H]1CCC2=O,,,,0,,test
CN[C@H](CC(=O)O)C(=O)O,,,,1,,train
C1CCC(NC2CCCCC2)CC1.CC(C)(F)C[C@H](N[C@@H](c1ccc(-c2ccc(S(C)(=O)=O)cc2)cc1)C(F)(F)F)C(=O)O,,,,1,,train
O[C@H]1CNC[C@@H]1O,,,,1,,train
COc1ccc(C(OC[C@H]2O[C@@H](n3cnc4c(=O)[nH]c(NC(=O)C(C)C)nc43)C[C@@H]2O)(c2ccccc2)c2ccc(OC)cc2)cc1,,,,1,,train
C[C@]12CC[C@H]3[C@@H](C=C[C@]4(O)C[C@@H](O)CC[C@]34C)[C@@H]1[C@@H]1C[C@@H]1C2=O,,,,1,,valid
CCN1CCN(C(=O)Cc2ccc(Cl)c(Cl)c2)[C@H]2[C@@H]1CCC[C@@H]2N1CCCC1,,,,1,,train
CC(C)[C@H](N)C(=O)OCc1ccccc1.Cc1ccc(S(=O)(=O)O)cc1,,,,0,,train
CCCCOC(=O)[C@H]1CCCC[C@H]1C(=O)OCc1ccccc1,,,,1,,train
CN(C)[C@]12C(=O)C(c3ccccc3)=C(c3ccccc3)[C@H]1C1CCC2C1,,,,1,,train
N[C@@H](C(=O)N1CCCC1)[C@@H](O)c1ccncc1,,,,1,,test
CC1=CC[C@@H](C(C)CC=O)CC1,,,,1,,train
CC[C@H](C)[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)CNC(=O)[C@@H](N)Cc1ccc(O)cc1)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CCCCN)C(N)=O,,,,1,,train
C[C@]12CC[C@H]3[C@@H]([C@H]4C[C@H]4[C@]4(O)C[C@@H](O)CC[C@]34C)[C@@H]1[C@@H]1C[C@@H]1C2=O,,,,1,,train
NC(=O)CC[C@@H](C(=O)O)N(CCNC(=S)Nc1ccc2c(c1)C(=O)OC21c2ccc(O)cc2Oc2cc(O)ccc21)C(=O)c1ccc(NCc2cnc3nc(N)[nH]c(=O)c3n2)cc1,,,,1,,train
CN1CCN(c2ncccc2CO)[C@@H](c2ccccc2)C1.CN1CCN(c2ncccc2CO)[C@H](c2ccccc2)C1,,,,1,,valid
C[C@@H](N)c1ccccc1,,,,1,,train
CNC[C@H](O)c1cccc(O)c1,,,,1,,train
Fc1c(Cl)cc([C@]2(C(F)(F)F)CC(c3ccc4c(c3)COC43CNC3)=NO2)cc1Cl.O=S(=O)(O)c1ccccc1,,,,1,,train
CC(C)CCC[C@H](C)CCO,,,,1,,train
O=C(O)[C@H](O)[C@@H](O)C(=O)O,,,,1,,test
O=c1nc(NO)ccn1[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,,,,1,,train
CCCCCCCCCCCC(=O)NCCCC[C@H](N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(N)=O,,,,1,,train
CC(=O)N[C@@H](C)C(=O)N[C@@H](CO)C(=O)N[C@@H](CSSC[C@@H](NC(=O)[C@H](CO)NC(=O)[C@H](C)NC(C)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1ccc(O)cc1)C(N)=O)[C@@H](C)O)C(C)C)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1cnc[nH]1)C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N[C@@H](CC(C)C)C(=O)N[C@@H](CC(N)=O)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCC(N)=O)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1ccc(O)cc1)C(N)=O)[C@@H](C)O)C(C)C,,,,1,,train
COc1ccc2c(c1)CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,,,,1,,train
O[C@@H]1CO[C@H]2[C@@H]1OC[C@@H]2O,,,,1,,valid
C[C@H](N[C@@H]1CCc2[nH]c3ccccc3c2C1)c1ccccc1,,,,1,,train
Oc1ccc(O)c2c1[C@@H](O)[C@H]1O[C@H]1C21Oc2cccc3cccc(c23)O1,,,,1,,train
CC(=O)N[C@@H](CCC(=O)O)C(=O)O.CC(C)C[C@H](N)c1ccccc1N1CCCCC1,,,,1,,train
CCCN(CCc1cccs1)[C@H]1CCc2c(O)cccc2C1,,,,1,,train
CNS(=O)(=O)C[C@H]1CC[C@H](N(C)c2ncnc3[nH]ccc23)CC1,,,,1,,test
CC(=O)O[C@@H]1C[C@H](C)CC[C@H]1C(C)C,,,,1,,train
C[C@H](O)C(=O)O,,,,0,,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O,,,,0,,train
O=C(O)CC[C@H](NP(=O)(O)OCCCCc1ccccc1)C(=O)O,,,,1,,train
C[C@H]1CC[C@@]2(O)C(C)(C)[C@@H]3CC[C@@]2(C)[C@H]1C3,,,,1,,valid
CO[C@@H]1C/C=C/[C@H](C)[C@@H]2O[C@](O)([C@H](C)C(=O)O[C@H](c3ccccc3)CCCC1)[C@H](OC)[C@@H](O)[C@@H]2C,,,,1,,train
CSCC[C@H](N)C(=O)O,,,,0,,train
CCOC(=O)[C@@]12CCC[C@@H]1[C@@H]1CC[C@H]2C1,,,,1,,train
CC(=C/C(=O)O)/C=C/[C@@]1(O)[C@@H](C)CCCC1(C)C,,,,1,,train
C=C(C)[C@H]1CC=C(C)CC1,,,,1,,test
CCOP(=O)(O)N[C@@H](CC(C)C)C(=O)O,,,,1,,train
CCCN[C@H]1CCc2c(cccc2OC)C1,,,,1,,train
C=C(C)[C@@H]1CC[C@@H](C)C[C@H]1O,,,,1,,train
CC1(C)[C@@H]2CC[C@@]3(C2)[C@@H]1C(=O)CCC3(C)C,,,,1,,train
CC1(C)[C@@H](O)CC[C@]2(C)[C@H]3C(=O)C=C4[C@@H]5C[C@@](C)(C(=O)O)CC[C@]5(C)CC[C@@]4(C)[C@]3(C)CC[C@@H]12,,,,1,,valid
Cc1ccc2c(c1)[C@H](N)[C@@H](C)C2,,,,1,,train
C=C[C@H]1CN2CC[C@H]1C[C@@H]2[C@@H](O)c1ccnc2ccc(OC)cc12,,,,1,,train
CN(C)[C@H]1CCN(c2cc(-c3ccccc3)nc3ccnn23)C1,,,,1,,train
CC(C)[C@@H]1CC[C@@H](C)CC1=O,,,,1,,train
O[C@@H](CNCc1ccccc1)[C@H]1CCc2cc(F)ccc2O1,,,,1,,test
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3OC1CC(/N=c2/c(O)c(O)/c2=N\CCCCCC/N=c2\c(O)c(O)\c2=N\C2CC(O[C@H]3C[C@](O)(C(=O)CO)Cc4c(O)c5c(c(O)c43)C(=O)c3c(OC)cccc3C5=O)OC(C)C2O)C(O)C(C)O1,,,,1,,train
C[C@@H]1COc2ccccc2N1C(=O)C(Cl)Cl.C[C@H]1COc2ccccc2N1C(=O)C(Cl)Cl,,,,1,,train
CC1(C)[C@H]2[C@H](O)OC(=O)[C@H]21,,,,1,,train
O[C@@H]1C=C[C@H](O)[C@@]23O[C@@]12C1(Oc2cccc4cccc(c24)O1)[C@@H]1O[C@@H]1[C@@H]3O,,,,1,,train
COC1CC[C@]2(O1)[C@H]1C[C@H]1[C@H]1[C@@H]3C=CC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]12C,,,,1,,valid
CC(Cc1ccc(C(C)(C)C)cc1)CN1C[C@@H](C)O[C@@H](C)C1,,,,1,,train
CSCC[C@H](NC(=O)[C@H](CC(C)C)NC(=O)CNC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](Cc1ccccc1)NC(=O)[C@H](CCC(N)=O)NC(=O)[C@@H](CCC(N)=O)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](CCCCN)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](N)CCCN=C(N)N)C(N)=O,,,,1,,train
O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO.[Zn+2],,,,1,,train
CCCCCCCC/C=C\CCCCCCCC(=O)N[C@@H]1C[C@H]1O,,,,1,,train
CC1(C)[C@H]2CC[C@](C)(O)[C@@H]1C2,,,,1,,test
O=C(O)C[C@@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@@H](C(=O)O)[C@@H](C(=O)O)[C@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@H](C(=O)O)[C@@H](C(=O)O)[C@@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@H](C(=O)O)[C@@H](C(=O)O)[C@H](C(=O)O)P(=O)(O)O.O=C(O)C[C@H](C(=O)O)[C@H](C(=O)O)[C@@H](C(=O)O)P(=O)(O)O,,,,0,,train
C1CCC(NC2CCCCC2)CC1.CC(C)(O)c1ccccc1CC[C@@H](SCC1(CC(=O)O)CC1)c1cccc(/C=C/c2ccc3ccc(Cl)cc3n2)c1,,,,1,,train
Cc1ccc2c(c1)[C@@H](N)[C@@H](C)C2.Cc1ccc2c(c1)[C@@H](N)[C@H](C)C2.Cc1ccc2c(c1)[C@H](N)[C@@H](C)C2.Cc1ccc2c(c1)[C@H](N)[C@H](C)C2,,,,1,,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O.CCCCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O.OCCN(CCO)CCO.OCCN(CCO)CCO,,,,0,,train
COc1ccc(C(OC[C@H]2O[C@@H](n3ccc(NC(=O)c4ccccc4)nc3=O)C[C@H]2O)(c2ccccc2)c2ccc(OC)cc2)cc1,,,,1,,valid
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1C(=O)NCCc1ccccn1,,,,1,,train
CC1(C)COC2(CC[C@H]3[C@@H]4CCC5=CC6(CC[C@@H]5[C@H]4C(=O)C[C@@]32C)SCCS6)OC1,,,,1,,train
CC(CC(=O)C[C@@H](C)[C@H]1CC(=O)[C@@]2(C)C3=C(C(=O)[C@@H](O)[C@]12C)[C@@]1(C)CC[C@H](O)C(C)(C)[C@@H]1CC3=O)C(=O)O,,,,1,,train
C[C@@H]1C[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@]2(C)[C@H]1C(=O)CO,,,,1,,train
C=C1CCC(C)=CCCC=C(C)CC[C@@H]1C(C)=O.C=C1CCC(C)=CCCC=C(C)CC[C@H]1C(C)=O.CC(=O)/C1=C(\C)CC/C(C)=C\CC/C=C(/C)CC1.CC(=O)[C@@H]1CCC(C)=CCCC=C(C)CC=C1C.CC(=O)[C@H]1CCC(C)=CCCC=C(C)CC=C1C,,,,1,,test
CCCOC(=O)[C@H](C)O,,,,0,,train
CC1(C)CC[C@]2(C(=O)O)CC[C@]3(C)C(=CC[C@@H]4[C@@]5(C)CC[C@H](OC(=O)/C=C/c6ccc(O)cc6)C(C)(C)[C@@H]5CC[C@]43C)[C@@H]2C1,,,,1,,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C=CC4=CC(=O)C=C[C@]4(C)[C@H]3CC[C@@]21C,,,,1,,train
CC(C)=CCC/C(C)=C/CC[C@H](C)CCO,,,,1,,train
CCCCCCCCCCCCC/C=C/[C@@H](O)[C@H](CO)NC(=O)CCCCCCC,,,,1,,valid
CC(C)c1ccc2c(c1)CC[C@H]1[C@](C)(CN)CCC[C@]21C.Cc1nn(-c2cccc(Cl)c2)c(O)c1N=Nc1cc(S(C)(=O)=O)ccc1O.Cc1nn(-c2cccc(Cl)c2)c(O)c1N=Nc1cc(S(C)(=O)=O)ccc1O.[Co+3].[H+],,,,1,,train
CO[C@]12C[C@@H](CO)CN(C)[C@@H]1Cc1c[nH]c3cccc2c13,,,,1,,train
CC(c1ccc([C@@H](C)c2ccccc2)cc1)c1cc(C(=O)O)c(O)c(C(C)c2ccccc2)c1.CC(c1ccc([C@H](C)c2ccccc2)cc1)c1cc(C(=O)O)c(O)c(C(C)c2ccccc2)c1.CC(c1ccccc1)c1cc(C(=O)O)c(O)c(C(C)c2ccc([C@@H](C)c3ccccc3)cc2)c1.CC(c1ccccc1)c1cc(C(=O)O)c(O)c(C(C)c2ccc([C@H](C)c3ccccc3)cc2)c1.C[C@@H](c1ccccc1)c1cc(C(=O)O)c(O)c([C@@H](C)c2ccccc2)c1.C[C@@H](c1ccccc1)c1cc(C(=O)O)c(O)c([C@@H](C)c2ccccc2)c1.C[C@@H](c1ccccc1)c1ccc(O)c(C(=O)O)c1.C[C@@H](c1ccccc1)c1cccc(C(=O)O)c1O.C[C@H](c1ccccc1)c1cc(C(=O)O)c(O)c([C@H](C)c2ccccc2)c1.C[C@H](c1ccccc1)c1cc(C(=O)O)c(O)c([C@H](C)c2ccccc2)c1.C[C@H](c1ccccc1)c1ccc(O)c(C(=O)O)c1.C[C@H](c1ccccc1)c1cccc(C(=O)O)c1O.[Zn+2].[Zn+2].[Zn+2].[Zn+2].[Zn+2].[Zn+2],,,,1,,train
CC[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@H]43)[C@@H]1CCC2=O,,,,1,,train
CC(=O)C1=C(C)[C@@H]2C[C@@]3(C1)[C@H](C)CC[C@H]3C2(C)C,,,,1,,test
CC1=CC[C@@H](C)[C@H](C=O)C1.CC1=CC[C@@H](C=O)[C@H](C)C1,,,,1,,train
C=C(C)[C@H]1C=C[C@@](C)(O)CC1,,,,1,,train
CN[C@@H](C)c1cccc(O)c1,,,,0,,train
C=C1C=CC(C(C)CC(=O)C=C(C)C)CC1.CC(C)=CC(=O)CC(C)C1C=CC(C)=CC1.CC(C)=CC(=O)C[C@H](C)c1ccc(C)cc1,,,,1,,train
CCCCCCCCCCCC(=O)NC(N)=NCCC[C@H](N)C(=O)OCC.O=C1CC[C@@H](C(=O)O)N1,,,,1,,valid
C=C[C@H]1CN2CC[C@H]1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,,,,1,,train
COC1=CCC2=C(CC[C@@H]3[C@@H]2CC[C@]2(C)[C@@H](O)CC[C@@H]32)C1,,,,1,,train
CCOC(=O)[C@H](C)O,,,,0,,train
CCCCOP(=S)(S)OCCCC.CN1CCC[C@H]1c1cccnc1,,,,1,,train
Fc1ccc2c(c1)CC[C@H](C1CO1)O2,,,,1,,test
C[C@]12CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@H]43)[C@@H]1CCC2=O,,,,1,,train
CCCCCCCCCCCCCC[C@@H](O)[C@@H](O)[C@H](N)CO,,,,1,,train
CSCC[C@H](NC(=O)[C@H](CO)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@@H](N)CO)C(=O)N[C@@H](CCC(=O)O)C(=O)N[C@@H](Cc1c[nH]cn1)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)NCC(=O)N[C@@H](CCCCN)C(=O)N1CCC[C@H]1C(=O)N[C@H](C(=O)NCC(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCCN)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N1CCC[C@H]1C(=O)N[C@H](C(=O)N[C@@H](CCCCN)C(=O)N[C@H](C(=O)N[C@@H](Cc1ccc(O)cc1)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CC(=O)O)C(=O)N[C@@H](C)C(=O)NCC(=O)N[C@@H](CCC(=O)O)C(=O)O)C(C)C)C(C)C)C(C)C,,,,1,,train
CC1(C)[C@H]2[C@H](C(Br)(Br)Br)OC(=O)[C@H]21,,,,1,,train
CCC(O)CCCCCCC[C@H](O)[C@H]1CC[C@H]([C@H]2CC[C@H]([C@H](O)CCCCCCCCCCC3CC(CC(C)=O)C(=O)O3)O2)O1,,,,1,,valid
C[C@H](N)c1ccccc1,,,,1,,train
C[C@@H]1[C@@H](CN)C[C@H]2C[C@@H]1C2(C)C,,,,1,,train
CC(C)(C)NC(=O)[C@@H]1C[C@@H]2CCCC[C@@H]2CN1,,,,1,,train
CCc1ccc(C(=O)O[C@H]2C[C@H]3[C@](C)(COC(C)=O)[C@@H](OC(C)=O)CC[C@]3(C)[C@H]3[C@@H](O)c4c(cc(-c5cccnc5)oc4=O)O[C@]23C)cc1,,,,1,,train
C[C@]12CCC3=C4CCC(=O)C=C4CC[C@H]3[C@@H]1CC[C@@H]2O,,,,1,,test
C[C@]12CC[C@H]3[C@@H](C=CC4=CC(=O)CC[C@@H]43)[C@@H]1CC[C@@H]2O,,,,1,,train
C=C[C@H]1CN2CC[C@@H]1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,,,,1,,train
N[C@@H](CC[C@]1(O)CNC1=O)C(=O)O,,,,1,,train
C[C@]12C[C@@H](O)[C@H]3[C@@H](CCC4=CC(=O)CC[C@@H]43)[C@@H]1CCC2=O,,,,1,,train
COC(=O)[C@@H](C)Oc1ccc(O)cc1,,,,1,,valid
CC(C)/C(O)=C1/C(=O)O[C@@H](c2ccoc2)[C@]2(C)C(O)[C@@H](O)[C@@]34OC5(C)OC67CC(C)(C(O)[C@]6(O)[C@@H](O)[C@]3(O5)C12)[C@H](CC(=O)O)[C@]74C,,,,1,,train
CC(C)OC(=O)[C@H](C)OC(=O)C(C)(C)C,,,,0,,train
CCOC(=O)[C@]1(C)CCC[C@@]2(C)[C@H]1CC=C1C=C(C(C)C)CC[C@@H]12,,,,1,,train
CCCCC[C@@H]1C(=O)CC[C@H]1C(=O)OC,,,,1,,train
CCOC(=O)[C@H]1[C@H](C)CCCC1(C)C,,,,1,,test
CCCC(O)CC[C@@H]1[C@@H](C)CCCC1(C)C,,,,1,,train
CC1=CC[C@@H]2C[C@H]1C2(C)C,,,,1,,train
O=C1OC([C@@H](O)CO)C(=O)[C-]1O,,,,1,,train
CC(C)C(C)(C)[C@@H]1CCC(=O)[C@H](C)C1,,,,1,,train
CC(C)=CCC/C(C)=C/CC[C@H](C)CC=O,,,,1,,valid
CCCCCCCCCCCC(=O)N[C@@H](CCCNC(=N)N)C(=O)OCC,,,,1,,train
CC1(C)C[C@@H]1C(=O)N/C(=C\CCCCSC[C@H](N)C(=O)O)C(=O)O.C[C@@H](O)[C@H]1C(=O)N2C(C(=O)O)=C(SCC/N=C\N)C[C@H]12,,,,1,,train
C[C@@H](S)CC(=O)OCC(COC(=O)C[C@@H](C)S)(COC(=O)C[C@@H](C)S)COC(=O)C[C@@H](C)S.C[C@H](S)CC(=O)OCC(COC(=O)C[C@@H](C)S)(COC(=O)C[C@@H](C)S)COC(=O)C[C@@H](C)S.C[C@H](S)CC(=O)OCC(COC(=O)C[C@H](C)S)(COC(=O)C[C@@H](C)S)COC(=O)C[C@@H](C)S.C[C@H](S)CC(=O)OCC(COC(=O)C[C@H](C)S)(COC(=O)C[C@H](C)S)COC(=O)C[C@@H](C)S.C[C@H](S)CC(=O)OCC(COC(=O)C[C@H](C)S)(COC(=O)C[C@H](C)S)COC(=O)C[C@H](C)S,,,,1,,train
COC(=O)[C@]1(C)CCC[C@@]2(C)[C@H]1CC=C1[C@@H]2CC[C@](O)(C(C)C)[C@H]1O,,,,1,,train
CCOC(=O)NC(=O)[C@@H]1C[C@H](C)CC[C@H]1C(C)C,,,,1,,test
CC(C)[C@H]1CC[C@H](C)C[C@H]1O,,,,1,,train
C=C[C@@](C)(O)/C=C/C=C(\C)CCC=C(C)C,,,,1,,train
CCCCCCCCCCC/C=C/CCCCC(=O)N(C)C[C@H](C)[C@@H](C)[C@H](C)[C@H](C)CO.CCCCCCCCCCCCCCCCCC(=O)N(C)C[C@H](C)[C@@H](C)[C@H](C)[C@H](C)CO,,,,1,,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,,,,1,,train
COc1cc(Br)c2c3c1O[C@@H]1CC(=O)C=C[C@]31CCNC2,,,,1,,valid
C[C@H]1CN(C2CCN(c3nc(N)n[nH]3)CC2)[C@@H](Cc2ccc(Cl)cc2)CO1,,,,0,,train
CC1=CCC2CCCCC2C1N1C(=O)[C@@H]2[C@@H]3C[C@@H]4[C@@]5(C)CCC[C@@](C)(C(=O)O)[C@@H]5CC[C@]4(C=C3C(C)C)[C@@H]2C1=O,,,,0,,train
C[C@@H](CN)CCCCCCN.C[C@H](CN)CCCCCCN,,,,1,,train
Nc1nc(=O)c2c([nH]1)NCC(CNc1ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc1)N2,,,,0,,train
N[C@@H](CC(=O)O)C(=O)O.N[C@@H](CC(=O)O)C(=O)O.N[C@@H](CC(=O)O)C(=O)O.[Mg+2],,,,1,,test
C[C@]12CCC(O)CC1=CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,,,,1,,train
C=C1CC[C@H]2C[C@@H]1C2(C)C,,,,1,,train
C[N+](C)(C)CCCCC[C@H](N)C(=O)O,,,,1,,train
CNC[C@H](O)c1cccc(O)c1.[H+],,,,1,,train
C[C@]12CCC(=O)C=C1C=C[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@@]21CCC(=O)O1.Oc1c(Cl)c(Cl)c(O)c(Cl)c1Cl,,,,1,,valid
COc1ccc([C@@H](C)N)cc1,,,,1,,train
COc1ccc([C@H](C)N)cc1,,,,1,,train
CC1(C)[C@H]2CC=C(CCO)[C@@H]1C2,,,,1,,train
CC(C)CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=NO)CC[C@]4(C)[C@H]3CC[C@]12C,,,,1,,train
CC1(C)C2CC[C@]1(CS(=O)(=O)O)C(=O)C2.COC(=O)[C@H](c1ccccc1Cl)N1CCc2sccc2C1,,,,1,,test
CC1(C)[C@H]2CC[C@](C)(C2)[C@H]1O,,,,1,,train
CC1(C)COC2(CCC3=C(CC[C@@H]4[C@@H]3CC[C@@]3(C)[C@H]4CC[C@]3(O)C#N)C2)OC1,,,,1,,train
C[C@H](N)C(=O)N[C@H](CCC(N)=O)C(=O)O,,,,0,,train
CCOC(=O)[C@H]1C2CCC(CC2)[C@H]1N,,,,1,,train
CC(=O)O[C@H]1[C@@H](N2CCCC2)C[C@H]2[C@@H]3CC[C@H]4C[C@H](O)[C@@H](N5CCOCC5)C[C@]4(C)[C@H]3CC[C@@]21C,,,,1,,valid
Fc1ccc2c(c1)CC[C@@H](C1CO1)O2,,,,1,,train
C[C@H]1OC(=O)[C@@H](C)OC1=O,,,,0,,train
CCCCC(=O)N(Cc1ccc(-c2ccccc2-c2nn[nH]n2)cc1)[C@H](C(=O)OC)C(C)C,,,,1,,train
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]12,,,,1,,train
Fc1ccc2c(c1)CC[C@H]([C@H]1CO1)O2,,,,1,,test
CC(c1ncncc1F)C(Cn1cncn1)(OS(=O)(=O)C[C@@]12CCC(CC1=O)C2(C)C)c1ccc(F)cc1F,,,,1,,train
CC[C@]12CC[C@@H]3C4=C(CC[C@H]3[C@@H]1CC[C@@H]2O)CC(OC)=CC4,,,,1,,train
C=CCOC(=O)[C@@H]1[C@@H](C)CCCC1(C)C,,,,1,,train
CCCCCCCCCCCCCC[C@@H](O)[C@@H](O)[C@@H](N)CO,,,,1,,train
C[C@H]1O[C@H]1C(=O)O,,,,1,,valid
COc1ccnc(C(=O)N[C@H]2COC(=O)[C@H](Cc3ccccc3)[C@@H](OC(=O)C(C)C)[C@H](C)OC2=O)c1O,,,,1,,train
COC(=O)CC(C)(C)C[C@@H](C)C(=O)OC.COC(=O)CC(C)(C)C[C@H](C)C(=O)OC.COC(=O)C[C@@H](C)CC(C)(C)C(=O)OC.COC(=O)C[C@H](C)CC(C)(C)C(=O)OC,,,,0,,train
CC(=O)OCC[C@@H](C)CCC=C(C)C,,,,1,,train
COc1cccc([C@@H](C)N)c1,,,,1,,train
C[C@@H]1CCC2C(C)(C)C3CC21CC[C@@]3(C)OC(=O)Cc1ccncc1,,,,1,,test
CCOC(=O)[C@@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@@H]1CC2C=C[C@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@H]1C2,,,,1,,train
C[C@]12CCC(=O)C=C1CC[C@H]1[C@@H]3[C@@H](O)CC(=O)[C@@]3(C)CC[C@@H]12,,,,0,,train
CC(=O)[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C,,,,1,,train
CC[C@]1(CC=C2CCCc3cc(OC)ccc32)C(=O)CC[C@@H]1O,,,,1,,train
CC(=O)O[C@]1(C)CC[C@@]23C[C@@H]1C(C)(C)[C@@H]2CC[C@H]3C,,,,1,,valid
C[C@]12CCC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@@]2(O)C#CCO,,,,1,,train
CC[C@H](CC#N)OC(=O)N[C@@H](C)c1cccc(NC(=O)Nc2ccc(-c3cnco3)c(OC)c2)c1,,,,1,,train
CCCC[C@@H](NC(=O)[C@@H](N)CC)B(O)O,,,,1,,train
C1=C[C@H]2C[C@@H]1[C@H]1[C@H]3CC[C@H](C3)[C@H]12,,,,1,,train
CC1=CC[C@H]2C[C@@H]1C2(C)C,,,,1,,test
Fc1ccc2c(c1)CC[C@H]([C@@H]1CO1)O2,,,,1,,train
COc1cc(Br)c2c3c1O[C@@H]1CC(=O)C=C[C@]31CCN(C)C2,,,,1,,train
C[Si](C)(C)OC[C@H]1OC(=O)[C@H](O[Si](C)(C)C)[C@@H](O[Si](C)(C)C)[C@@H]1O[Si](C)(C)C,,,,1,,train
CCOC(=O)C(C[C@]1(CC)CCCN2CCc3c([nH]c4ccccc34)[C@@H]21)=NO,,,,1,,train
C[C@H]1C[C@@H](O)CC(C)(C)C1,,,,1,,valid
C[C@]12C=CC(=O)C=C1CC[C@@H]1[C@@H]2CC[C@]2(C)C(O)CC[C@@H]12,,,,1,,train
Nc1c(Br)cc(Br)cc1CN[C@H]1CC[C@H](O)CC1,,,,1,,train
Clc1ccc(I)cc1Cc1ccc(O[C@H]2CCOC2)cc1,,,,1,,train
COc1cccc([C@H](C)N)c1,,,,1,,train
COc1cc2c(cc1O)CC[C@@H]1[C@@H]2CC[C@]2(C)[C@@H](O)CC[C@@H]12,,,,1,,test
CCOc1ccc(C[C@@H](CNCCN)NC(=O)OCc2ccccc2)cc1,,,,1,,train
O=[N+]([O-])O[C@H]1CO[C@H]2[C@@H]1OC[C@H]2O[N+](=O)[O-],,,,1,,train
CN[C@@H](C)c1cccc(OC)c1,,,,0,,train
C[C@H]1C(C)(C)C2=C(c3ncncc3CC2)C1(C)C,,,,1,,train
C[C@@H]1O[C@@H]1P(=O)(O)O.C[C@H](N)c1ccccc1.C[C@H](N)c1ccccc1,,,,1,,valid
O=C[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,,,,1,,train
CC(C)COC(=O)[C@@H]1[C@@H](C(=O)OCC(C)C)[C@@H](C)CC[C@H]1C.CC(C)COC(=O)[C@H]1[C@H](C(=O)OCC(C)C)[C@@H](C)CC[C@H]1C,,,,1,,train
CC(C)c1ccc2c(c1)CCC1C(C)(CN)CCCC21C.CC(C)c1ccc2c(c1)CC[C@@H]1[C@](C)(CN)CCC[C@@]21C,,,,1,,train
CC(C)(C)OC(=O)N[C@@H](CO)C(=O)O,,,,0,,train
CC(=O)O[C@H]1CC(=O)[C@@]2(C)CC[C@H]3[C@@H](CCC4=CC(=O)CC[C@@]43C)[C@H]12,,,,1,,test
CC1(C)[C@@H]2CC[C@@]1(C)C(=O)C2,,,,1,,train
C=C1C[C@]2(CC)C(=O)CC[C@H]2[C@@H]2CCC3=CC(=O)CC[C@@H]3[C@@H]12,,,,1,,train
CC(C)C[C@H](N)c1ccccc1N1CCCCC1,,,,1,,train
CC1=CC[C@H]([C@@H](C)CCO)CC1.CC1=CC[C@H]([C@H](C)CCO)CC1,,,,1,,train
C/C(=C\c1csc(C)n1)[C@H]1OC(=O)C[C@H](O)C(C)(C)C(=O)[C@H](C)[C@@H](O)[C@@H](C)CCC/C=C\[C@@H]1O,,,,1,,valid
CCCCCCOC(C)c1c(C)c2cc3nc(c(CC(=O)N[C@@H](CCC(=O)O)C(=O)O)c4[nH]c(cc5nc(cc1[nH]2)C(C)=C5CC)c(C)c4C(=O)O)[C@@H](CCC(=O)O)[C@@H]3C,,,,1,,train
N[C@@H](CSS(=O)(=O)O)C(=O)O,,,,1,,train
COC1(OC)CCC2=C(CC[C@@H]3[C@@H]2CC[C@]2(C)C(=O)CC[C@@H]32)C1,,,,1,,train
O=C(O)[C@H]1[C@H](C(=O)NCCO)[C@H]2C=C[C@H]1C2,,,,1,,train
CC(=O)O[C@]1(C(C)=O)CC[C@H]2[C@@H]3C[C@H](C)C4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C.C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1CC[C@@H]2O,,,,1,,test
CCNC(=O)C(=O)O[C@@H]1C[C@H](C)CC[C@H]1C(C)C,,,,1,,train
CC(C)[C@H]1OCC[C@]2(C[C@H]3CC[C@]2(C)C3(C)C)O1,,,,1,,train
C[C@H](O)C(=O)O.C[C@H](O)C(=O)O.[Ca+2],,,,0,,train
CC1(C)CC[C@H]2O[C@]23C(C)(C)[C@H]2CC[C@@]13C2,,,,1,,train
C1=C[C@H]2C[C@@H]1[C@H]1Cc3ccccc3[C@H]12,,,,1,,valid
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1OC(=O)OCCO,,,,1,,train
CCCN[C@H]1CCc2c(cccc2OC)C1.O=C(O)[C@H](O)c1ccccc1Cl,,,,1,,train
CN[C@@H](C)[C@@H](O)c1ccccc1,,,,1,,train
CC(C)[C@@H](N)C(=O)O,,,,1,,train
CC(O)[C@H](N)C(=O)O,,,,0,,test
CN(Cc1ccccc1)C[C@@H](O)c1cccc(O)c1,,,,1,,train
C=C(C)C(=O)NCCC[N+](C)(C)CC(=O)NCCC[N+](C)(C)C[C@@H](O)C[N+](C)(C)C.C=C(C)C(=O)NCCC[N+](C)(C)CC(=O)NCCC[N+](C)(C)C[C@H](O)C[N+](C)(C)C,,,,0,,train
COC(=O)OC1C/C=C\CCCC1.COC(=O)OC1C2CCCC1CC2.COC(=O)OC1CC/C=C\CCC1.COC(=O)O[C@@H]1CC[C@@H]2CCC[C@@H]21.COC(=O)O[C@H]1CC[C@@H]2CCC[C@@H]21,,,,1,,train
NC[C@](O)(c1ccc(F)cc1F)C(F)(F)c1ccc(-c2ccc(OCC(F)(F)F)cc2)cn1,,,,1,,train
CCC(C)C(N)C1=NCC(C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](CCC(=O)O)C(=O)N[C@H](C(=O)N[C@H]2CCCCNC(=O)[C@H](CC(N)=O)NC(=O)[C@@H](CC(=O)O)NC(=O)[C@H](Cc3cnc[nH]3)NC(=O)[C@@H](Cc3ccccc3)NC(=O)[C@H]([C@@H](C)CC)NC(=O)[C@@H](CCCN)NC2=O)[C@@H](C)CC)S1.[Zn+2],,,,1,,valid
CCCCCC[C@@H](O)C/C=C\CCCCCCCC(=O)NCCCN(C)C,,,,1,,train
N[C@@H]1CC2OC1c1ccccc12,,,,1,,train
CC1=CC[C@H](C(C)C)C=C1,,,,1,,train
O=C(O)C(=O)[C@@H](O)[C@H](O)[C@H](O)CO.O=C(O)C(=O)[C@@H](O)[C@H](O)[C@H](O)CO.[Ca+2],,,,0,,train
Nc1nc2c(ncn2[C@@H]2O[C@H](CO)[C@@H](O)[C@H]2O)c(=O)[nH]1,,,,,1,test
CNc1ncnc2c1ncn2[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,,,,,1,train
c1cc[n+]([C@]23C[C@H]4C[C@H](C[C@H](C4)C2)C3)cc1,,,,,1,train
C[C@@H](Cl)C(=O)O,,,,,1,train
NCCC(=O)N[C@@H](Cc1c[nH]cn1)C(=O)O,,,,,1,train
NC(=O)NCCC[C@H](N)C(=O)O,,,,,1,valid
CC(=O)C1C[C@H]2C=C[C@@H]1C2,,,,,1,train
O=C(N[C@H](CO)[C@H](O)c1ccc([N+](=O)[O-])cc1)C(Cl)Cl,,,,,1,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](COP(=O)(O)OP(=O)(O)O)[C@@H](O)[C@H]1O,,,,,1,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)ccc(Cl)c4[C@@](C)(O)C3C[C@@H]12,,,,,1,train
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,,,,,1,test
Nc1ncnc2c1ncn2[C@H]1C[C@H](O)[C@@H](COP(=O)(O)O)O1,,,,,1,train
C[C@@H](Cc1ccc(C(C)(C)C)cc1)CN1CCCCC1,,,,,1,train
O=C1[C@H]2C[C@@H]3C[C@@H](C[C@H]1C3)C2,,,,,1,train
N[C@@H](Cc1c[nH]cn1)C(=O)O,,,,,1,train
Cc1cc2nc3c(=O)[nH]c(=O)nc-3n(C[C@H](O)[C@H](O)[C@H](O)CO)c2cc1C,,,,,1,valid
CC(=O)N[C@@H](CC(=O)O)C(=O)O,,,,,1,train
CNC(=O)Oc1ccc2c(c1)[C@]1(C)CCN(C)[C@@H]1N2C,,,,,1,train
C[C@@H](Cc1cccc(CC(=O)NCc2ccc(N(C)C(=O)CCN3CCC(OC(=O)Nc4ccccc4-c4ccccc4)CC3)cc2)c1)NC[C@H](O)c1ccc(O)c2[nH]c(=O)ccc12,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCNC(=N)N)NC1=O,,,,,1,train
N=C(N)Nc1ccc(CNC(=O)N2CCN(C(=O)O[C@H]3CCC[C@@H](OC(=O)N4CCN(C(=O)NCc5ccccc5)CC4)CCC3)CC2)cc1,,,,,1,test
Cc1nc(/C=C(\CO)[C@@H]2C/C=C\CCC[C@H](C)[C@H](O)[C@@H](C)C(=O)C(C)(C)[C@@H](O)CC(=O)O2)cs1,,,,,1,train
Nc1ncnc2c1ncn2[C@@H]1O[C@H](COP(=O)(S)S)[C@@H](O)[C@H]1O,,,,,1,train
CC1(C)O[C@@H]2CC3C4CCC5=CC(=O)C=C[C@]5(C)[C@@]4(F)[C@@H](O)C[C@]3(C)[C@]2(C(=O)CO)O1.CO[C@@]12[C@H](COC(N)=O)C3=C(C(=O)C(C)=C(N)C3=O)N1C[C@@H]1N[C@@H]12,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCNC(N)=O)NC1=O,,,,,1,train
CC(CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3[C@H](O)C[C@H]4C[C@@H](NCCCNCCCCNCCCN)CC[C@]4(C)[C@H]3CC[C@]12C)C(=O)O,,,,,1,valid
CNC(=O)O[C@H]1CCC(n2cc(C(N)=O)c(Nc3ccc(F)cc3)n2)[C@@H](C#N)C1,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CC(N)=O)NC1=O,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)C1CCN1C(C)(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,1,train
OC[C@@H](O)[C@@H](O)[C@H](O)[C@@H](O)C(c1c[nH]c2ccccc12)c1c[nH]c2ccccc12,,,,,1,train
COc1cccc2c1C(=N)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(CCO)C[C@@H]3O[C@H]1C[C@H](N)[C@H](O)[C@H](C)O1,,,,,1,test
CC(C)c1cc2c(cc1S(=O)(=O)O)[C@@]1(C)CCC[C@@](C)(C(=O)O)[C@@H]1CC2,,,,,1,train
N[C@@H](C(=O)N1CCCC1)[C@@H](O)c1ccncc1,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)C1CC1,,,,,1,train
CN1CCN(C(=O)Cc2ccc(Cl)c(Cl)c2)[C@H]2[C@@H]1CCC[C@@H]2N1CCCC1,,,,,1,train
C[C@H]1O[C@@H](c2ccccc2)OC[C@H]1NC(=O)CN,,,,,0,valid
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CCC,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCN)NC1=O,,,,,1,train
O=C1O[C@H](O)[C@@H]2[C@H]1[C@H]1CC[C@@H]2O1,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C1CCCCC1)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,0,train
CN(C(=O)c1ccc(OCC2CC2)cc1)[C@@H]1Cc2ccc(CN3CCCCCC3)cc2C1,,,,,1,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,1,train
CC(C)/C(O)=C1/C(=O)O[C@@H](c2ccoc2)[C@]2(C)C(O)[C@@H](O)[C@@]34OC5(C)OC67CC(C)(C(O)[C@]6(O)[C@@H](O)[C@]3(O5)C12)[C@H](CC(=O)O)[C@]74C,,,,,1,train
N[C@@H](CCC(=O)N[C@@H](CSc1c(Br)c(Br)c2c(c1Br)C(=O)OC2(c1ccc(O)c(S(=O)(=O)O)c1)c1ccc(O)c(S(=O)(=O)O)c1)C(=O)NCC(=O)O)C(=O)O,,,,,1,train
CN(C)CCC[C@@]1(c2ccc(F)cc2)OCc2cc(C#N)ccc21,,,,,1,train
CCN1CCN(C(=O)Cc2ccc(Cl)c(Cl)c2)[C@H]2[C@@H]1CCC[C@@H]2N1CCCC1,,,,,1,valid
O=C1N[C@@H]2C(=C[C@H](O)[C@H]3OP(=O)(O)O[C@H]32)c2cc3c(cc21)OCO3,,,,,1,train
CN(C)[C@]12C(=O)C(c3ccccc3)=C(c3ccccc3)[C@H]1C1CCC2C1,,,,,1,train
N[C@@H](CCC(=O)C[C@@H](CSSC[C@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)C(=O)NCC(=O)O)C(=O)O,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)C(NC(=O)[C@@H]1CCCCN1CC(F)F)C1CCCCC1)C1CCOCC1)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,0,train
N[C@@H](CCC(=O)O)C(=O)O,,,,,1,test
CC[C@H](C)[C@H](NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@H](Cc1c[nH]c2ccccc12)NC(=O)CNC(=O)[C@@H](N)Cc1ccc(O)cc1)C(=O)N[C@@H](CCCN=C(N)N)C(=O)N1CCC[C@H]1C(=O)N[C@@H](CCCCN)C(N)=O,,,,,1,train
N#C[C@@H]1CCCC[C@H]1n1cc(C(N)=O)c(Nc2ccc(S(=O)(=O)C(F)F)cc2)n1,,,,,1,train
CC(=O)OC[C@H]1O[C@@H](NS(N)(=O)=O)C[C@@H](OC(C)=O)[C@@H]1OC(C)=O,,,,,1,train
Fc1c(Cl)cc([C@]2(C(F)(F)F)CC(c3ccc4c(c3)COC43CNC3)=NO2)cc1Cl.O=S(=O)(O)c1ccccc1,,,,,1,train
O=c1nc(NO)ccn1[C@@H]1O[C@H](CO)[C@@H](O)[C@H]1O,,,,,1,valid
Oc1ccc(O)c2c1[C@@H](O)[C@H]1O[C@H]1C21Oc2cccc3cccc(c23)O1,,,,,1,train
CCCCCCCCCCCC(=O)NCCCC[C@H](N)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(N)=O,,,,,0,train
CCCCCCCCCCCC(=O)OC[C@H](O)[C@@H](O)[C@H](O)[C@H](O)CO,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@](C)(Cc2ccccc2)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,,,,,1,train
O[C@@H]1CO[C@H]2[C@@H]1OC[C@@H]2O,,,,,0,test
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CC(N)=O)NC1=O,,,,,1,train
O=C(O)[C@H](O)[C@@H](O)[C@H](O)[C@H](O)C(O)CO,,,,,1,train
CCCCCCCCCCCCCCCC(=O)N(C)[C@H](CO)C(=O)N[C@H](C)C(=O)NCC(=O)N(C)[C@@H]1C(=O)N[C@@H](C)C(=O)N[C@H](C(=O)O)Cc2ccc(O)c(c2)-c2cc1ccc2O,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)c1cccnc1N(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCCNC(=O)CC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,,,,,1,valid
CC(C)[C@]12CC[C@](C)(O1)C1CC[C@](C)(S(=O)(=O)O)C1[C@@H]2O,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](Cc2c[nH]cn2)NC1=O,,,,,1,train
NCCCC[C@H](N)C(=O)N1CCSC1,,,,,1,train
CNc1ncnc2c1ncn2C1O[C@H](CO)[C@H](O[C@@H]2OC(CO)[C@H](OP(=O)(O)O)C(OP(=O)(O)O)[C@@H]2O)[C@@H]1OP(=O)(O)O,,,,,1,train
CC[C@]1(O)C[C@@H]2C[C@H](c3cc4c(cc3OC)N(C)[C@H]3[C@@](O)(C(N)=O)[C@H](O)[C@]5(CC)C=CCN6CC[C@]43[C@@H]65)c3[nH]c4ccccc4c3CCN(C2)C1,,,,,1,test
CC[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,1,train
CCOP(=O)(O)N[C@@H](CC(C)C)C(=O)O,,,,,1,train
C=C(C)[C@H]1CC=C(C)CC1,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC(F)F,,,,,1,train
Cn1c(COc2ccc(CC3SC(=O)N([C@@H]4O[C@H](C(=O)O)[C@@H](O)[C@H](O)[C@H]4O)C3=O)cc2)nc2ccc(O)cc21,,,,,1,valid
COc1cccc2c1C(=O)c1c(O)c3c(c(O)c1C2=O)C[C@@](O)(C(=O)CO)C[C@@H]3OC1CC(/N=c2/c(O)c(O)/c2=N\CCCCCC/N=c2\c(O)c(O)\c2=N\C2CC(O[C@H]3C[C@](O)(C(=O)CO)Cc4c(O)c5c(c(O)c43)C(=O)c3c(OC)cccc3C5=O)OC(C)C2O)C(O)C(C)O1,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CC[C@H](F)C1,,,,,1,train
C[C@H](O)CCO,,,,,1,train
CSCC[C@H](NC(=O)[C@H](CC(C)C)NC(=O)CNC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](Cc1ccccc1)NC(=O)[C@H](CCC(N)=O)NC(=O)[C@@H](CCC(N)=O)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](CCCCN)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](N)CCCN=C(N)N)C(N)=O,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)NC1CCC1,,,,,1,test
N=C(N)NCCC[C@@H]1NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCC(N)=O)NC(=O)[C@@H](NC(=O)[C@@H](N)CCCNC(=N)N)CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC1=O,,,,,1,train
CC(C)(O)[C@@H]1CC[C@@H](Nc2ccc3ncc(-c4cccc(Cl)c4)n3n2)C1,,,,,1,train
CCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O.CCCCCCCCCCCCCC(=O)N[C@@H](CCC(=O)O)C(=O)O.OCCN(CCO)CCO.OCCN(CCO)CCO,,,,,1,train
OC[C@H]1NC[C@H](O)[C@H]1O,,,,,1,train
CC(CC(=O)C[C@@H](C)[C@H]1CC(=O)[C@@]2(C)C3=C(C(=O)[C@@H](O)[C@]12C)[C@@]1(C)CC[C@H](O)C(C)(C)[C@@H]1CC3=O)C(=O)O,,,,,1,valid
CC(C)=CCC/C(C)=C/CC[C@H](C)CCO,,,,,1,train
CC[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)C1CC1,,,,,1,train
CC/C=C\C[C@H]1C(=O)C=C[C@H]1CC(=O)OCCO,,,,,1,train
O=C1C[C@@H](c2ccc(O)cc2)[C@]2(O)C(=O)O[C@@H]([C@H](O)CO)[C@H]2O1,,,,,1,train
CC(C)[C@@H]1CC[C@@H](C)C[C@H]1O,,,,,1,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC,,,,,1,train
CN1CC[C@@H](c2c(O)cc(O)c3c(=O)cc(-c4ccccc4Cl)oc23)[C@@H]1CO,,,,,1,train
CCCCCCCCCCCCCC[C@@H](O)[C@@H](O)[C@H](N)CO,,,,,0,train
Nc1ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc1,,,,,1,train
C=C/C=C\C(C)C(OC(N)=O)C(C)C(O)C(C)/C=C(\C)C1C(C)C(O)C(C)C1/C=C/C[C@@H]1OC(=O)[C@H](C)[C@@H](O)[C@@H]1C,,,,,1,valid
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)OC,,,,,1,train
O=C(OC[C@H]1OC(OC(=O)c2ccc(O)c(O)c2O)[C@H](OC(=O)c2ccc(O)c(O)c2O)[C@@H](OC(=O)c2ccc(O)c(O)c2O)[C@@H]1OC(=O)c1ccc(O)c(O)c1O)c1ccc(O)c(O)c1O,,,,,1,train
CC(=O)O[C@@]12CO[C@@H]1C[C@H](O)[C@@]1(C)C(=O)[C@H](OC(=O)N[C@@H](C)C(=O)O)C3=C(C)[C@@H](OC(=O)[C@H](O)[C@@H](NC(=O)c4ccccc4)c4ccccc4)C[C@@](O)([C@@H](OC(=O)c4ccccc4)[C@H]21)C3(C)C,,,,,0,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCOCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,1,train
CCCCCCCCCCCCCCCC(=O)N[C@@H](CCC(=O)NCCCC[C@H](NC(=O)[C@H](C)NC(=O)[C@H](C)NC(=O)[C@H](CCC(N)=O)NC(=O)CNC(=O)[C@H](CCC(=O)O)NC(=O)[C@H](CC(C)C)NC(=O)[C@H](Cc1ccc(O)cc1)NC(=O)[C@H](CO)NC(=O)[C@H](CO)NC(=O)[C@@H](NC(=O)[C@H](CC(=O)O)NC(=O)[C@H](CO)NC(=O)[C@@H](NC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](NC(=O)CNC(=O)[C@H](CCC(=O)O)NC(=O)[C@H](C)NC(=O)[C@@H](N)Cc1cnc[nH]1)[C@@H](C)O)[C@@H](C)O)C(C)C)C(=O)N[C@@H](CCC(=O)O)C(=O)N[C@@H](Cc1ccccc1)C(=O)N[C@H](C(=O)N[C@@H](C)C(=O)N[C@@H](Cc1c[nH]c2ccccc12)C(=O)N[C@@H](CC(C)C)C(=O)N[C@H](C(=O)N[C@@H](CCCNC(=N)N)C(=O)NCC(=O)N[C@@H](CCCNC(=N)N)C(=O)NCC(=O)O)C(C)C)[C@@H](C)CC)C(=O)O,,,,,1,test
C=C1CCC[C@]2(C)[C@H]3CC4=C(O[C@]3(C)CC[C@@H]12)C(=O)C=C(OCC)C4=O,,,,,1,train
CCCC[C@H](N)C(=O)O,,,,,1,train
CC(C)[C@@H]1OCCN1CCO.CC(C)[C@@H]1OCCN1CCOC(=O)OCCN1CCO[C@H]1C(C)C.CC(C)[C@H]1OCCN1CCO.CC(C)[C@H]1OCCN1CCOC(=O)OCCN1CCO[C@H]1C(C)C.COC(=O)OCCN1CCO[C@@H]1C(C)C.COC(=O)OCCN1CCO[C@H]1C(C)C,,,,,1,train
CNC(=O)[C@H](Cc1ccc(OC)cc1)NC(=O)C1(CC(=O)NO)CCCCC1,,,,,1,train
N=C(N)NCCC[C@H](N)C(=O)O.N[C@@H](CCC(=O)O)C(=O)O,,,,,1,valid
C[C@@]1(c2cc(NC(=O)c3cnc(NS(C)(=O)=O)cn3)ccc2F)C=CSC(N)=N1,,,,,1,train
O=C1O[C@H](CO)[C@@H](O)[C@H](O)[C@H]1O,,,,,1,train
N#C[C@@H]1CCCCC1n1cc(C(N)=O)c(Nc2ccc(S(=O)(=O)CC(F)(F)F)cc2)n1,,,,,1,train
C[C@H](c1ccccc1)c1cccc([C@@H](C)c2ccccc2)c1O,,,,,1,train
COc1cc(Br)c2c3c1O[C@@H]1CC(=O)C=C[C@]31CCNC2,,,,,1,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCN1C(C)C)C1CCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,1,train
CC1=CCC2CCCCC2C1N1C(=O)[C@@H]2[C@@H]3C[C@@H]4[C@@]5(C)CCC[C@@](C)(C(=O)O)[C@@H]5CC[C@]4(C=C3C(C)C)[C@@H]2C1=O,,,,,1,train
CO[C@H]1/C=C\C=C(/C)C(=O)NC2=CC(=O)C(NCCCCCCNC(=O)c3ccccn3)=C(C[C@@H](C)C[C@H](OC)[C@H](O)[C@@H](C)/C=C(\C)[C@@H]1OC(N)=O)C2=O,,,,,1,train
C[C@H](N)C(=O)N[C@@H](C)C(=O)N[C@H]1[C@@H]2CN(c3nc4c(cc3F)c(=O)c(C(=O)O)cn4-c3ccc(F)cc3F)C[C@@H]21,,,,,1,train
C=CC(=O)OCCOC(=O)NCCC(C)(C)CC(C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCCC(C)(C)C[C@@H](C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCCC(C)(C)C[C@H](C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCCC(C)CC(C)(C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCC[C@@H](C)CC(C)(C)CNC(=O)OCCOC(=O)C=C.C=CC(=O)OCCOC(=O)NCC[C@H](C)CC(C)(C)CNC(=O)OCCOC(=O)C=C,,,,,1,valid
C[C@]12CCC(=O)C=C1C=C[C@@H]1[C@@H]2CC[C@@]2(C)[C@H]1CC[C@@]21CCC(=O)O1.Oc1c(Cl)c(Cl)c(O)c(Cl)c1Cl,,,,,1,train
CC1(C)[C@H]2CC[C@@]3(CCc4c(O)c(C=O)c(O)c(C=O)c4O3)[C@@H]1C2,,,,,1,train
CC(C)CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3CCC4=CC(=NO)CC[C@]4(C)[C@H]3CC[C@]12C,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](Cc2cccc(C(N)=O)c2)NC1=O,,,,,1,train
CCCCC/C=C\C/C=C\C=C\C=C\[C@@H](SC[C@@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)[C@@H](O)CCCC(=O)O,,,,,1,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)CC1(CC#N)CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,1,train
COc1cc(OC(F)(F)F)ccc1Cn1c([C@H]2CCCC[C@H]2C(=O)O)nc2ccc(OCc3ccn(C)n3)cc21,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CC[C@@H](F)C1,,,,,1,train
COC(=O)CC(C)(C)C[C@@H](C)C(=O)OC.COC(=O)CC(C)(C)C[C@H](C)C(=O)OC.COC(=O)C[C@@H](C)CC(C)(C)C(=O)OC.COC(=O)C[C@H](C)CC(C)(C)C(=O)OC,,,,,0,train
C[C@@]1(c2cc(NC(=O)c3cnc(-n4ccnn4)cn3)ccc2F)C=CSC(N)=N1,,,,,0,valid
O=C(CCc1ccc(CN[C@H](C(=O)OC2CCCC2)C2CCCCC2)cn1)NO,,,,,0,train
CCOC(=O)[C@@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@@H]1CC2C=C[C@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@H]1C2,,,,,1,train
O=[N+]([O-])c1ccc(OP(=O)(O)O[C@@H]2[C@H](O)[C@H](O)[C@@H](OP(=O)(O)O)[C@H](OP(=O)(O)O)[C@H]2O)cc1,,,,,1,train
CC(=O)O[C@]1(C)CC[C@@]23C[C@@H]1C(C)(C)[C@@H]2CC[C@H]3C,,,,,1,train
Cc1ncoc1C(=O)Nc1n[nH]c2c1CN(C(=O)N1C[C@@H](C)N(C)C[C@@H]1C)C2(C)C,,,,,1,test
C[C@H]1CO[C@]2(C)Oc3c(c4c(c5c3C[C@@H]3[C@@H](C)CO[C@]3(C)O5)C[C@@H]3[C@@H](C)CO[C@]3(C)O4)C[C@H]12,,,,,1,train
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCCN)NC1=O,,,,,1,train
Cc1ccccc1NC(=O)Nc1ccc(CC(=O)N(C)[C@@H](CC(C)C)C(=O)NCC[C@H](NC(=O)[C@@H]2CCCN2S(=O)(=O)c2cc(Cl)cc(Cl)c2)C(=O)O)cc1,,,,,1,train
C[N+](C)(C)CCCCC[C@H](N)C(=O)O,,,,,1,train
CC(C)c1ccc2c(c1)CCC1C(C)(CN)CCCC21C.CC(C)c1ccc2c(c1)CC[C@@H]1[C@](C)(CN)CCC[C@@]21C,,,,,1,valid
CCCCCCCCO[C@H]1O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]1O[C@H]1O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]2O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]3O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](O[C@H]4O[C@H](COC(C)=O)[C@@H](OC(C)=O)[C@H](OC(C)=O)[C@@H]4OC(C)=O)[C@@H]3OC(C)=O)[C@@H]2OC(C)=O)[C@@H]1OC(C)=O,,,,,0,train
CC1=CC[C@H]([C@@H](C)CCO)CC1.CC1=CC[C@H]([C@H](C)CCO)CC1,,,,,1,train
C/C(=C\c1csc(C)n1)[C@H]1OC(=O)C[C@H](O)C(C)(C)C(=O)[C@H](C)[C@@H](O)[C@@H](C)CCC/C=C\[C@@H]1O,,,,,1,train
N[C@@H](CSS(=O)(=O)O)C(=O)O,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)C1C[C@@]2(CN1C(=O)[C@@H](NC(=O)c1cccc(C(=O)NCCC)c1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,,,,,1,test
O=C(O)CCC(=O)NCCCC[C@H](NC(=O)CCC(=O)O)C(=O)O,,,,,0,train
CC1(C)[C@H]2CC[C@]1(C)[C@H](O)C2,,,,,1,train
ClC[C@H]1CO1,,,,,1,train
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC,,,,,1,train
CCCC[C@H]1CO[C@H](c2ccc(Br)c(F)c2)OC1,,,,,1,valid
CC1=C[C@H]2O[C@@H]3C[C@H]4OC(=O)/C=C\C=C\[C@H]([C@H](C)O)OCC[C@@H](C)[C@H](O)C(=O)OC[C@@]2(CC1)[C@]4(C)[C@]31CO1,,,,,1,train
CCOC(=O)[C@]1(C)CCC[C@@]2(C)[C@H]1CC=C1C=C(C(C)C)CC[C@@H]12,,,,,1,train
O=C1CC[C@H]2C[C@]34CN5CCCC/C=C\CC[C@](O)(C=C(c6nccc7c6[nH]c6c(O)cccc67)[C@@H]3CC5)[C@@H]4N2CC/C1=C/c1ccc(Br)cc1,,,,,1,train
CC(C)(O)[C@H]1CC[C@H](Nc2ccc3ncc(-c4cccc(F)c4)n3n2)CC1,,,,,1,train
CC1(C)[C@H](/C=C(\Cl)C(F)(F)F)[C@@H]1C(=O)O[C@@H](C#N)c1cccc(Oc2ccccc2)c1,,,,,1,test
CC(=O)N[C@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCC(N)=O)NC1=O,,,,,1,train
COc1cc(/C=C/C(=O)O[C@H]2[C@H](O[C@@H]3O[C@@H](C)[C@H](O)[C@@H](O)[C@H]3O)[C@H]3O[C@@H](c4ccc(O)c(O)c4)CO[C@@H]3O[C@@H]2CO)ccc1O,,,,,1,train
COC1[C@H](NC(=O)CC2OC(COCc3ccccc3)C(OCc3ccccc3)C(OCc3ccccc3)C2OCc2ccccc2)CC[C@H]2[C@H]3Cc4ccc(O)cc4[C@@]12CCN3C,,,,,1,train
C[C@H]1O[C@H]1C(=O)O,,,,,1,train
Cc1oc(-c2ccccc2)nc1CCCC[C@H]1CO[C@](C)(C(=O)O)OC1,,,,,1,valid
OC[C@@H]1[C@@H](O)[C@H](O)C[S+]1C[C@@H](O)[C@@H](O)[C@H](O)[C@H](O)[C@@H](O)CO,,,,,0,train
